module.exports = {
  extends: [require.resolve('@umijs/lint/dist/config/eslint')],
  globals: {
    page: true,
    REACT_APP_ENV: true,
  },
  parserOptions: {
    ecmaVersion: 2020,
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true, // 启用 JSX 解析
    },
  },
  plugins: ['import'],
  rules: {
    'react-hooks/rules-of-hooks': 'warn',
    'import/newline-after-import': ['error', { count: 1 }],
    'react/react-in-jsx-scope': 'error', // React 17 之后不需要引入 React，但试飞依旧检查该项
    '@typescript-eslint/no-unused-expressions': 'off', // 关闭不必要的表达式检查
    'no-empty-pattern': 'error',
    'react/no-unused-prop-types': 'error',
    'react/jsx-curly-brace-presence': ['error', { props: 'never', children: 'never' }],
  },
};
