@import './assets/styles/scroll.css';

@primaryColor: #3164f6;

@font-face {
  font-family: DINAlternate-Bold;
  src: url(./assets/font/DIN_Alternate_Bold.ttf);
}

@font-face {
  font-family: DIN_Alternate_Bold;
  src: url(./assets/font/DIN_Alternate_Bold.ttf);
}

html,
body,
#root {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
}
.ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {
  left: unset;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

@media (max-width: 768px) {
  .ant-table {
    width: 100%;
    overflow-x: auto;
    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;
        > span {
          display: block;
        }
      }
    }
  }
}

.ant-layout {
  background: #fff !important;
  background-color: #fff !important;
}

.ant-pro-layout .ant-layout-sider.ant-pro-sider {
  padding-top: 10px;
  background: #18223a;
}

.ant-layout-header {
  color: #fff;
  font-weight: 400;
  font-size: 24px;
}

.ant-layout-header {
  height: 60px !important;
  padding: 0 !important;
  line-height: 60px !important;
  background: url(./assets/images/top_beijing.png) left no-repeat !important;

  .ant-pro-global-header {
    height: 60px;
  }

  .ant-pro-global-header-logo {
    h1 {
      color: white;
    }
  }

  .ant-pro-global-header {
    padding: 0 13px;
    background: transparent;
    > * {
      height: unset;
    }
  }
}

.ant-menu-light .ant-menu-item,
:where(.css-dev-only-do-not-override-patp1).ant-menu-light > .ant-menu .ant-menu-item,
:where(.css-dev-only-do-not-override-patp1).ant-menu-light .ant-menu-submenu-title,
:where(.css-dev-only-do-not-override-patp1).ant-menu-light > .ant-menu .ant-menu-submenu-title {
  color: #94a1ad;
}

.ant-pro-layout .ant-pro-sider .ant-menu .ant-menu-item:hover {
  color: #fff !important;
}

.ant-menu-light .ant-menu-item-selected,
:where(.css-dev-only-do-not-override-patp1).ant-menu-light > .ant-menu .ant-menu-item-selected {
  color: #fff;
  background: @primaryColor;
}

.ant-menu-light .ant-menu-submenu-selected > .ant-menu-submenu-title {
  color: #fff;
}

.ant-menu-light .ant-menu-submenu-title {
  color: #94a1ad;
}

.ant-menu-vertical .ant-menu-submenu-selected {
  background: @primaryColor;
}

.ant-pro-layout .ant-pro-layout-content {
  padding-inline: 0;
  padding-block: 0;
}

.ant-btn-default:not(:disabled):not(.ant-btn-disabled):hover {
  color: @primaryColor;
  background: #ffffff;
  border-color: @primaryColor;
}

.ant-btn-link {
  color: @primaryColor;
}

a {
  color: @primaryColor;
}

.ant-pagination .ant-pagination-item-active {
  border-color: @primaryColor;
}

.ant-pagination .ant-pagination-item-active:hover {
  border-color: #4bcff8;
}

.ant-pagination .ant-pagination-item-active a {
  color: @primaryColor;
}

.ant-drawer-header-title {
  flex-direction: row-reverse;
}

.common-page {
  height: calc(100vh - 148px);
  margin: 24px;
  background: #ffffff;
  box-shadow: 0px 2px 3px 0px rgba(0, 0, 0, 0.03);

  .common-page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 48px;
    padding: 0 20px;
    color: #181818;
    font-weight: 500;
    font-weight: 700;
    line-height: 47px;
    letter-spacing: 0;
    border-bottom: 1px solid #f0f2f5;

    .add-header {
      display: flex;
      align-items: center;

      > img {
        margin-right: 12px;
        cursor: pointer;
      }
    }
  }

  .common-page-body {
    height: calc(100% - 48px);
    padding: 20px 20px 10px;
  }

  .search-form {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    margin-bottom: 16px;

    .ant-input-search,
    .ant-select {
      width: 200px;
    }
  }

  .table-wrap {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: calc(100% - 48px);

    .ant-table-cell {
      // padding: 9px 16px;
    }
  }

  .pagination-wrap {
    padding-top: 10px;
    border-top: 1px solid rgba(233, 233, 233, 1);

    .ant-pagination {
      justify-content: flex-end;
    }
  }

  .submit-wrap {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    margin-bottom: 0;
    padding: 10px 24px;
    text-align: right;
    background: #fff;
    border-top: 1px solid #ededed;

    .ant-btn {
      width: 88px;
      margin-left: 8px;
    }
  }
}

.margin-t-20 {
  margin-top: 20px;
}

.custom-number {
  font-family: DINAlternate-Bold;
}

.ant-table-body {
  scrollbar-width: auto;
  scrollbar-color: auto;
}

.ant-pro-top-nav-header-menu {
  .ant-menu-item {
    display: inline-block;
    height: 50px !important;
    line-height: 50px !important;

    .ant-pro-base-menu-horizontal-item-title {
      // display: inline-block;
      height: 46px !important;
      line-height: 46px !important;
    }
  }

  .ant-pro-base-menu-horizontal-item-title {
    color: rgba(255, 255, 255, 0.7) !important;
    font-size: 16px !important;
    border-bottom: 3px transparent solid;
  }

  .ant-menu-item-selected {
    .ant-pro-base-menu-horizontal-item-title {
      color: #fff !important;
      border-bottom: 3px #ffc73a solid;
    }
  }
}

.homeTab {
  .ant-tabs-nav {
    display: none;
  }
}

:where(.css-dev-only-do-not-override-1n0yexz).ant-table-wrapper .ant-table {
  scrollbar-color: auto !important;
}

.ant-button-green {
  background: #17c5a6;
  border-color: #17c5a6;
  &:hover,
  &:active {
    background: #17c5a6 !important;
  }
}

.ant-modal-body {
  max-height: calc(100vh - 240px);
  overflow-y: auto;
}

.word-break {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
