import { request } from 'umi';

// 工单总览
export async function workorderOverView(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/order/workorderOverView`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 单位列表
export async function listUnitManage(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/assets/listUnitManage`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 工单列表
export async function listWorkOrder(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/order/listWorkOrder`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 工单详情
export async function getWorkOrderDetail(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/order/getWorkOrderDetail`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 工单管理-发起工单
export async function startWorkOrder(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/order/startWorkOrder`, {
    method: 'POST',
    data: {
      ...params,
    },
    ...(options || {}),
  });
}

// 工单管理-工单流转
export async function workOrderCirculation(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/order/workOrderCirculation`, {
    method: 'POST',
    data: {
      ...params,
    },
    ...(options || {}),
  });
}

// 工单管理-工单流转记录
export async function getWorkOrderRecord(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/order/getWorkOrderRecord`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 工单管理-根据物品id或是配件id查询在途故障单锁定的数量
export async function getTicketWhCount(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/order/getTicketWhCount`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
