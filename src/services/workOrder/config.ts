import { request } from 'umi';

// 工单管理基础配置
export async function listTicketType(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/order/listTicketType`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 工单管理类型保存
export async function saveTicketType(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/order/saveTicketType`, {
    method: 'POST',
    data: {
      ...params,
    },
    ...(options || {}),
  });
}

// 类型配置查询
export async function getTicketTypeConfig(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/order/getTicketTypeConfig`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 人员列表
export async function listPerManage(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/assets/listPerManage`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 保存工单类型配置
export async function saveTicketTypeConfig(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/order/saveTicketTypeConfig`, {
    method: 'POST',
    data: {
      ...params,
    },
    ...(options || {}),
  });
}

// 删除工单类型
export async function removeTicketType(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/order/removeTicketType`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
