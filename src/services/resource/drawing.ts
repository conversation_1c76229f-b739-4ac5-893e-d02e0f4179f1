import { request } from 'umi';

// 图纸管理
export async function listDrawManage(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/assets/listDrawManage`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 保存图纸
export async function saveDrawManage(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/assets/saveDrawManage`, {
    method: 'POST',
    data: {
      ...params,
    },
    ...(options || {}),
  });
}

// 删除图纸
export async function removeDrawManage(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/assets/removeDrawManage`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
