import { request } from 'umi';

// 供应链管理
export async function listSupplyManage(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/assets/listSupplyManage`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 新增供应链
export async function saveSupplyManage(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/assets/saveSupplyManage`, {
    method: 'POST',
    data: {
      ...params,
    },
    ...(options || {}),
  });
}

// 删除供应链
export async function removeSupplyManage(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/assets/removeSupplyManage`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 导出供应链
export async function exportSupply(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/assets/exportSupply`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
