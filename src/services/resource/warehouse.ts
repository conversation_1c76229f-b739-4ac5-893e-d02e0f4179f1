import { request } from 'umi';

// 仓库管理
export async function listWhManage(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/assets/listWhManage`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 仓库保存
export async function saveWhManage(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/assets/saveWhManage`, {
    method: 'POST',
    data: {
      ...params,
    },
    ...(options || {}),
  });
}

// 物品配件查询
export async function listItemAccessory(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/assets/listItemAccessory`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 仓库删除
export async function removeWhManage(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/assets/removeWhManage`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
