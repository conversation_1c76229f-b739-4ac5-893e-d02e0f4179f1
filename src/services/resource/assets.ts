import { request } from 'umi';

// 资产管理
export async function listAssetManage(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/assets/listAssetManage`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 新增资产
export async function saveAssetManage(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/assets/saveAssetManage`, {
    method: 'POST',
    data: {
      ...params,
    },
    ...(options || {}),
  });
}

// 资产删除
export async function removeAssetManage(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/assets/removeAssetManage`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
