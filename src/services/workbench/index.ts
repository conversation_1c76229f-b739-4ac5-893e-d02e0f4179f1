import { request } from 'umi';

// 数字化应用-我的收藏
export async function saveDigitalAppRelByMe(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/saveDigitalAppRelByMe`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 信息发布-数字化应用 列表
export async function listDigitalAppBaseInfo(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/listDigitalAppBaseInfo`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 数字化应用-收藏
export async function saveDigitalAppRel(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/saveDigitalAppRel`, {
    method: 'POST',
    data: {
      ...params,
    },
    ...(options || {}),
  });
}

// 数字化应用-我的设备告警
export async function istDevWarningByMe(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/istDevWarningByMe`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 数字化应用-我的待办
export async function istTodoByMe(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/istTodoByMe`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 数字化应用-我的已办
export async function istDoneByMe(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/istDoneByMe`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
