import { request as netRequest } from '@/utils/net';
import { request } from 'umi';

// 信息发布-数字化应用 列表
export async function listDigitalAppBaseInfo(params?: any, options?: { [key: string]: any }) {
  const _options = {
    ...(options || {}),
    // headers: {
    //   isDebug: true,
    // },
  };
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/listDigitalAppBaseInfo`, {
    method: 'GET',
    params: { ...params },
    ..._options,
  });
}

// 数字化应用-未登录
export async function listDigitalAppBaseInfoUn(params?: any, options?: { [key: string]: any }) {
  const _options = {
    ...(options || {}),
  };
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/listDigitalAppBaseInfoUn`, {
    method: 'GET',
    params: { ...params },
    ..._options,
  });
}

// 数字化应用-全部应用
export async function listDigitalAppBaseInfoAll(params?: any, options?: { [key: string]: any }) {
  // const _options = {
  //   ...(options || {}),
  //   headers: {
  //     isDebug: true,
  //   },
  // };
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/listDigitalAppBaseInfoAll`, {
    method: 'GET',
    params: { ...params },
    // ..._options,
    ...(options || {}),
  });
}

// 信息发布-文章 列表
export async function listPissTextInfo(params?: any, options?: { [key: string]: any }) {
  const _options = {
    ...(options || {}),
  };
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/listPissTextInfo`, {
    method: 'GET',
    params: { ...params },
    ..._options,
  });
}

// 信息发布-文章 列表
export async function infoPublishListByView(params?: any, options?: { [key: string]: any }) {
  const _options = {
    ...(options || {}),
  };
  return request<Record<string, any>>(
    `${PREFIX}/portal/backend/infoPublish/infoPublishListByView`,
    {
      method: 'GET',
      params: { ...params },
      ..._options,
    },
  );
}
export async function dataServiceGatewayByGet(params?: any, options?: { [key: string]: any }) {
  const _options = {
    ...(options || {}),
  };
  return request<Record<string, any>>(
    `${PREFIX}/portal/backend/configuration/dataServiceGatewayByGet`,
    {
      method: 'GET',
      params: { ...params },
      ..._options,
    },
  );
}

// 模块列表
export async function listModuleInfo(params?: any, options?: { [key: string]: any }) {
  const _options = {
    ...(options || {}),
  };
  return request<Record<string, any>>(
    `${PREFIX}/portal/backend/columnMgr/styleConf/styleConfList`,
    {
      method: 'GET',
      params: {
        ...params,
      },
      ..._options,
    },
  );
}

// 信息发布-布局管理 列表
export async function listLayout(params?: any, options?: { [key: string]: any }) {
  const _options = {
    ...(options || {}),
  };
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/listLayout`, {
    method: 'GET',
    params: {
      ...params,
    },
    ..._options,
  });
}

// 信息发布-文章 详情
export async function getPissTextInfoDetail(params?: any, options?: { [key: string]: any }) {
  const _options = {
    ...(options || {}),
  };
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/getPissTextInfoDetail`, {
    method: 'GET',
    params: {
      ...params,
    },
    ..._options,
  });
}
// 信息发布-文章 详情
export async function infoPublishDetail(params?: any, options?: { [key: string]: any }) {
  const _options = {
    ...(options || {}),
  };
  return request<Record<string, any>>(`${PREFIX}/portal/backend/infoPublish/infoPublishDetail`, {
    method: 'GET',
    params: {
      ...params,
    },
    ..._options,
  });
}
// 文章已读
export async function saveInfoPublishRead(params?: any, options?: { [key: string]: any }) {
  const _options = {
    ...(options || {}),
  };
  return request<Record<string, any>>(`${PREFIX}/portal/backend/infoPublish/saveInfoPublishRead`, {
    method: 'POST',
    data: {
      ...params,
    },
    ..._options,
  });
}
// 保存用户链接
export async function saveUseLink(params?: any, options?: { [key: string]: any }) {
  const _options = {
    ...(options || {}),
  };
  return request<Record<string, any>>(`${PREFIX}/portal/frontend/userCommLink/saveUseLink`, {
    method: 'POST',
    data: {
      ...params,
    },
    ..._options,
  });
}

// 信息发布-首页 底部
export async function listPageUrlInfo(params?: any, options?: { [key: string]: any }) {
  const _options = {
    ...(options || {}),
  };
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/listPageUrlInfo`, {
    method: 'GET',
    params: {
      ...params,
    },
    ..._options,
  });
}

/** 待办列表 */
export async function todoList(params?: any, options?: { [key: string]: any }) {
  const _options = {
    ...(options || {}),
  };
  return request<Record<string, any>>(`${PREFIX}/portal/backend/todo/todoList`, {
    method: 'GET',
    params: {
      ...params,
    },
    ..._options,
  });
}

// 用户常用链接
export async function useLinkList(params?: any, options?: { [key: string]: any }) {
  const _options = {
    ...(options || {}),
  };
  return request<Record<string, any>>(`${PREFIX}/portal/frontend/userCommLink/useLinkList`, {
    method: 'GET',
    params: {
      ...params,
    },
    ..._options,
  });
}
// 获取菜单树
export async function getMenuTreeBySysCode(params?: any, options?: { [key: string]: any }) {
  const _options = {
    ...(options || {}),
  };
  return request<Record<string, any>>(
    `${PREFIX}/portal/frontend/userCommLink/getMenuTreeBySysCode`,
    {
      method: 'GET',
      params: {
        ...params,
      },
      ..._options,
    },
  );
}

// 资源管理 - 单位管理 查询
export async function listUnitManage(params?: any, options?: { [key: string]: any }) {
  const _options = {
    ...(options || {}),
  };
  return request<Record<string, any>>(`${PREFIX}/portal/assets/listUnitManage`, {
    method: 'GET',
    params: {
      ...params,
    },
    ..._options,
  });
}

export async function listUnitManage2(params?: any, options?: { [key: string]: any }) {
  const _options = {
    ...(options || {}),
  };
  return request<Record<string, any>>(`${PREFIX}/portal/assets/listUnitManage2`, {
    method: 'GET',
    params: {
      ...params,
    },
    ..._options,
  });
}

export async function getScheduleInfo() {
  return netRequest.get(`${PREFIX}/portal/backend/scheduleArrange/scheduleList`);
}

export async function saveScheduleInfo(params: any) {
  return netRequest.post(`${PREFIX}/portal/backend/scheduleArrange/saveSchedule`, params);
}

export async function deleteScheduleInfo(id: number) {
  return netRequest.get(`${PREFIX}/portal/backend/scheduleArrange/removeSchedule`, { id });
}

export async function queryScheduleDetail(date: string) {
  return netRequest.get(`${PREFIX}/portal/backend/scheduleArrange/scheduleDetail`, { date });
}
