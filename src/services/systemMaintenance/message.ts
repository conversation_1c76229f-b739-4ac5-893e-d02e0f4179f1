import { request } from 'umi';

// 保存文章
export async function savePissTextInfo(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/savePissTextInfo`, {
    method: 'POST',
    data: {
      ...params,
    },
    ...(options || {}),
  });
}
// 保存
export async function saveInfoPublish(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/backend/infoPublish/saveInfoPublish`, {
    method: 'POST',
    data: {
      ...params,
    },
    ...(options || {}),
  });
}

// 文章列表
export async function listPissTextInfo(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/listPissTextInfo`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 文章列表
export async function getInfoPublishList(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/backend/infoPublish/infoPublishList`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 文章详情
export async function getPissTextInfoDetail(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/getPissTextInfoDetail`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 敏感词列表
export async function listSensitiveWords(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/listSensitiveWords`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 文章删除
export async function removePissTextInfo(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/removePissTextInfo`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
// 删除
export async function removeInfoPublish(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/backend/infoPublish/removeInfoPublish`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
