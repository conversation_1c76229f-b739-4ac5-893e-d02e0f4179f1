import { request } from 'umi';

// 分组管理
export async function listMaterialGroup(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/listMaterialGroup`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 新增分组
export async function saveMaterialGroup(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/saveMaterialGroup`, {
    method: 'POST',
    data: {
      ...params,
    },
    ...(options || {}),
  });
}

// 删除分组
export async function removeMaterialGroup(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/removeMaterialGroup`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 素材列表
export async function listMaterial(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/listMaterial`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 保存素材
export async function saveMaterial(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/saveMaterial`, {
    method: 'POST',
    data: {
      ...params,
    },
    ...(options || {}),
  });
}

// 素材删除
export async function removeMaterial(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/removeMaterial`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
