import { request } from 'umi';

// 审核发布
export async function listApprovePublish(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/listApprovePublish`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 年度发布分析
export async function yearPublishAnalysis(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/yearPublishAnalysis`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 日发布分析
export async function dayPublishAnalysis(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/dayPublishAnalysis`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 信息发布总览
export async function infoPublishOverView(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/infoPublishOverView`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 信息发布趋势
export async function variousTypesAnalysis(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/variousTypesAnalysis`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
