import { request } from 'umi';

// 样式列表
export async function listModuleInfo(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(
    `${PREFIX}/portal/backend/columnMgr/styleConf/styleConfList`,
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}
// 删除样式
export async function removeStyleConf(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(
    `${PREFIX}/portal/backend/columnMgr/styleConf/removeStyleConf`,
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}
// api列表
export async function accessConfList(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/backend/configuration/accessConfList`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
export async function getDataConfDetail(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(
    `${PREFIX}/portal/backend/columnMgr/dataCompConf/dataConfDetail`,
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}
// api列表
export async function saveAccessConf(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/backend/configuration/saveAccessConf`, {
    method: 'POST',
    data: {
      ...params,
    },
    ...(options || {}),
  });
}
// 栏目列表
export async function getColumnList(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/backend/columnMgr/columnList`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 样式保存
export async function saveModuleInfo(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(
    `${PREFIX}/portal/backend/columnMgr/styleConf/saveStyleConf`,
    {
      method: 'POST',
      data: {
        ...params,
      },
      ...(options || {}),
    },
  );
}
// 组件保存
export async function saveCompConf(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/backend/columnMgr/compConf/saveCompConf`, {
    method: 'POST',
    data: {
      ...params,
    },
    ...(options || {}),
  });
}
// 数据组件保存
export async function saveDataCompConf(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(
    `${PREFIX}/portal/backend/columnMgr/dataCompConf/saveDataCompConf`,
    {
      method: 'POST',
      data: {
        ...params,
      },
      ...(options || {}),
    },
  );
}
// 栏目保存
export async function saveColumn(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/backend/columnMgr/saveColumn`, {
    method: 'POST',
    data: {
      ...params,
    },
    ...(options || {}),
  });
}

// 获取组件列表
export async function getCompConfList(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/backend/columnMgr/compConf/compConfList`, {
    method: 'get',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
// 获取数据组件列表
export async function getDataConfList(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(
    `${PREFIX}/portal/backend/columnMgr/dataCompConf/dataConfList`,
    {
      method: 'get',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

// 模块删除
export async function removeModuleInfo(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(
    `${PREFIX}/portal/backend/columnMgr/compConf/removeCompCOnf`,
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}
// 模块删除
export async function removeDataCompInfo(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(
    `${PREFIX}/portal/backend/columnMgr/dataCompConf/removeDataCompConf`,
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}
// 模块删除
export async function removeColumn(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/backend/columnMgr/removeColumn`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 接口删除
export async function removeAccessConf(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/backend/configuration/removeAccessConf`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 查询缩略图
export async function listModuleInfoTgp(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/listModuleInfoTgp`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
