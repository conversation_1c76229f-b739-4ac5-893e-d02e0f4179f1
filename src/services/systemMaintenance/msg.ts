import { request } from '@/utils/net';

export async function getMsgList() {
  return request.get(`${PREFIX}/portal/backend/message/messageList`);
}

export async function markMsgIsRead(params: any) {
  return request.post(`${PREFIX}/portal/backend/message/saveUserMessage`, params);
}

export async function delMsg(params: any) {
  return request.get(`${PREFIX}/portal/backend/message/removeUserMessage`, params);
}
