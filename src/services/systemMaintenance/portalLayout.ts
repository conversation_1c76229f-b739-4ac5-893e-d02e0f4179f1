import { request } from 'umi';

// 信息发布-布局管理 列表
export async function listLayout(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/frontend/templateList`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
export async function getUserTemplateList(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/frontend/userTemplateList`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 信息发布-布局管理 新增/修改
export async function saveLayout(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/saveLayout`, {
    method: 'POST',
    data: {
      ...params,
    },
    ...(options || {}),
  });
}

// 信息发布-布局管理 删除
export async function removeLayout(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/removeLayout`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 信息发布-布局管理 绑定模块关系
export async function saveLayoutRelModuleBind(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/saveLayoutRelModuleBind`, {
    method: 'POST',
    data: {
      ...params,
    },
    ...(options || {}),
  });
}

// 保存模板 个人
export async function saveUserTemplate(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/frontend/saveUserTemplate`, {
    method: 'POST',
    data: {
      ...params,
    },
    ...(options || {}),
  });
}
// 保存模板  默认
export async function saveTemplate(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/frontend/saveTemplate`, {
    method: 'POST',
    data: {
      ...params,
    },
    ...(options || {}),
  });
}
