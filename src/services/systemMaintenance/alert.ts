import { request } from '@/utils/net';

export async function saveAlertTypeInfo(params: any) {
  return request.post(`${ALARMPREFIX}/warning/safeproduction/sort/saveSort`, params);
}

export async function deleteAlertTypeInfo(id: string) {
  return request.get(`${ALARMPREFIX}/warning/safeproduction/sort/removeSort`, { id });
}

export async function saveAlertLevelInfo(params: any) {
  return request.post(`${ALARMPREFIX}/warning/safeproduction/scale/saveScale`, params);
}

export async function deleteAlertLevelInfo(id: string) {
  return request.get(`${ALARMPREFIX}/warning/safeproduction/scale/removeScale`, { id });
}

export async function queryAlertPolicyDetail(id: string) {
  return request.get(`${ALARMPREFIX}/warning/safeproduction/scale/configurationDetail`, { id });
}

export async function saveAlertPolicyDetail(params: any) {
  return request.post(`${ALARMPREFIX}/warning/safeproduction/scale/saveConfiguration`, params);
}

export async function getAlertTypeList() {
  return request.get(`${ALARMPREFIX}/warning/safeproduction/sort/sortList`, {
    pageNo: 1,
    pageSize: 1000,
  });
}

export async function getAlertLevelist() {
  return request.get(`${ALARMPREFIX}/warning/safeproduction/scale/scaleList`, {
    pageNo: 1,
    pageSize: 1000,
  });
}

export async function saveReportList(params: any) {
  return request.post(`${ALARMPREFIX}/warning/safeproduction/report/saveReportList`, params);
}

export async function getReportDetail(id: string) {
  return request.get(`${ALARMPREFIX}/warning/safeproduction/report/reportDetail`, { id });
}

export async function updateDispose(params: any) {
  return request.post(`${ALARMPREFIX}/warning/safeproduction/dispose/updateDispose`, params);
}

export async function getSupervisionDetail(id: string) {
  return request.get(`${ALARMPREFIX}/warning/safeproduction/handle/handleDetail`, { id });
}

export async function updateSupervisionInfo(params: any) {
  return request.post(`${ALARMPREFIX}/warning/safeproduction/handle/saveHandle`, params);
}

export async function statisticAnalysisList(params: any) {
  return request.get(
    `${ALARMPREFIX}/warning/safeproduction/statisticAnalysis/statisticAnalysisList`,
    params,
  );
}

export async function saveMessageRead(params: any) {
  return request.post(`${ALARMPREFIX}/warning/safeproduction/report/saveMessageRead`, params);
}
