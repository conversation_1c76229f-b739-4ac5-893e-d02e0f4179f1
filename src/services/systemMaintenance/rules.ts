import { request } from '@/utils/net';

export async function saveInstitution(params: any) {
  return request.post(`${PREFIX}/portal/backend/institutionMgr/saveInstitution`, params);
}

export async function deleteInstitution(id: string) {
  return request.get(`${PREFIX}/portal/backend/institutionMgr/removeInstitution`, { id });
}

export async function getRuleList(params: any) {
  return request.get(`${PREFIX}/portal/backend/institutionMgr/institutionList`, params);
}
