import { request } from 'umi';

// 渠道管理
export async function listPissMedia(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/listPissMedia`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 新增渠道
export async function savePissMedia(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/infoPublish/savePissMedia`, {
    method: 'POST',
    data: {
      ...params,
    },
    ...(options || {}),
  });
}
