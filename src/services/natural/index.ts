import { request } from '@/utils/net';

export async function removeGeology(id: string) {
  return request.get(`${ALARMPREFIX}/warning/naturalDisaster/geology/removeGeology`, { id });
}

export async function geologyAnalysis() {
  return request.get(`${ALARMPREFIX}/warning/naturalDisaster/geology/statisticGeology`);
}

export async function addGeologyInfo(params: any) {
  return request.post(`${ALARMPREFIX}/warning/naturalDisaster/geology/saveGeology`, params);
}

export async function geologyDetail(id: string) {
  return request.get(`${ALARMPREFIX}/warning/naturalDisaster/geology/geologyDetail`, { id });
}

export async function floodAnalysis() {
  return request.get(`${ALARMPREFIX}/warning/naturalDisaster/flood/statisticFlood`);
}

export async function removeFlood(id: string) {
  return request.get(`${ALARMPREFIX}/warning/naturalDisaster/flood/removeFlood`, { id });
}

export async function floodDetail(id: string) {
  return request.get(`${ALARMPREFIX}/warning/naturalDisaster/flood/floodDetail`, { id });
}

export async function addFloodInfo(params: any) {
  return request.post(`${ALARMPREFIX}/warning/naturalDisaster/flood/saveFlood`, params);
}

export async function addForestInfo(params: any) {
  return request.post(`${ALARMPREFIX}/warning/naturalDisaster/forest/saveForest`, params);
}

export async function removeForest(id: string) {
  return request.get(`${ALARMPREFIX}/warning/naturalDisaster/forest/removeForest`, { id });
}

export async function forestDetail(id: string) {
  return request.get(`${ALARMPREFIX}/warning/naturalDisaster/forest/forestDetail`, { id });
}

export async function forestAnalysis() {
  return request.get(`${ALARMPREFIX}/warning/naturalDisaster/forest/statisticForest`);
}
