// src/mock/alertTypeConfig.ts
import { Request, Response } from 'express';
import Mock from 'mockjs';

const { Random } = Mock;

// Mock data structure
const generateMockAlertTypes = () => {
  return [
    {
      id: 1,
      name: '安全生产监测预警',
      code: 'AQSC-202507001',
      source: '人工录入',
      createTime: '2025-06-16 14:33:22',
      operations: ['新增子类型', '编辑'],
      parentId: 0,
      children: [
        {
          id: 2,
          name: '危化企业监测',
          code: 'AQSC-202507002',
          source: '人工录入',
          createTime: '2025-06-15 14:33:22',
          operations: ['新增子类型', '编辑', '删除'],
          children: [],
        },
        {
          id: 3,
          name: '危险源监测',
          code: 'AQSC-202507003',
          source: '人工录入',
          createTime: '2025-06-14 14:33:22',
          operations: ['新增子类型', '编辑', '删除'],
          children: [],
        },
        {
          id: 4,
          name: '冶金企业监测',
          code: 'AQSC-202507004',
          source: '人工录入',
          createTime: '2025-06-13 14:33:22',
          operations: ['新增子类型', '编辑', '删除'],
          children: [],
        },
        {
          id: 5,
          name: '非煤矿山企业监测',
          code: 'AQSC-202507005',
          source: '人工录入',
          createTime: '2025-06-12 14:33:22',
          operations: ['新增子类型', '编辑', '删除'],
          children: [],
        },
      ],
    },
    {
      id: 6,
      name: '自然灾害监测预警',
      code: 'ZRZH-202507001',
      source: '人工录入',
      createTime: '2025-06-11 14:33:22',
      operations: ['新增子类型', '编辑'],
      parentId: 0,

      children: [
        {
          id: 7,
          name: '防汛监测',
          code: 'ZRZH-202507002',
          source: '人工录入',
          createTime: '2025-06-10 14:33:22',
          operations: ['新增子类型', '编辑', '删除'],
          children: [],
        },
        {
          id: 8,
          name: '积水点监测预警',
          code: 'ZRZH-202507003',
          source: '人工录入',
          createTime: '2025-06-10 14:33:22',
          operations: ['新增子类型', '编辑', '删除'],
          children: [],
        },
        {
          id: 9,
          name: '地质灾害监测预警',
          code: 'ZRZH-202507004',
          source: '人工录入',
          createTime: '2025-06-10 14:33:22',
          operations: ['新增子类型', '编辑', '删除'],
          children: [],
        },
        {
          id: 10,
          name: '森林火灾监测预警',
          code: 'ZRZH-202507005',
          source: 'AI感知平台',
          createTime: '2025-06-16 14:33:22',
          operations: ['新增子类型', '编辑', '删除'],
          children: [],
        },
      ],
    },
  ];
};

// Mock API endpoint
export default {
  // Get alert type list
  'GET /api/warning/safeproduction/sort/sortList': (req: Request, res: Response) => {
    const { pageNo = 1, pageSize = 10 } = req.query ?? {};

    // Generate mock data
    const allData = generateMockAlertTypes();

    // Simulate pagination
    const start = (Number(pageNo) - 1) * Number(pageSize);
    const end = start + Number(pageSize);
    const paginatedData = allData.slice(start, end);

    // Return response in expected format
    return {
      code: '200',
      msg: 'success',
      data: {
        data: paginatedData,
        total: allData.length,
        current: Number(pageNo),
        size: Number(pageSize),
      },
    };
  },

  // Save alert type info
  'POST /api/warning/safeproduction/sort/save': (req: Request, res: Response) => {
    const { name, code } = req.body;

    // Simulate save operation
    return res.json({
      code: '200',
      msg: '保存成功！',
      data: {
        id: Random.id(),
        name,
        code,
        createTime: Random.datetime('yyyy-MM-dd HH:mm:ss'),
      },
    });
  },

  // Delete alert type
  'DELETE /api/warning/safeproduction/sort/delete': (req: Request, res: Response) => {
    const { id } = req.query;

    // Simulate delete operation
    return res.json({
      code: '200',
      msg: '删除成功！',
      data: {
        id,
      },
    });
  },
};
