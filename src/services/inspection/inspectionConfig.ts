import { request } from 'umi';

// 巡检管理-巡检任务配置-巡检类型列表
export async function listInspectionTypeInfo(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/inspection/listInspectionTypeInfo`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 巡检管理-巡检任务配置-巡检类型 树列表
export async function listInspectionTypeInfoByTree(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/inspection/listInspectionTypeInfoByTree`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 巡检管理-巡检任务配置-巡检类型 新增/修改
export async function saveInspectionTypeInfo(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/inspection/saveInspectionTypeInfo`, {
    method: 'POST',
    data: {
      ...params,
    },
    ...(options || {}),
  });
}

// 巡检管理-巡检任务配置-巡检类型 删除
export async function removeInspectionTypeInfo(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/inspection/removeInspectionTypeInfo`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 巡检管理-巡检任务配置-巡检计划列表
export async function listInspectionPlanInfo(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/inspection/listInspectionPlanInfo`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 巡检管理-巡检任务配置-巡检项目列表
export async function listInspectionItem(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/inspection/listInspectionItem`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 巡检管理-巡检任务配置-巡检项目 新增/修改
export async function saveInspectionItem(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/inspection/saveInspectionItem`, {
    method: 'POST',
    data: {
      ...params,
    },
    ...(options || {}),
  });
}

// 巡检管理-巡检任务配置-巡检项目 删除
export async function removeInspectionItem(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/inspection/removeInspectionItem`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 资源管理 - 人员管理 查询
export async function listPerManage(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/assets/listPerManage`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 巡检管理-巡检结果统计-巡检记录列表
export async function listInspectionDealRecord(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/inspection/listInspectionDealRecord`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 巡检管理-巡检结果统计-巡检任务概要
export async function listInspectionTaskOutline(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/inspection/listInspectionTaskOutline`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 巡检管理-巡检结果统计-巡检结果概要
export async function listInspectionResultOutline(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/inspection/listInspectionResultOutline`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 巡检管理-巡检任务配置-巡检计划 新增/修改
export async function saveInspectionPlanInfo(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/inspection/saveInspectionPlanInfo`, {
    method: 'POST',
    data: {
      ...params,
    },
    ...(options || {}),
  });
}

// 巡检管理-巡检任务配置-巡检计划详情
export async function getInspectionPlanInfoDetail(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/inspection/getInspectionPlanInfoDetail`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 巡检管理-巡检任务配置-巡检计划 删除
export async function removeInspectionPlanInfo(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/inspection/removeInspectionPlanInfo`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 巡检管理-巡检任务配置-关闭巡检计划
export async function closeInspectionPlanInfo(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/inspection/closeInspectionPlanInfo`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 巡检管理-巡检任务列表
export async function listInspectionTaskInfo(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/inspection/listInspectionTaskInfo`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 巡检管理-巡检任务总览
export async function listInspectionTaskOverView(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/inspection/listInspectionTaskOverView`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 巡检管理-巡检任务详情
export async function getInspectionTaskInfoDetail(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/inspection/getInspectionTaskInfoDetail`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 巡检管理-巡检任务 处理
export async function dealInspectionTaskInfo(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/inspection/dealInspectionTaskInfo`, {
    method: 'POST',
    data: {
      ...params,
    },
    ...(options || {}),
  });
}

// 巡检管理-巡检任务 新增/修改 - 手工任务
export async function saveInspectionTaskInfo(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/inspection/saveInspectionTaskInfo`, {
    method: 'POST',
    data: {
      ...params,
    },
    ...(options || {}),
  });
}

// 巡检管理-关闭 巡检任务
export async function closeInspectionTaskInfo(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/inspection/closeInspectionTaskInfo`, {
    method: 'POST',
    data: {
      ...params,
    },
    ...(options || {}),
  });
}

// 巡检管理-巡检结果统计-巡检记录列表-导出
export async function assetManufacturerExport(
  params?: any,
  options?: { [key: string]: any },
): Promise<Blob> {
  return request<any>(`${PREFIX}/portal/inspection/listInspectionDealRecordExport`, {
    method: 'GET',
    responseType: 'blob',
    params: {
      ...params,
    },
    ...(options || {}),
  }).then((response) => {
    return response;
  });
}
