import { request } from 'umi';

// 考勤总览
export async function getAttendanceAnalysisOView(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/attendance/getAttendanceAnalysisOView`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 人数分布
export async function getAttendanceAnalysisPFB(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/attendance/getAttendanceAnalysisPFB`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 平均工时
export async function getAttendanceAnalysisAvgWorkHour(
  params?: any,
  options?: { [key: string]: any },
) {
  return request<Record<string, any>>(
    `${PREFIX}/portal/attendance/getAttendanceAnalysisAvgWorkHour`,
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

// 考勤统计列表
export async function listAttendanceStatistics(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/attendance/listAttendanceStatistics`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 考勤统计列表导出
export async function listAttendanceStatisticsExport(
  params?: any,
  options?: { [key: string]: any },
) {
  return request<Record<string, any>>(
    `${PREFIX}/portal/attendance/listAttendanceStatisticsExport`,
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}
