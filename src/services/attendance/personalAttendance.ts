import { request } from 'umi';

// 个人考勤-请假 列表
export async function listAttendanceRecords(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/attendance/listAttendanceRecords`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 个人考勤-请假 列表
export async function listAttendanceReqLeaveRecord(params?: any, options?: { [key: string]: any }) {
  if (params.startTime) {
    params.startTime = `${params.startTime} 00:00:00`;
  }
  if (params.endTime) {
    params.endTime = `${params.endTime} 23:59:59`;
  }
  return request<Record<string, any>>(`${PREFIX}/portal/attendance/listAttendanceReqLeaveRecord`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 考勤台账 总览
export async function getAttendanceRecordsOViewByMe(
  params?: any,
  options?: { [key: string]: any },
) {
  return request<Record<string, any>>(`${PREFIX}/portal/attendance/getAttendanceRecordsOViewByMe`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 考勤管理-考勤打卡/更新打卡
export async function saveAttendanceRecords(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/attendance/saveAttendanceRecords`, {
    method: 'POST',
    data: {
      ...params,
    },
    ...(options || {}),
  });
}

// 考勤管理-请假
export async function saveAttendanceReqLeaveRecord(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/attendance/saveAttendanceReqLeaveRecord`, {
    method: 'POST',
    data: {
      ...params,
    },
    ...(options || {}),
  });
}

// 考勤管理-请假取消
export async function saveReqLeaveApproval(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/attendance/saveReqLeaveApproval`, {
    method: 'POST',
    data: {
      ...params,
    },
    ...(options || {}),
  });
}
