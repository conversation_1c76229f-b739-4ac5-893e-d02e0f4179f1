import { request } from 'umi';

// 请假列表
export async function listAttendanceReqLeaveView(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/hbnsbd/attendance/listAttendanceReqLeaveView`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 请假审批
export async function saveReqLeaveApproval(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/hbnsbd/attendance/saveReqLeaveApproval`, {
    method: 'POST',
    data: {
      ...params,
    },
    ...(options || {}),
  });
}

// 请假审批导出
export async function exportAttendanceReqLeaveRecord(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/hbnsbd/attendance/exportAttendanceReqLeaveRecord`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
