import { request } from 'umi';

// 总览
export async function getAttendanceRecordsOView(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/attendance/getAttendanceRecordsOView`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 考勤台账列表
export async function listAttendanceLedger(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/attendance/listAttendanceLedger`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 导出考勤台账
export async function exportAttendanceLedger(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/attendance/exportAttendanceLedger`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
