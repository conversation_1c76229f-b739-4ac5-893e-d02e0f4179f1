import { request } from 'umi';

// 文档列表
export async function listDoc(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/docManage/listDoc`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 新增文件
export async function saveDoc(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/docManage/saveDoc`, {
    method: 'POST',
    data: {
      ...params,
    },
    ...(options || {}),
  });
}

// 文档日志列表
export async function listDocLog(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/docManage/listDocLog`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 文档详情
export async function getDocDetail(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/docManage/getDocDetail`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
