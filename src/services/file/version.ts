import { request } from 'umi';

// 版本列表
export async function listDocVersion(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/docManage/listDocVersion`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 子版本列表
export async function listChildDocVersion(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/docManage/listChildDocVersion`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 新增版本
export async function saveDocVersion(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/docManage/saveDocVersion`, {
    method: 'POST',
    data: {
      ...params,
    },
    ...(options || {}),
  });
}

// 文档类型
export async function listDocType(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/docManage/listDocType`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 新增文档类型
export async function saveDocType(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/docManage/saveDocType`, {
    method: 'POST',
    data: {
      ...params,
    },
    ...(options || {}),
  });
}

// 关联文档列表
export async function listChildVersionRelDoc(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/docManage/listChildVersionRelDoc`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 删除文档版本
export async function removeDocVersion(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/docManage/removeDocVersion`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

// 删除文档
export async function removeDoc(params?: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${PREFIX}/portal/docManage/removeDoc`, {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}
