import React, { useState, useEffect } from 'react';
import { getMsgList } from '@/services/systemMaintenance/msg';
import { Tabs, List, Modal, Button, message as AntdMessage } from 'antd';

interface Message {
  id: number;
  title: string;
  content: string;
}

interface MsgListProps {
  updateMessageCount?: (count: number) => void; // 父组件传入的函数
}

const MsgList: React.FC<MsgListProps> = ({ updateMessageCount }) => {
  const [visible, setVisible] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState<Message | null>(null);
  const [personalMessages, setPersonalMessages] = useState<Message[]>([]);
  const [systemMessages, setSystemMessages] = useState<Message[]>([]);

  useEffect(() => {
    const fetchMessages = async () => {
      try {
        const response = await getMsgList();
        if (response.code === '200') {
          const alarmMessages = response.data.alarm.map((item: any, index: number) => ({
            id: index,
            title: `个人消息 ${index + 1}`,
            content: item.content,
          }));
          const systemMessages = response.data.system.map((item: any, index: number) => ({
            id: index,
            title: `系统消息 ${index + 1}`,
            content: item.content,
          }));
          setPersonalMessages(alarmMessages);
          setSystemMessages(systemMessages);

          // 计算消息总数并调用父组件函数
          const totalMessages = alarmMessages.length + systemMessages.length;
          updateMessageCount?.(totalMessages);
        } else {
          AntdMessage.error('获取消息列表失败');
        }
      } catch (error) {
        AntdMessage.error('请求消息列表时发生错误');
      }
    };

    fetchMessages();
  }, [updateMessageCount]);

  const showModal = (message: Message) => {
    setSelectedMessage(message);
    setVisible(true);
  };

  const handleCancel = () => {
    setVisible(false);
    setSelectedMessage(null);
  };

  return (
    <div>
      <Tabs defaultActiveKey="1">
        <Tabs.TabPane tab="个人消息" key="1">
          <List
            dataSource={personalMessages}
            renderItem={(item) => (
              <List.Item>
                <a onClick={() => showModal(item)}>{item.title}</a>
              </List.Item>
            )}
          />
        </Tabs.TabPane>
        <Tabs.TabPane tab="系统消息" key="2">
          <List
            dataSource={systemMessages}
            renderItem={(item) => (
              <List.Item>
                <a onClick={() => showModal(item)}>{item.title}</a>
              </List.Item>
            )}
          />
        </Tabs.TabPane>
      </Tabs>
      <Modal
        title="消息详情"
        open={visible}
        onCancel={handleCancel}
        footer={[<Button onClick={handleCancel}>取消</Button>]}
      >
        <p>{selectedMessage?.content}</p>
      </Modal>
    </div>
  );
};

export default MsgList;
