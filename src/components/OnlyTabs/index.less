.tabsContainer {
  width: 100%;
  display: flex;
  gap: 28px;
  position: relative;
  padding-bottom: 6px;
  padding-left: 12px;
  border-bottom: 1px solid #d9d9d9;
  font-weight: normal;

  &.buttonMode {
    border-bottom: none; // 按钮模式下去掉底部分割线
    padding-bottom: 0;
    gap: 0;
  }

  .tab {
    position: relative;
    height: 28px;
    line-height: 25px;
    cursor: pointer;
    color: #595959;
    font-size: 14px;
    transition: color 0.3s;

    &:hover {
      color: #1890ff;
    }

    &.active {
      color: #1890ff;

      &::after {
        content: '';
        position: absolute;
        bottom: -6px;
        left: 0;
        right: 0;
        height: 2px;
        background-color: #1890ff;
      }
    }

    &.button {
      border: 1px solid #d9d9d9; // 按钮模式下的边框
      padding: 3px 20px;
      height: 34px;
      text-align: center;
      line-height: 28px;
      color: #595959;
      box-sizing: border-box;
      background-color: #ffffff;
      transition: all 0.3s;
    
      &:hover {
        background-color: #e6f7ff; // 鼠标悬停时按钮背景色变化
        color: #1890ff;
      }
    
      &.active {
        color: #1890ff;
        font-weight: normal;
        border-color: #1890ff;
        box-shadow: inset -1px 0 0 0 #1890ff; // 使用 box-shadow 模拟右边框高亮
    
        &::after {
          content: none; // 按钮模式下去掉高亮分割线
        }
      }
    
      &:not(:last-child) {
        margin-right: -1px; // 避免按钮之间的边框叠加
      }
      &:last-child {
        &.active {
          border-right: none; // 移除最后一个按钮的右边框
        }
    
      }
    }
  }
}