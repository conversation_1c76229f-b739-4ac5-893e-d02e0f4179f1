import React from 'react';
import styles from './index.less';

interface Tab {
  key: string;
  label: string;
}

interface TabsProps {
  tabs: Tab[];
  activeKey: string;
  onTabClick: (key: string) => void;
  isButtonMode?: boolean; // 新增属性
}

const OnlyTabs: React.FC<TabsProps> = ({ tabs, activeKey, onTabClick, isButtonMode = false }) => {
  return (
    <div
      className={`${styles.tabsContainer} ${isButtonMode ? styles.buttonMode : ''}`} // 根据 isButtonMode 动态添加样式
    >
      {tabs.map((tab) => (
        <div
          key={tab.key}
          className={`${styles.tab} ${activeKey === tab.key ? styles.active : ''} ${
            isButtonMode ? styles.button : ''
          }`} // 根据 isButtonMode 动态添加按钮样式
          onClick={() => onTabClick(tab.key)}
        >
          {tab.label}
        </div>
      ))}
    </div>
  );
};

export default OnlyTabs;
