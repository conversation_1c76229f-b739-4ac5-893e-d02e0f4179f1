import { Button, Form, Row } from 'antd';
import { Rule } from 'antd/lib/form';
import React, { useEffect, useState } from 'react';
import styles from './index.less';

export interface FormItem {
  label: string;
  prop: string;
  rules?: Rule[];
  disabled?: boolean;
  type?: string;
  span?: number;
  component?: React.ReactNode;
}

type Handle = (formData?: any) => boolean | Promise<boolean | void>;

interface ModalFormProps {
  visible: boolean;
  onClose: () => void;
  openType?: 'add' | 'edit' | 'view' | 'addSub';
  showDefaultButtons?: boolean;
  customButtons?: React.ReactNode;
  validateOnSubmit?: boolean;
  onSubmit?: Handle;
  onCancel?: Handle;
  formComp?: any;
  initialValues?: any;
  containerClass?: string;
  formClass?: string;
}

const ModalForm: React.FC<ModalFormProps> = ({
  visible,
  onClose,
  openType = 'add',
  showDefaultButtons = true,
  customButtons,
  validateOnSubmit = true,
  onSubmit,
  onCancel,
  formComp,
  initialValues,
  containerClass,
  formClass,
}) => {
  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    if (openType === 'edit' || openType === 'view' || openType === 'addSub') {
      form.setFieldsValue(initialValues);
    }
  }, [openType, initialValues, form]);

  const judgeCloseByHandle = async (handle?: Handle) => {
    if (handle) {
      const result = await handle(form.getFieldsValue());
      if (result !== false) {
        onClose();
        form.resetFields();
      }
    } else {
      onClose();
      form.resetFields();
    }
  };

  const handleSubmit = async () => {
    if (submitting) return;
    setSubmitting(true);
    if (validateOnSubmit) {
      try {
        await form.validateFields();
      } catch (error) {
        return;
      }
    }
    await judgeCloseByHandle(onSubmit);
    setSubmitting(false);
  };

  const handleCancel = () => {
    judgeCloseByHandle(onCancel);
  };

  const enhancedFormComp = React.cloneElement(formComp, { form, formInitValue: initialValues });

  return (
    <div className={`${containerClass || ''} ${visible && styles.formContainer}`}>
      {visible && (
        <div className="common-page-body module-detail">
          <div className={`${styles.form} ${formClass || ''}`}>
            <Form form={form} labelCol={{ span: 4 }} disabled={openType === 'view'}>
              <Row gutter={[16, 0]}>{enhancedFormComp}</Row>
            </Form>
          </div>
          <div className="submit-wrap" style={{ textAlign: 'right' }}>
            {showDefaultButtons && (
              <>
                {openType !== 'view' ? (
                  <Button
                    className={styles.btn}
                    loading={submitting}
                    disabled={submitting}
                    type="primary"
                    onClick={handleSubmit}
                  >
                    提交
                  </Button>
                ) : (
                  <Button className={styles.btn} type="primary" onClick={onClose}>
                    确定
                  </Button>
                )}
                {customButtons}
                <Button className={styles.btn} onClick={handleCancel}>
                  取消
                </Button>
              </>
            )}
            {!showDefaultButtons && customButtons}
          </div>
        </div>
      )}
    </div>
  );
};

export default ModalForm;
