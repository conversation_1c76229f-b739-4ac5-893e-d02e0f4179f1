import { downloadFile } from '@/utils/commonFileFun';
import { Image as AImage, Button, Modal, Upload, message } from 'antd';
import type { UploadProps } from 'antd/es/upload';
import CryptoJS from 'crypto-js';
import React, { useEffect, useState } from 'react';
import imagesImg from '../../assets/images/common/image.png';
import videoImg from '../../assets/images/common/video.png';
import videoImgCommon from '../../assets/images/video.png';
import './index.less';

interface UploadCustomProps {
  type?: string;
  value?: any;
  onChange?: any;
  otherProps?: UploadProps;
  accept?: string;
  fileDir?: string;
  uploadChange?: any;
  buttonTitle?: string;
  buttonType?: 'primary' | 'default';
  maxNum?: number;
}
const typeMap: any = {
  image: { type: '.png,.jpg,.jpeg' },
  video: { type: '.mp4' },
  doc: { type: '.doc,.pdf,.docx' },
};
const UploadCustom: React.FC<UploadCustomProps> = (props: UploadCustomProps) => {
  const {
    type = 'image',
    value = [],
    onChange,
    otherProps,
    accept = '',
    fileDir = 'portal',
    buttonTitle = '选择文件',
    buttonType = 'default',
    uploadChange,
    maxNum,
  } = props;
  const [previewOpen, setPreviewOpen] = useState(false);
  const [realTitle, setRealTitle] = useState('');
  const [filePath, setFilePath] = useState('');
  const [fileList, setFileList] = useState<any[]>(value);
  const [current, setCurrent] = useState(0);
  const handleCancel = () => {
    setPreviewOpen(false);
  };

  function getIndex(item: any, file: any) {
    let result: boolean = false;
    if (item?.response?.data?.downloadUrl === file?.response?.data?.downloadUrl) {
      result = true;
    }
    if (item?.filePath && file?.filePath) {
      if (item?.filePath === file?.filePath) {
        result = true;
      }
    }
    return result;
  }

  const handlePreview = async (file: any) => {
    console.log(file);
    if (!file.filePath) {
      file.preview = downloadFile(file?.response?.data.downloadUrl);
    } else {
      file.preview = downloadFile(file.url);
    }
    if (type === 'image') {
      const index = fileList.findIndex((item: any) => getIndex(item, file));
      setCurrent(index);
    }
    setFilePath(file.preview);
    // const re = new RegExp('(.zip|.Zip|.ZIP)$');
    // if (!re.test(file?.name)) {
    setPreviewOpen(true);
    // }
    setRealTitle(file?.name);
  };

  const handleChange: UploadProps['onChange'] = ({ file, fileList: newFileList, event }) => {
    console.log('newFileList===', newFileList);
    if (uploadChange) {
      if (file?.status === 'uploading') {
        uploadChange(true);
      } else {
        uploadChange(false);
      }
    }
    setFileList(newFileList);
    onChange && onChange(newFileList, file, event);
  };

  const uploadButton = (
    <>
      {type === 'doc' ? (
        <Button type={buttonType}>{buttonTitle}</Button>
      ) : (
        <div>
          <img
            style={{ width: 32 }}
            src={type === 'image' ? imagesImg : videoImg}
            alt="upload type"
          />
        </div>
      )}
    </>
  );

  useEffect(() => {
    if (value?.length > 0 && fileList !== value) {
      setFileList(value);
    }
  }, [value]);

  const uploadProps: any = {
    // action: `${PREFIX}/api/simple-uploadFile?bucket=${fileDir || type}`,
    action: `${PREFIX_SERVER}/api/simple-uploadFile?bucket=${fileDir || type}`,
    fileList,
    showUploadList: {
      showPreviewIcon: true,
      showRemoveIcon: true,
    },
    multiple: true,
    headers: {
      access_token: localStorage.getItem('honeycomb_token') || '',
    },
    ...otherProps,
  };

  function beforeUpload(file: any) {
    if (file.size === 0) {
      message.error('上传文件的内容不能为空');
      return Upload.LIST_IGNORE;
    }
    const fileType = file?.name?.substring(file?.name!.lastIndexOf('.'))?.toLowerCase();
    const acceptList = typeMap[type]?.type?.concat(',')?.concat(accept)?.split(',');
    if (acceptList?.indexOf(fileType) < 0) {
      message.error(`仅支持上传${typeMap[type]?.type?.concat(',')?.concat(accept)}格式文件`);
      return Upload.LIST_IGNORE;
    }
    return true;
  }

  if (type !== 'doc') {
    uploadProps.listType = 'picture-card';
  }

  if (type === 'video') {
    uploadProps.previewFile = () =>
      new Promise((resolve) => {
        resolve(videoImgCommon);
      });
  }

  let filePreviewUrl = null;
  const modalProps: any = {};
  if (type !== 'image' && filePath) {
    modalProps.width = 1260;
    modalProps.bodyStyle = { height: 600 };
    const trans = CryptoJS.enc.Utf8.parse(filePath);
    filePreviewUrl = `${PRIVIEW_SERVER}?url=${encodeURIComponent(
      CryptoJS.enc.Base64.stringify(trans),
    )}`;
  }

  return (
    <>
      <Upload
        {...uploadProps}
        beforeUpload={(file: any) => beforeUpload(file)}
        name="uploadFile"
        onChange={handleChange}
        onPreview={handlePreview}
        accept={typeMap[type]?.type?.concat(',').concat(accept)}
      >
        {maxNum !== undefined && fileList.length >= maxNum ? null : uploadButton}
      </Upload>
      {previewOpen &&
        (type === 'image' ? (
          <div style={{ display: 'none' }}>
            <AImage.PreviewGroup
              preview={{
                visible: previewOpen,
                current,
                onVisibleChange: (vis) => {
                  setPreviewOpen(vis);
                },
              }}
            >
              {fileList?.map((item: any, index: number) => (
                <AImage
                  key={index}
                  src={item?.url || downloadFile(item?.response?.data?.downloadUrl)}
                />
              ))}
            </AImage.PreviewGroup>
          </div>
        ) : (
          <Modal
            {...modalProps}
            maskClosable={false}
            keyboard={false}
            className="custom-upload-preview"
            open
            width={1260}
            title={realTitle}
            footer={null}
            onCancel={handleCancel}
          >
            {filePreviewUrl && (
              <iframe title="preview" src={filePreviewUrl} height="100%" width="100%" />
            )}
          </Modal>
        ))}
    </>
  );
};

export default UploadCustom;
