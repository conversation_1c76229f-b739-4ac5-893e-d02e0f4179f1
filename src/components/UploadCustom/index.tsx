import { batchDeletFile } from '@/services/index';
import { downloadFile } from '@/utils/commonFileFun';
import { UploadOutlined } from '@ant-design/icons';
import { Image as AImage, Button, Modal, Upload, message } from 'antd';
import ImgCrop from 'antd-img-crop';
import type { UploadProps } from 'antd/es/upload';
import CryptoJS from 'crypto-js';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import imagesImg from '../../assets/images/common/image.png';
import videoImg from '../../assets/images/common/video.png';
import videoImgCommon from '../../assets/images/video.png';
import './index.less';

/* eslint-disable */
const CusIframe: React.FC<any> = ({ filePreviewUrl }) => {
  return <iframe title="preview" src={filePreviewUrl} height="100%" width="100%" />;
};

const ViewImgs: React.FC<any> = ({ fileList, setPreviewOpen, current, previewOpen }) => {
  return (
    <AImage.PreviewGroup
      preview={{
        visible: previewOpen,
        current,
        onVisibleChange: (vis) => {
          setPreviewOpen(vis);
        },
      }}
    >
      {fileList?.map((item: any, index: number) => (
        <AImage key={index} src={item?.url || downloadFile(item?.response?.data?.downloadUrl)} />
      ))}
    </AImage.PreviewGroup>
  );
};

interface UploadCustomProps {
  type?: string;
  value?: any;
  onChange?: any;
  otherProps?: UploadProps;
  accept?: string;
  fileDir?: string;
  uploadChange?: any;
  buttonTitle?: string;
  buttonType?: 'primary' | 'default';
  maxNum?: number;
  dragable?: boolean;
  listType?: string;
  maxSize?: number; // 单位:M
  needCrop?: boolean;
  cropProps?: any;
}
const typeMap: any = {
  image: { type: '.png,.jpg,.jpeg,.gif' },
  video: { type: '.mp4' },
  doc: { type: '.doc,.pdf,.docx' },
  docMng: { type: '.doc,.pdf,.docx,.png,.jpg,.jpeg,.xls,.xlsx' },
  audio: { type: '.mp3' },
  app: { type: '.exe,.zip,.rar,.jpg' },
};
const UploadCustom: any = (props: UploadCustomProps, ref: any) => {
  const {
    type = 'image',
    value = [],
    onChange,
    otherProps,
    accept = '',
    fileDir = 'portal',
    buttonTitle = '选择文件',
    buttonType = 'default',
    uploadChange,
    maxNum,
    dragable,
    listType,
    maxSize,
    needCrop,
    cropProps,
  } = props;
  const [previewOpen, setPreviewOpen] = useState(false);
  const [realTitle, setRealTitle] = useState('');
  const [filePath, setFilePath] = useState('');
  const [fileList, setFileList] = useState<any[]>(value);
  const [current, setCurrent] = useState(0);
  const [removeList, setRemoveList] = useState<any[]>([]);
  const handleCancel = () => {
    setPreviewOpen(false);
  };

  function getIndex(item: any, file: any) {
    let result: boolean = false;
    if (item?.response?.data?.downloadUrl === file?.response?.data?.downloadUrl) {
      result = true;
    }
    if (item?.filePath && file?.filePath) {
      if (item?.filePath === file?.filePath) {
        result = true;
      }
    }
    return result;
  }

  const handlePreview = async (file: any) => {
    if (!file.filePath) {
      file.preview = downloadFile(file?.response?.data?.downloadUrl);
    } else {
      file.preview = file.url;
    }
    if (type === 'image') {
      const index = fileList.findIndex((item: any) => getIndex(item, file));
      setCurrent(index);
    }
    setFilePath(file.preview);
    // const re = new RegExp('(.zip|.Zip|.ZIP)$');
    // if (!re.test(file?.name)) {
    setPreviewOpen(true);
    // }
    setRealTitle(file?.name);
  };

  const handleChange: UploadProps['onChange'] = ({ file, fileList: newFileList, event }) => {
    // console.log('newFileList===', newFileList, event);
    if (uploadChange) {
      if (file?.status === 'uploading') {
        uploadChange(true);
      } else {
        uploadChange(false);
      }
    }
    setFileList(newFileList);
    onChange && onChange(newFileList, file, event);
  };

  const handleRemove = (file: any) => {
    const list = [...removeList, file];
    // console.log('handleRemove===', list);
    setRemoveList(list);
  };

  const remove = (flag?: boolean) => {
    if (!flag && removeList.length === 0) {
      return;
    }
    const arr: any = [];
    (flag ? [...fileList, ...removeList] : removeList).forEach((item) => {
      if (item.response) {
        arr.push(item.response?.data?.savePath);
      } else {
        arr.push(item.uid);
      }
    });
    if (arr?.length === 0) {
      return;
    }
    const params = {
      bucket: 'portal',
      objectNames: arr,
    };
    batchDeletFile(params);
  };

  useImperativeHandle(ref, () => ({
    remove,
  }));

  const uploadButton = (
    <>
      {['docMng', 'doc', 'app'].includes(type) ? (
        <Button type={buttonType}>{buttonTitle}</Button>
      ) : (
        <div>
          <img
            style={{ width: 32 }}
            src={type === 'image' ? imagesImg : videoImg}
            alt="upload type"
          />
        </div>
      )}
    </>
  );

  useEffect(() => {
    if (value?.length > 0 && fileList !== value) {
      setFileList(value);
    }
  }, [value]);

  const uploadProps: any = {
    // action: `${PREFIX}/api/simple-uploadFile?bucket=${fileDir || type}`,
    action: `${PREFIX_SERVER}/api/simple-uploadFile?bucket=${fileDir || type}`,
    fileList,
    showUploadList: {
      showPreviewIcon: true,
      showDownloadIcon: true,
      showRemoveIcon: true,
    },
    multiple: true,
    headers: {
      access_token: localStorage.getItem('jishan_token') || '',
    },
    ...otherProps,
  };

  function beforeUpload(file: any) {
    if (file.size === 0) {
      message.error('上传文件的内容不能为空');
      return Upload.LIST_IGNORE;
    }
    if (maxSize && file.size > maxSize * 1024 * 1024) {
      message.error(`文件大小不能超过${maxSize}M`);
      return Upload.LIST_IGNORE;
    }
    const fileType = file?.name?.substring(file?.name!.lastIndexOf('.'))?.toLowerCase();
    const acceptList = typeMap[type]?.type?.concat(',')?.concat(accept)?.split(',');
    if (acceptList?.indexOf(fileType) < 0) {
      message.error(`仅支持上传${typeMap[type]?.type?.concat(',')?.concat(accept)}格式文件`);
      return Upload.LIST_IGNORE;
    }
    return true;
  }

  if (type !== 'doc' && type !== 'docMng' && !listType) {
    uploadProps.listType = 'picture-card';
  }

  if (type === 'video') {
    uploadProps.previewFile = () =>
      new Promise((resolve) => {
        resolve(videoImgCommon);
      });
  }

  let filePreviewUrl = null;
  const modalProps: any = {};
  if (type !== 'image' && filePath) {
    modalProps.width = 1260;
    modalProps.bodyStyle = { height: 600 };
    const trans = CryptoJS.enc.Utf8.parse(filePath);
    filePreviewUrl = `${PRIVIEW_SERVER}?url=${encodeURIComponent(
      CryptoJS.enc.Base64.stringify(trans),
    )}`;
  }

  return (
    <>
      {dragable ? (
        <Upload.Dragger
          {...uploadProps}
          className="draggable-upload"
          beforeUpload={(file: any) => beforeUpload(file)}
          name="uploadFile"
          onChange={handleChange}
          onPreview={handlePreview}
          onRemove={handleRemove}
          accept={typeMap[type]?.type?.concat(',').concat(accept)}
        >
          {maxNum !== undefined && fileList.length >= maxNum ? null : (
            <div>
              <p className="ant-upload-drag-icon">
                <UploadOutlined />
              </p>
              <p className="ant-upload-text">点击或者文件拖拽到这里上传</p>
              <p className="ant-upload-hint">格式支持{typeMap[type]?.type}, 文件不超过50MB</p>
            </div>
          )}
        </Upload.Dragger>
      ) : (
        <ImgCrop
          rotationSlider
          modalWidth={cropProps?.width + 140}
          cropperProps={{
            cropSize: { width: cropProps?.width, height: cropProps?.height },
            mediaProps: {},
            style: { containerStyle: { height: cropProps?.height + 100 } },
            zoomSpeed: 1,
            restrictPosition: false,
          }}
          beforeCrop={() => !!(needCrop && type === 'image')}
        >
          <Upload
            {...uploadProps}
            beforeUpload={(file: any) => beforeUpload(file)}
            name="uploadFile"
            onChange={handleChange}
            onPreview={handlePreview}
            onRemove={handleRemove}
            accept={typeMap[type]?.type?.concat(',').concat(accept)}
          >
            {maxNum !== undefined && fileList.length >= maxNum ? null : uploadButton}
          </Upload>
        </ImgCrop>
      )}
      {previewOpen &&
        (type === 'image' ? (
          <div style={{ display: 'none' }}>
            <ViewImgs
              fileList={fileList}
              setPreviewOpen={setPreviewOpen}
              current={current}
              previewOpen={previewOpen}
            />
            {/* <AImage.PreviewGroup
                preview={{
                  visible: previewOpen,
                  current,
                  onVisibleChange: (vis) => {
                    setPreviewOpen(vis);
                  },
                }}
              >
                {fileList?.map((item: any, index: number) => (
                  <AImage key={index} src={item?.url || downloadFile(item?.response?.data?.downloadUrl)} />
                ))}
              </AImage.PreviewGroup> */}
          </div>
        ) : (
          <Modal
            {...modalProps}
            maskClosable={false}
            keyboard={false}
            className="custom-upload-preview"
            open
            width={1260}
            title={realTitle}
            footer={null}
            onCancel={handleCancel}
          >
            {filePreviewUrl && <CusIframe filePreviewUrl={filePreviewUrl} />}
          </Modal>
        ))}
    </>
  );
};
export default forwardRef<any, any>(UploadCustom);
