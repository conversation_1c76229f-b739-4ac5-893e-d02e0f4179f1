/* eslint-disable */
import { RightOutlined } from '@ant-design/icons';
import { useDeepCompareEffect } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import React, { useState } from 'react';
import dangjianBanner from '../../../assets/images/home/<USER>';
import morentupian from '../../../assets/images/morentupian.png';
import './index.less';

const GraphicCards: React.FC<any> = (props) => {
  const { name, params, service } = props;

  const [list, setList] = useState<any[]>([]);

  const getList = () => {
    service &&
      service({ pageNo: 1, pageSize: 3, ...params }).then((res: any) => {
        const list = res.data.data || [];
        list.forEach((item: any) => {
          if (item.tgpUrls) {
            const tgpUrls = item.tgpUrls.split(',');
            item.firstTgpUrl = `${PREFIX_FILE}${PREFIX_SERVER}${tgpUrls[0]}`;
          } else {
            item.firstTgpUrl = morentupian;
          }
        });
        setList(list);
      });
  };

  useDeepCompareEffect(() => {
    getList();
  }, [params]);

  return (
    <div className="portalComponents-graphicCards">
      <img className="banner-img" src={dangjianBanner} alt="" />

      <div className="block-box">
        <div className="block-top">
          <div className="block-head">
            <div className="block-title">
              <span className="big">{name.charAt(0)}</span>
              <span className="normal">{name.charAt(1)}</span>
              <span className="normal">{`${name.charAt(2)}${name.charAt(3)}`}</span>
            </div>
            <div className="block-more" onClick={() => history.push(`/column?id=${params.moduleId}`)}>
              更多
              <RightOutlined />
            </div>
          </div>
        </div>

        <div className="block-cont">
          {list.map((item, index) => {
            return (
              <>
                {index < 3 && (
                  <div key={item.textId} className="card-item">
                    <div className="card-cont">
                      <div className="card-title ellipsis">{item.textTitle}</div>
                      <div className="card-desc ellipsis-multiline">{item.textSummary}</div>
                      <div className="card-more" onClick={() => history.push(`/columnDetail?mid=${params.moduleId}&tid=${item.textId}`)}>
                        <span>更多</span>
                      </div>
                    </div>
                    <img
                      className="card-img"
                      src={item.firstTgpUrl}
                      alt=""
                      onClick={() => history.push(`/columnDetail?mid=${params.moduleId}&tid=${item.textId}`)}
                    />
                  </div>
                )}
              </>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default GraphicCards;
