.portalComponents-graphicCards {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 865px;

  .banner-img {
    width: 100%;
    height: 500px;
  }

  .block-box {
    position: absolute;
    top: 40px;
    width: 1260px;

    .block-top {
      display: flex;
      justify-content: center;
      background: url('../../../assets/images/home/<USER>') no-repeat left 80px;

      .block-head {
        display: flex;
        height: 105px;
        background: url('../../../assets/images/home/<USER>') no-repeat left top;

        .block-title {
          padding-top: 42px;
          padding-left: 15px;
          color: #fff;
          font-weight: 500;
          font-size: 32px;
          font-family: Alibaba-PuHuiTi-M;
          letter-spacing: 5px;

          .big {
            font-weight: 500;
            font-size: 60px;
            font-family: Alibaba-PuHuiTi-M;
            letter-spacing: 5px;
          }

          .green {
            color: #00b39c;
          }
        }

        .block-more {
          display: flex;
          align-items: end;
          margin-left: 8px;
          padding-bottom: 2px;
          color: #92c2c8;
          font-weight: 400;
          font-size: 14px;
          font-family: Alibaba-PuHuiTi-R;
          cursor: pointer;

          .anticon {
            margin-bottom: 1px;
            margin-left: 2px;
            font-size: 12px;
          }
        }
      }
    }

    .block-cont {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      padding-top: 55px;

      .card-item {
        display: flex;
        flex-direction: column;
        .card-img {
          width: 400px;
          height: 266px;
        }

        .card-cont {
          width: 400px;
          height: 300px;
          padding: 32px 20px 20px 20px;
          background: #ffaf00;

          .card-title {
            color: #ffffff;
            font-weight: 500;
            font-size: 24px;
            font-family: Alibaba-PuHuiTi-M;
          }

          .card-desc {
            height: 140px;
            margin-top: 20px;
            color: #ffffff;
            font-weight: 400;
            font-size: 14px;
            font-family: Alibaba-PuHuiTi-R;
            line-height: 28px;
          }

          .card-more {
            display: flex;
            justify-content: flex-end;
            margin-top: 30px;
            cursor: pointer;

            span {
              display: inline-block;
              width: 80px;
              height: 28px;
              color: #ffffff;
              font-weight: 400;
              font-size: 16px;
              font-family: Alibaba-PuHuiTi-R;
              line-height: 28px;
              text-align: center;
              background: #e49915;
              border-radius: 14px;
            }
          }
        }

        &:first-child {
          flex-direction: column-reverse;
          padding-top: 33px;
          .card-cont {
            background: #197eef;

            .card-more {
              span {
                background: #1563e4;
              }
            }
          }
        }

        &:last-child {
          flex-direction: column-reverse;
          padding-top: 33px;
          .card-cont {
            background: #ee2929;

            .card-more {
              span {
                background: #db1212;
              }
            }
          }
        }
      }
    }
  }

  .ellipsis {
    /* 隐藏超出容器的文本 */
    overflow: hidden;
    /* 确保文本不会换行 */
    white-space: nowrap;
    /* 当文本溢出时，显示省略号 */
    text-overflow: ellipsis;
  }

  .ellipsis-multiline {
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 5; /* 设置为想要的行数 */
  }
}
