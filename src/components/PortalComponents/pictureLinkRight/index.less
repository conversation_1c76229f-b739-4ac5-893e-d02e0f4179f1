.portalComponents-pictureLinkRight {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  height: 100%;
  // padding-right: 40px;
  .block-cont {
    position: relative;
    width: 100%;
    height: 100%;
    cursor: pointer;

    img {
      width: 100%;
      height: 100%;
    }

    .cont-box {
      position: absolute;
      top: 0;
      left: 0;
      display: flex;
      flex-direction: column;
      justify-content: center;
      width: 620px;
      height: 160px;
      padding-left: 60px;

      .cont-title {
        color: #ffffff;
        font-weight: 500;
        font-size: 36px;
        font-family: Alibaba-PuHuiTi-M;
        font-style: italic;
        letter-spacing: 8px;
        text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.5);
      }

      .link-enter {
        display: flex;
        align-items: center;
        margin-top: 22px;

        .enter-text {
          color: #04f4fc;
          font-weight: 400;
          font-size: 16px;
          font-family: Alibaba-PuHuiTi-R;
        }

        .anticon {
          margin-left: 8px;
          color: #04f4fc;
        }
      }
    }
  }
}
