import { PlayCircleFilled } from '@ant-design/icons';
import { history } from '@umijs/max';
import React from 'react';
import bannerImg from '../../../assets/images/home/<USER>';
import './index.less';

const PictureLinkLeft: React.FC<any> = (props) => {
  const { params } = props;

  return (
    <div className="portalComponents-pictureLinkLeft">
      <div className="block-cont" onClick={() => history.push(`/column?id=${params.columnId}`)}>
        <img src={bannerImg} alt="" />
        <div className="cont-box">
          <div className="cont-title">{props.name}</div>
          <div className="link-enter">
            <span className="enter-text">点击进入</span>
            <PlayCircleFilled />
          </div>
        </div>
      </div>
    </div>
  );
};

export default PictureLinkLeft;
