.portalComponents-articlesList {
  background: #fff;

  .block-top {
    padding: 0 24px;

    .block-head {
      display: flex;
      height: 105px;
      background: url('../../../assets/images/home/<USER>') no-repeat left top;

      .block-title {
        padding-top: 42px;
        padding-left: 15px;
        color: #0076d7;
        font-weight: 500;
        font-size: 32px;
        font-family: Alibaba-PuHuiTi-M;
        letter-spacing: 5px;

        .big {
          font-weight: 500;
          font-size: 60px;
          font-family: Alibaba-PuHuiTi-M;
          letter-spacing: 5px;
        }

        .green {
          color: #00b39c;
        }
      }

      .block-more {
        display: flex;
        align-items: end;
        margin-left: 8px;
        padding-bottom: 2px;
        color: #92c2c8;
        font-weight: 400;
        font-size: 14px;
        font-family: Alibaba-PuHuiTi-R;
        cursor: pointer;

        .anticon {
          margin-bottom: 1px;
          margin-left: 2px;
          font-size: 12px;
        }
      }
    }
  }

  .block-cont {
    padding: 10px 40px 40px 40px;
    .article-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 30px 0;
      color: #333333;
      font-weight: 500;
      font-size: 14px;
      font-family: PingFangSC-Medium;
      cursor: pointer;

      .item-title {
        position: relative;
        display: inline-block;
        width: calc(100% - 170px);
        padding-left: 18px;

        &::before {
          position: absolute;
          top: 6px;
          left: 0;
          display: inline-block;
          width: 4px;
          height: 4px;
          background: #0075de;
          border-radius: 50%;
          content: ' ';
        }
      }

      .item-time {
        display: inline-block;
        width: 160px;
        text-align: right;
      }
    }
  }

  .ellipsis {
    /* 隐藏超出容器的文本 */
    overflow: hidden;
    /* 确保文本不会换行 */
    white-space: nowrap;
    /* 当文本溢出时，显示省略号 */
    text-overflow: ellipsis;
  }
}
