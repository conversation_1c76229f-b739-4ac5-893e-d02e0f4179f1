import { RightOutlined } from '@ant-design/icons';
import { useDeepCompareEffect } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import React, { useState } from 'react';
import './index.less';

const TodoList: React.FC<any> = (props) => {
  const { name, params, service } = props;

  const [list, setList] = useState<any[]>([]);

  const getList = () => {
    service &&
      service({ pageNo: 1, pageSize: 6, ...params, orgId: userInfo.orgId }).then((res: any) => {
        if (res?.code === '200') {
          setList(res.data.data || []);
        }
      });
  };

  useDeepCompareEffect(() => {
    getList();
  }, [params]);

  return (
    <div className="portalComponents-articlesList">
      <div className="block-top">
        <div className="block-head">
          <div className="block-title">
            <span className="big">{name.charAt(0)}</span>
            <span className="normal">{name.charAt(1)}</span>
            <span className="normal green">{`${name.charAt(2)}${name.charAt(3)}`}</span>
          </div>
          <div className="block-more" onClick={() => history.push(`/column?id=${params.columnId}`)}>
            更多
            <RightOutlined />
          </div>
        </div>
      </div>

      <div className="block-cont">
        {list.map((item: any, index: number) => {
          return (
            <>
              {index < 6 && (
                <div
                  key={index}
                  className="article-item"
                  onClick={() =>
                    history.push(`/columnDetail?mid=${params.columnId}&tid=${item.textId}`)
                  }
                >
                  <span className="item-title ellipsis">{item.textTitle}</span>
                  <span className="item-time">[{item.createTime}]</span>
                </div>
              )}
            </>
          );
        })}
      </div>
    </div>
  );
};

export default TodoList;
