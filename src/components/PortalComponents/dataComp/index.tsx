import { getDataConfDetail } from '@/services/systemMaintenance/module';
import { generateOptions, mapPieData, setValueByPath } from '@/utils/commonFunction';
import { useDeepCompareEffect } from '@ant-design/pro-components';
import * as echarts from 'echarts';
import React, { useEffect, useRef, useState } from 'react';
import './index.less';

/**
 * 数据组件（饼图、柱状图、折线图等）
 * @param props
 * @returns
 */
const DataComp: React.FC<any> = (props) => {
  const { name, params, service } = props;
  const chartRef = useRef<HTMLDivElement>(null);
  const [chartInstance, setChartInstance] = useState<echarts.ECharts | null>(null);
  const [echartsConfig, setEchartsConfig] = useState<any>({});
  async function getDetail() {
    const res = await getDataConfDetail({ id: params?.moduleId });
    if (res?.code === '200') {
      getList(res?.data);
    }
  }
  const getList = (details: any) => {
    service &&
      service({ ...params, orgId: userInfo.orgId }).then((res: any) => {
        if (res?.code === '200') {
          try {
            let config = JSON.parse(details?.compItem?.echartsConfig);
            const filedMap = config;
            if (params?.styleCode === 'pie') {
              details?.fieldMapping?.forEach((item: any) => {
                setValueByPath(filedMap, item?.styleFields, item?.interfaceFields);
              });
              config = mapPieData(res, filedMap);
            } else {
              details?.fieldMapping?.forEach((item: any) => {
                setValueByPath(filedMap, item?.styleFields, item?.interfaceFields);
              });
              config = generateOptions(res, filedMap);
            }
            setEchartsConfig(config);
            // chartInstance.setOption(config);
          } catch (error) {
            console.error('Invalid ECharts configuration:', error);
            // chartInstance.setOption({});
          }
        }
      });
  };

  useEffect(() => {
    if (chartRef.current) {
      const chart = echarts.init(chartRef.current);
      setChartInstance(chart);
      return () => {
        chart.dispose();
      };
    }
    return () => {};
  }, []);

  useDeepCompareEffect(() => {
    getDetail();
  }, [params]);

  useEffect(() => {
    if (chartInstance) {
      try {
        chartInstance.setOption(echartsConfig);
      } catch (error) {
        console.error('Invalid ECharts configuration:', error);
        chartInstance.setOption({});
      }
    }
  }, [chartInstance, echartsConfig]);

  return (
    <div className="portalComponents-dataComp">
      <div className="block-top">
        <div className="block-head">
          <div className="block-title">
            <span className="big">{name.charAt(0)}</span>
            <span className="normal">{name.charAt(1)}</span>
            <span className="normal green">{`${name.charAt(2)}${name.charAt(3)}`}</span>
          </div>
        </div>
      </div>

      <div className="block-cont">
        <div className="sub-container">
          <div ref={chartRef} style={{ width: '100%', height: '200px' }} />
        </div>
      </div>
    </div>
  );
};

export default DataComp;
