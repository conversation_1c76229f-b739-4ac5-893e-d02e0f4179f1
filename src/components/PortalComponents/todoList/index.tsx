import { todoList } from '@/services/home';
import useDict from '@/utils/useDict';
import { useDeepCompareEffect } from '@ant-design/pro-components';
import moment from 'moment';
import React, { useState } from 'react';
import './index.less';

const ArticlesList: React.FC<any> = (props) => {
  const { name, params } = props;

  const [list, setList] = useState<any[]>([]);
  const [totalList, setTotalList] = useState<any[]>([]);
  const todoType = useDict('todo_type').value;
  const [current, setCurrent] = useState<any>({});

  const getList = () => {
    todoList({
      ...params,
      expTime: moment().format('YYYY-MM-DD HH:mm:ss'),
    }).then((res: any) => {
      if (res?.code === '200') {
        const tempList = todoType?.map((item) => ({
          ...item,
          value: res.data?.total?.[item.dictCode],
        }));
        const first = tempList[0];
        setList(res?.data?.todoList);
        setTotalList(tempList || []);
        setCurrent(first);
      }
    });
  };

  useDeepCompareEffect(() => {
    getList();
  }, [todoType]);

  return (
    <div className="portalComponents-todoList">
      <div className="block-top">
        <div className="block-head">
          <div className="block-title">
            <span className="big">{name.charAt(0)}</span>
            <span className="normal">{name.charAt(1)}</span>
            <span className="normal green">{`${name.charAt(2)}${name.charAt(3)}`}</span>
          </div>
        </div>
      </div>

      <div className="block-cont">
        <div className="type-container">
          {totalList.map((item) => (
            <div
              key={item.id}
              className={`todo-item todo-${item.dictCode} ${
                item?.dictCode === current?.dictCode ? 'active' : ''
              }`}
              onClick={() => setCurrent(item)}
            >
              <div className="todo-category">
                <span className="todo-title">{item.dictValue}</span>
                <span className="todo-count">{item.value}</span>
              </div>
            </div>
          ))}
        </div>
        <div className="list-container">
          {list
            ?.filter((item: any) => item.todoType === current?.dictCode)
            .map((item, idx) => (
              <div key={idx} className="todo-card">
                <div className="todo-details">
                  <span className="todo-type">【{current?.dictValue}】</span>
                  {item?.todoTitle}，{item?.todoContent}
                  <span
                    className="todo-action"
                    onClick={() => {
                      window.open(item?.todoUrl);
                    }}
                  >
                    处置
                  </span>
                </div>
              </div>
            ))}
        </div>
      </div>
    </div>
  );
};

export default ArticlesList;
