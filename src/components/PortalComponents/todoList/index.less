.portalComponents-todoList {
  height: 100%;
  background: #fff;
  .block-top {
    padding: 0 24px;

    .block-head {
      display: flex;
      height: 105px;
      background: url('../../../assets/images/home/<USER>') no-repeat left top;

      .block-title {
        padding-top: 42px;
        padding-left: 15px;
        color: #0076d7;
        font-weight: 500;
        font-size: 32px;
        font-family: Alibaba-PuHuiTi-M;
        letter-spacing: 5px;

        .big {
          font-weight: 500;
          font-size: 60px;
          font-family: Alibaba-PuHuiTi-M;
          letter-spacing: 5px;
        }

        .green {
          color: #00b39c;
        }
      }

      .block-more {
        display: flex;
        align-items: end;
        margin-left: 8px;
        padding-bottom: 2px;
        color: #92c2c8;
        font-weight: 400;
        font-size: 14px;
        font-family: Alibaba-PuHuiTi-R;
        cursor: pointer;

        .anticon {
          margin-bottom: 1px;
          margin-left: 2px;
          font-size: 12px;
        }
      }
    }
  }

  .block-cont {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    height: calc(100% - 105px);
    padding: 10px 40px 40px 40px;
    .type-container {
      display: flex;
      flex-direction: column;
      justify-content: space-evenly;
      width: 30%;
      height: 100%;
      .type-item {
        width: 80%;
        height: 30px;
        line-height: 30px;
        text-align: center;
        border: 1px solid #d9d9d9;
        box-shadow: 5px 3px 3px rgba(0, 0, 0, 0.3);
      }
    }
    .list-container {
      width: 70%;
      height: 100%;
      overflow-y: auto;
    }
    .article-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 30px 0;
      color: #333333;
      font-weight: 500;
      font-size: 14px;
      font-family: PingFangSC-Medium;
      box-shadow: 5px 3px 3px rgba(0, 0, 0, 0.3);
      cursor: pointer;

      .item-title {
        position: relative;
        display: inline-block;
        width: calc(100% - 170px);
        padding-left: 18px;

        &::before {
          position: absolute;
          top: 6px;
          left: 0;
          display: inline-block;
          width: 4px;
          height: 4px;
          background: #0075de;
          border-radius: 50%;
          content: ' ';
        }
      }

      .item-time {
        display: inline-block;
        width: 160px;
        text-align: right;
      }
    }
  }

  .ellipsis {
    /* 隐藏超出容器的文本 */
    overflow: hidden;
    /* 确保文本不会换行 */
    white-space: nowrap;
    /* 当文本溢出时，显示省略号 */
    text-overflow: ellipsis;
  }
}

// App.less
@warning-color: #a0e75a;
@info-color: #5bc0de;
@process-color: #f0ad4e;

.todo-container {
  max-width: 400px;
  margin: 20px auto;
  padding: 20px;
  font-family: 'Segoe UI', system-ui;
}

.todo-header {
  margin-bottom: 30px;
  padding-bottom: 10px;
  color: #2c3e50;
  font-weight: 600;
  font-size: 28px;
  border-bottom: 2px solid #ecf0f1;
}

.todo-item {
  margin-bottom: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;

  &:hover {
    transform: translateY(-2px);
  }

  &.handled {
    opacity: 0.6;
    filter: saturate(0.5);
  }
}

.todo-category {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 15px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);

  .todo-title {
    font-weight: 600;
    font-size: 16px;
    letter-spacing: 0.5px;
  }
}

.todo-count {
  padding: 4px 10px;
  font-weight: 700;
  font-size: 12px;
  background: #f8f9fa;
  border-radius: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.todo-card {
  position: relative;
  margin: 8px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;

  &::before {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 3px;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.2) 100%);
    content: '';
  }
}

.todo-details {
  display: flex;
  gap: 8px;
  align-items: center;
  color: #34495e;
  font-size: 14px;
}

.todo-type {
  flex-shrink: 0;
  color: #e74c3c;
  font-weight: 500;
}

.todo-action {
  margin-left: auto;
  padding: 4px 12px;
  color: #3498db;
  font-weight: 600;
  background: none;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

// 颜色主题
.todo-alert {
  background: linear-gradient(145deg, lighten(@warning-color, 18%), @warning-color);
  &.active {
    background: linear-gradient(145deg, lighten(@warning-color, 10%), @warning-color);
  }
  .todo-count {
    color: darken(@warning-color, 30%);
  }
}

.todo-event {
  background: linear-gradient(145deg, lighten(@info-color, 18%), @info-color);
  &.active {
    background: linear-gradient(145deg, lighten(#5bc0de, 10%), #5bc0de);
  }
  .todo-count {
    color: darken(@info-color, 30%);
  }
}

.todo-process {
  background: linear-gradient(145deg, lighten(@process-color, 18%), @process-color);
  &.active {
    background: linear-gradient(145deg, lighten(#f0ad4e, 10%), #f0ad4e);
  }
  .todo-count {
    color: darken(@process-color, 30%);
  }
}
