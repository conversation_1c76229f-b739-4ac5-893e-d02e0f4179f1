import { downloadFile } from '@/utils/commonFileFun';
import { DoubleRightOutlined, RightOutlined } from '@ant-design/icons';
import { useDeepCompareEffect } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { Carousel } from 'antd';
import React, { useState } from 'react';
import morentupian from '../../../assets/images/morentupian.png';
import './index.less';

/**
 * 一图多文
 * @param props
 * @returns
 */
const PictureAndArticles: React.FC<any> = (props) => {
  const { name, params, service } = props;

  const [carouselList, setCarouselList] = useState<any[]>([]);
  const [list, setList] = useState<any[]>([]);
  const [current, setCurrent] = useState(0);
  const getList = () => {
    service &&
      service({ pageNo: 1, pageSize: 11, ...params, orgId: userInfo.orgId }).then((res: any) => {
        if (res?.code === '200') {
          const arr = res.data.data || [];
          const picList = arr.slice(0, 4);
          picList.forEach((item: any) => {
            if (item.tgpUrl) {
              item.tgpUrl = downloadFile(item.tgpUrl);
            } else {
              item.tgpUrl = morentupian;
            }
          });
          setCarouselList(picList);
          setList(arr.slice(0, 7));
        }
      });
  };

  useDeepCompareEffect(() => {
    getList();
  }, [params]);

  return (
    <div className="portalComponents-pictureAndArticles">
      <div className="block-top">
        <div className="block-head">
          <div className="block-title">
            <span className="big">{name.charAt(0)}</span>
            <span className="normal">{name.charAt(1)}</span>
            <span className="normal green">{`${name.charAt(2)}${name.charAt(3)}`}</span>
          </div>
          <div className="block-more" onClick={() => history.push(`/column?id=${params.columnId}`)}>
            更多
            <RightOutlined />
          </div>
        </div>
      </div>

      <div className="block-cont">
        <div className="pic-box">
          <Carousel
            autoplay
            afterChange={(value) => {
              setCurrent(value);
            }}
            dots={{ className: 'cusotom-dots' }}
          >
            {carouselList.map((item) => {
              return (
                <div
                  key={item.textId}
                  className="banner-item"
                  onClick={() =>
                    history.push(`/columnDetail?mid=${params.columnId}&tid=${item.textId}`)
                  }
                >
                  <img src={item.tgpUrl} alt="" />
                  <div className="banner-bottom">
                    <div className="banner-desc">
                      <div className="desc-title ellipsis">{item.textTitle}</div>
                      <div className="desc-text ellipsis">{item.textSummary}</div>
                    </div>
                  </div>
                </div>
              );
            })}
          </Carousel>
        </div>

        <div className="article-box">
          {list.map((item: any, index: number) => {
            return (
              <>
                {index === current && (
                  <div key={index} className="article-first-item">
                    <div className="item-title ellipsis" title={item.textTitle}>
                      {item.textTitle}
                    </div>
                    <div className="item-desc ellipsis">{item.textSummary}</div>
                    <div
                      className="item-more"
                      onClick={() =>
                        history.push(`/columnDetail?mid=${params.columnId}&tid=${item.textId}`)
                      }
                    >
                      更多详情
                      <DoubleRightOutlined />
                    </div>
                  </div>
                )}
                {index !== current && index < 7 && (
                  <div
                    key={index}
                    className="article-item ellipsis"
                    onClick={() =>
                      history.push(`/columnDetail?mid=${params.columnId}&tid=${item.textId}`)
                    }
                  >
                    {item.textTitle}
                  </div>
                )}
              </>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default PictureAndArticles;
