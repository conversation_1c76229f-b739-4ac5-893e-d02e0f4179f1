.portalComponents-pictureAndArticles {
  .block-top {
    padding: 0 24px;

    .block-head {
      display: flex;
      height: 105px;
      background: url('../../../assets/images/home/<USER>') no-repeat left top;

      .block-title {
        padding-top: 42px;
        padding-left: 15px;
        color: #0076d7;
        font-weight: 500;
        font-size: 32px;
        font-family: Alibaba-PuHuiTi-M;
        letter-spacing: 5px;

        .big {
          font-weight: 500;
          font-size: 60px;
          font-family: Alibaba-PuHuiTi-M;
          letter-spacing: 5px;
        }

        .green {
          color: #00b39c;
        }
      }

      .block-more {
        display: flex;
        align-items: end;
        margin-left: 8px;
        padding-bottom: 2px;
        color: #92c2c8;
        font-weight: 400;
        font-size: 14px;
        font-family: Alibaba-PuHuiTi-R;
        cursor: pointer;

        .anticon {
          margin-bottom: 1px;
          margin-left: 2px;
          font-size: 12px;
        }
      }
    }
  }

  .block-cont {
    display: flex;
    justify-content: space-between;
    margin-top: 80px;
    padding: 40px;
    background: #fff;

    .pic-box {
      width: 65%;
      height: 500px;
      margin-top: -88px;

      .banner-item {
        position: relative;
        width: 100%;
        height: 500px;

        img {
          width: 100%;
          height: 500px;
          object-fit: fill;
        }

        .banner-bottom {
          position: absolute;
          bottom: 0;
          width: 100%;
          height: 100px;
          padding: 20px;
          background-image: linear-gradient(
            90deg,
            rgba(49, 100, 246, 0.9) 0%,
            rgba(92, 158, 234, 0.9) 95%
          );

          .banner-desc {
            width: 80%;
            color: #fffff4;

            .desc-title {
              font-weight: 500;
              font-size: 20px;
              font-family: Alibaba-PuHuiTi-M;
            }

            .desc-text {
              margin-top: 5px;
              font-weight: 400;
              font-size: 14px;
              font-family: Alibaba-PuHuiTi-R;
            }
          }
        }
      }
      .slick-dots-bottom {
        right: 20px;
        bottom: 15px;
      }
      .cusotom-dots {
        justify-content: flex-end;

        li.slick-active::after {
          width: 20px !important;
          height: 4px !important;
          background: #3164f6;
          border-radius: 0;
        }
        li {
          right: 10px;
          width: 20px;
          height: 4px;
          background: #fff;
          border-radius: 0;
        }
        li.slick-active {
          width: 20px;
          height: 4px !important;
          background: #3164f6 !important;
        }
      }
    }

    .article-box {
      width: calc(35% - 30px);

      .article-first-item {
        margin-bottom: 20px;
        padding-top: 20px;
        padding-bottom: 20px;
        border-top: 1px rgba(214, 214, 214, 1) dashed;
        border-bottom: 1px rgba(214, 214, 214, 1) dashed;
        &:first-child {
          border-top: none;
        }
        &:last-child {
          border-bottom: none;
        }
        .item-title {
          margin-bottom: 15px;
          color: #0076d7;
          font-weight: 500;
          font-size: 20px;
          font-family: PingFangSC-Medium;
        }
        .item-desc {
          margin-bottom: 15px;
          color: #666666;
          font-weight: 400;
          font-size: 14px;
          font-family: Alibaba-PuHuiTi-R;
        }

        .item-more {
          color: #ffbf00;
          font-weight: 400;
          font-size: 14px;
          font-family: Alibaba-PuHuiTi-R;
          cursor: pointer;

          .anticon {
            margin-bottom: 1px;
            margin-left: 2px;
            font-size: 12px;
          }
        }
      }

      .article-item {
        position: relative;
        margin: 30px 0;
        padding-left: 24px;
        color: #333333;
        font-weight: 500;
        font-size: 14px;
        font-family: PingFangSC-Medium;
        cursor: pointer;

        &::before {
          position: absolute;
          top: 3px;
          left: 0px;
          display: inline-block;
          width: 12px;
          height: 12px;
          background: #e7ecfb;
          border: 1px solid rgba(0, 118, 215, 1);
          border-radius: 50%;
          content: ' ';
        }
      }
    }
  }

  .ellipsis {
    /* 隐藏超出容器的文本 */
    overflow: hidden;
    /* 确保文本不会换行 */
    white-space: nowrap;
    /* 当文本溢出时，显示省略号 */
    text-overflow: ellipsis;
  }
}
