import { UnorderedListOutlined } from '@ant-design/icons';
import { useDeepCompareEffect } from '@ant-design/pro-components';
import { Flex, Tooltip } from 'antd';
import React, { useState } from 'react';
import './index.less';

/**
 * 数据组件（饼图、柱状图、折线图等）
 * @param props
 * @returns
 */
const DigitalApp: React.FC<any> = (props) => {
  const { name, params } = props;
  const [linkList, setLinkList] = useState<any[]>([]);
  const getList = () => {
    setLinkList([
      {
        name: '监测预警子系统',
      },
      {
        name: '监督管理子系统',
      },
      {
        name: '指挥救援子系统',
      },
    ]);
  };

  function jumpLink(record: any) {
    const current = (window as any).systems?.find((item: any) => item?.code === record?.sysCode);
    if (record.menuUrl?.startsWith('http')) {
      window.open(record.menuUrl);
    } else {
      window.open(
        `${current?.url?.split('|')?.[0]}${
          record.menuUrl?.startsWith('/') ? record.menuUrl : `/${record.menuUrl}`
        }?token=${localStorage.getItem('jishan_token')}`,
      );
    }
  }

  useDeepCompareEffect(() => {
    getList();
  }, [params]);

  return (
    <div className="portalComponents-commonLink">
      <div className="block-top">
        <div className="block-head" style={{ display: 'flex', justifyContent: 'space-between' }}>
          <div className="block-title">
            <span className="big">{name.charAt(0)}</span>
            <span className="normal">{name.charAt(1)}</span>
            <span className="normal green">{`${name.charAt(2)}${name.charAt(3)}`}</span>
          </div>
        </div>
      </div>

      <div className="block-cont">
        <Flex wrap gap="20px" style={{ marginTop: 20 }}>
          {linkList?.map((item: any, _i) => (
            <Tooltip
            // title={`${item?.name}(${
            //   (window as any).systems?.find((system: any) => system?.code === item?.sysCode)
            //     ?.name || ''
            // })`}
            >
              <Flex
                onClick={() => jumpLink(item)}
                style={{ width: 'calc((100% - 80px) / 3)', cursor: 'pointer' }}
                justify="center"
                align="center"
                key={_i}
              >
                <UnorderedListOutlined />
                <span style={{ marginLeft: 10 }}>{item?.name}</span>
              </Flex>
            </Tooltip>
          ))}
        </Flex>
      </div>
    </div>
  );
};

export default DigitalApp;
