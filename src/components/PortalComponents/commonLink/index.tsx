import { getMenuTreeBySysCode, saveUseLink, useLinkList } from '@/services/home';
import { treeToList } from '@/utils/treeUtil';
import { SearchOutlined, UnorderedListOutlined } from '@ant-design/icons';
import { useDeepCompareEffect } from '@ant-design/pro-components';
import { Button, Checkbox, Drawer, Flex, Input, message, Tooltip } from 'antd';
import React, { useEffect, useState } from 'react';
import './index.less';

/**
 * 数据组件（饼图、柱状图、折线图等）
 * @param props
 * @returns
 */
const DataComp: React.FC<any> = (props) => {
  const { name, params } = props;
  const [showEdit, setShowEdit] = useState(false);
  const [treeList, setTreeList] = useState([]);
  const [showList, setShowList] = useState([]);
  const [linkList, setLinkList] = useState([]);
  const [checkedKeys, setCheckedKeys] = useState<any>([]);
  const getList = () => {
    useLinkList({ ...params, orgId: userInfo.orgId }).then((res: any) => {
      if (res?.code === '200') {
        setLinkList(res?.data || []);
        setCheckedKeys(res?.data?.map((item: any) => (item?.menuId ? Number(item?.menuId) : 0)));
      }
    });
  };

  async function getMenuTree() {
    const res = await getMenuTreeBySysCode({
      systemCodes:
        'portal-manage,jishan_workbench,hbnsbd_screen,12328-temp-dev,qwxt,bmp-loca,igs,easygetPre',
      // || (window as any).systems?.map((item: any) => item?.code)?.join(','),
    });
    if (res?.code === '200') {
      // const tempTree = getLeafNodes(res?.data || [], 'parentId', 'menuId');
      res?.data?.forEach((item: any) => {
        item.leafNodes = treeToList(item.children, 'children')?.filter(
          (item: any) => item?.menuType === 'module' || item?.menuType === 'link',
        );
      });
      setTreeList(res?.data || []);
      setShowList(res?.data || []);
    }
  }

  function filterName(value: string) {
    const tempList: any = [];
    if (!value) {
      setShowList(treeList);

      return;
    }
    let childList: any[] = [];
    treeList?.forEach((item: any) => {
      childList = [];
      item?.leafNodes?.forEach((child: any) => {
        if (child?.name?.includes(value)) {
          childList.push(child);
        }
      });
      if (childList?.length > 0) {
        tempList.push({ ...item, leafNodes: childList });
      }
    });
    setShowList(tempList);
  }

  async function onSave() {
    const tempList = treeList?.map((item: any) => item?.leafNodes);
    const allLeafNodes: any = [];
    tempList.forEach((item) => {
      allLeafNodes.push(...item);
    });
    const links = allLeafNodes
      ?.filter((item: any) => checkedKeys.indexOf(item?.menuId) >= 0)
      ?.map((item: any) => ({
        ...item,
        menuUrl: item?.menuType === 'link' ? item?.url : item.path,
      }));
    const res = await saveUseLink({
      links,
    });
    if (res?.code === '200') {
      getList();
      setShowEdit(false);
      message.success('保存成功');
    }
  }

  function jumpLink(record: any) {
    const current = (window as any).systems?.find((item: any) => item?.code === record?.sysCode);
    if (record.menuUrl?.startsWith('http')) {
      window.open(record.menuUrl);
    } else {
      window.open(
        `${current?.url?.split('|')?.[0]}${
          record.menuUrl?.startsWith('/') ? record.menuUrl : `/${record.menuUrl}`
        }?token=${localStorage.getItem('jishan_token')}`,
      );
    }
  }

  useEffect(() => {
    getMenuTree();
  }, []);

  useDeepCompareEffect(() => {
    getList();
  }, [params]);

  return (
    <div className="portalComponents-commonLink">
      <div className="block-top">
        <div className="block-head" style={{ display: 'flex', justifyContent: 'space-between' }}>
          <div className="block-title">
            <span className="big">{name.charAt(0)}</span>
            <span className="normal">{name.charAt(1)}</span>
            <span className="normal green">{`${name.charAt(2)}${name.charAt(3)}`}</span>
          </div>
          <div
            onClick={() => setShowEdit(!showEdit)}
            className="block-title"
            style={{ fontSize: 16, letterSpacing: 0, cursor: 'pointer' }}
          >
            编辑
          </div>
        </div>
      </div>

      <div className="block-cont">
        <Flex wrap gap="20px" style={{ marginTop: 20 }}>
          {linkList?.map((item: any, _i) => (
            <Tooltip
              title={`${item?.menuName}(${
                (window as any).systems?.find((system: any) => system?.code === item?.sysCode)
                  ?.name || ''
              })`}
            >
              <Flex
                onClick={() => jumpLink(item)}
                style={{ width: 'calc((100% - 80px) / 5)', cursor: 'pointer' }}
                vertical
                justify="center"
                align="center"
                key={_i}
              >
                <div>
                  <UnorderedListOutlined />
                </div>
                <div>{item?.menuName}</div>
              </Flex>
            </Tooltip>
          ))}
        </Flex>
      </div>
      {showEdit && (
        <Drawer
          title="常用链接选择"
          size="large"
          zIndex={99999}
          rootClassName="commonLinkDrawer"
          open={showEdit}
          footer={
            <>
              <Button
                onClick={() => setShowEdit(false)}
                style={{ float: 'right', marginLeft: '16px' }}
              >
                取消
              </Button>
              <Button type="primary" onClick={() => onSave()} style={{ float: 'right' }}>
                保存
              </Button>
            </>
          }
          onClose={() => setShowEdit(false)}
        >
          <Input
            addonBefore={<SearchOutlined />}
            onChange={(e: any) => {
              filterName(e.target.value);
            }}
            placeholder="请输入关键字"
          />

          <div style={{ color: 'gray', marginTop: 20 }}>
            固定至快捷操作（<span style={{ color: 'orange' }}> {checkedKeys?.length} / 10</span> ）
          </div>
          <Checkbox.Group
            style={{ width: '100%' }}
            onChange={(values) => {
              setCheckedKeys(values);
            }}
            value={checkedKeys}
          >
            <div className="menuContainer">
              {showList
                ?.sort?.((a: any, b: any) => b.children?.length - a.children?.length)
                ?.map((item: any, _i) => (
                  <div key={_i} className="menuItem">
                    <div className="menuItemTitle">{item?.systemName}</div>
                    <Flex vertical gap="10px" className="menuItemList">
                      {item?.leafNodes?.map((_item: any, i: number) => (
                        <Checkbox
                          disabled={
                            checkedKeys?.length >= 10 && !checkedKeys?.includes(_item?.menuId)
                          }
                          key={i}
                          value={_item?.menuId}
                        >
                          {_item?.menuName}
                        </Checkbox>
                      ))}
                    </Flex>
                  </div>
                ))}
            </div>
          </Checkbox.Group>
        </Drawer>
      )}
    </div>
  );
};

export default DataComp;
