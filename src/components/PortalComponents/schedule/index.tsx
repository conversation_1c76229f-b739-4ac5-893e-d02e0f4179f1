import React, { useState, useEffect } from 'react';
import { Card, Calendar, message } from 'antd';
import type { Dayjs } from 'dayjs';
import { getScheduleInfo, deleteScheduleInfo, saveScheduleInfo } from '@/services/home';
import ScheduleModal from './components/ScheduleModal';
import './index.less';

interface Schedule {
  date: string;
  content: string;
  name: string;
  degree: string;
  id: number;
}

const ScheduleComponent: React.FC = () => {
  const [selectedDate, setSelectedDate] = useState<Dayjs | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [schedules, setSchedules] = useState<Record<string, Schedule[]>>({});
  const [loading, setLoading] = useState(false);

  const queryContentInfo = async () => {
    const res = await getScheduleInfo();
    setLoading(false);
    if (res.code === '200') {
      const contentData = res.data || {};
      setSchedules(contentData);
    } else {
      message.error(res.msg);
    }
  };

  const handleDateSelect = (date: Dayjs) => {
    setSelectedDate(date);
    setIsModalVisible(true);
  };

  const getScheduleItem = (id: number) => {
    if (selectedDate) {
      const scheduleInfo = schedules[selectedDate.format('YYYY-MM-DD')] || {};
      const targetInfo = scheduleInfo.find((item) => item.id === id) || {};
      return targetInfo;
    }
    return {};
  };

  const dateCellRender = (date: Dayjs) => {
    const formattedDate = date.format('YYYY-MM-DD');
    const daySchedules = schedules[formattedDate] || [];
    const importantSchedule = daySchedules.find((s) => s.degree === '重要');
    const scheduleToShow = importantSchedule || daySchedules[0];
    return scheduleToShow ? <div className="schedule-content">{scheduleToShow.name}</div> : null;
  };

  const onScheduleDel = async (id: number) => {
    const res = await deleteScheduleInfo(id);
    if (res.code === '200') {
      message.success('删除成功');
      setTimeout(() => queryContentInfo());
    } else {
      message.error(res.msg);
    }
  };

  const addSchedule = async (params: any) => {
    if (selectedDate) {
      const res = await saveScheduleInfo({
        batchParam: [
          {
            date: selectedDate.format('YYYY-MM-DD'),
            ...params,
          },
        ],
      });
      if (res.code === '200') {
        message.success('添加成功');
        setTimeout(() => queryContentInfo());
      } else {
        message.error(res.msg);
      }
    }
  };

  const onScheduleUpdate = async (params: any) => {
    if (selectedDate) {
      const Newparams = {
        ...getScheduleItem(params.id),
        id: params.id,
        name: params.name,
      };
      const res = await saveScheduleInfo({
        batchParam: [Newparams],
      });
      if (res.code === '200') {
        message.success('更新成功');
        queryContentInfo();
      } else {
        message.error(res.msg);
      }
    }
  };

  const onChangeDegree = async (id: number, value: string) => {
    if (selectedDate) {
      const params = {
        ...getScheduleItem(id),
        id,
        degree: value,
      };
      const res = await saveScheduleInfo({
        batchParam: [params],
      });
      if (res.code === '200') {
        message.success('更新成功');
        queryContentInfo();
      } else {
        message.error(res.msg);
      }
    }
  };

  useEffect(() => {
    queryContentInfo();
  }, []);

  return (
    <Card className="custom-schedule" title="日程安排" style={{ width: 400 }}>
      <Calendar fullscreen={false} onSelect={handleDateSelect} cellRender={dateCellRender} />
      {selectedDate && (
        <ScheduleModal
          visible={isModalVisible}
          selectedDate={selectedDate}
          schedules={schedules[selectedDate.format('YYYY-MM-DD')] || []}
          onClose={() => setIsModalVisible(false)}
          deleteHandle={onScheduleDel}
          markDegreeHandle={onChangeDegree}
          addHandle={addSchedule}
          updateHandle={onScheduleUpdate}
          loading={loading}
          setLoading={setLoading}
        />
      )}
    </Card>
  );
};
export default ScheduleComponent;
