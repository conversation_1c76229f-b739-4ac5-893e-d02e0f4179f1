.schedule-modal {
  .schedule-item {
    display: flex;
    align-items: center;
    justify-content: space-between; // 使图标在行末尾
    width: 100%;
    margin-bottom: 10px;
    button {
      margin-left: 5px;
    }
    .round-checkbox {
      margin-right: 10px;
      .ant-checkbox-inner {
        border-radius: 50%;
      }
      .ant-checkbox-checked .ant-checkbox-inner {
        background-color: red; // 设置为红色实心圆
        border-color: red;
      }
      .ant-checkbox-checked .ant-checkbox-inner::after {
        display: none; // 去掉中间的打勾图标
      }
      &:hover .ant-checkbox-inner {
        border-color: red; // hover 时保持红色边框
      }
      .ant-wave-target {
        &:hover {
          border-color: red;
        }
      }
      &:hover {
        .ant-checkbox-inner {
          background-color: red !important;
        }
      }
    }
    .icon-spacing {
      margin-left: 16px;
    }
    span {
      flex-grow: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap; // 处理文本过长时显示省略号
    }
  }
  .schedule-footer {
    display: flex;
    justify-content: space-between;
    align-items: end;
    .schedule-footer-info {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
    }
  }
  .important-schedule,
  .normal-schedule {
    display: flex;
    align-items: center;
  }
  .important-circle {
    width: 10px;
    height: 10px;
    background-color: red;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
  }
  .normal-circle {
    width: 10px;
    height: 10px;
    border: 1px solid #000;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
  }
  .schedule-content {
    position: relative;
  }
  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
  }
}
