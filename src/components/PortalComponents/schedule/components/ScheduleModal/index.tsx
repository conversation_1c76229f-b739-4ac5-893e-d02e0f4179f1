import React, { useEffect, useState } from 'react';
import { Modal, Input, Checkbox, Button, Popconfirm, Spin, message } from 'antd';
import { EditOutlined, DeleteOutlined } from '@ant-design/icons';
import type { Dayjs } from 'dayjs';
import './index.less';

interface Schedule {
  date: string;
  content: string;
  name: string;
  degree: string;
  id: number;
}
interface ScheduleModalProps {
  visible: boolean;
  selectedDate: Dayjs;
  schedules: Schedule[];
  onClose: () => void;
  addHandle: (params: any) => void | Promise<void>;
  updateHandle: (params: any) => void | Promise<void>;
  deleteHandle: (id: number) => void | Promise<void>;
  markDegreeHandle: (id: number, degree: string) => void | Promise<void>;
  loading: boolean;
  setLoading: React.Dispatch<React.SetStateAction<boolean>>;
}
const ScheduleModal: React.FC<ScheduleModalProps> = ({
  visible,
  selectedDate,
  schedules,
  onClose,
  addHandle,
  deleteHandle,
  updateHandle,
  markDegreeHandle,
  loading,
  setLoading,
}) => {
  const [newScheduleContent, setNewScheduleContent] = useState('');
  const [isImportant, setIsImportant] = useState(false);
  const [scheduleStates, setScheduleStates] = useState<boolean[]>([]);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editingName, setEditingName] = useState('');

  useEffect(() => {
    // 当 schedules 变化时，更新 scheduleStates
    setScheduleStates(schedules.map((schedule) => schedule.degree === '重要'));
  }, [schedules]);

  const handleCheckboxChange = async (index: number, checked: boolean) => {
    setLoading(true);
    await markDegreeHandle(index, checked ? '重要' : '普通');
  };

  const handleAddSchedule = async () => {
    if (!newScheduleContent) {
      message.warning('请输入日程信息后再点击新增按钮');
      return;
    }
    setLoading(true);
    try {
      await addHandle({
        name: newScheduleContent,
        degree: isImportant ? '重要' : '普通',
      });
    } finally {
      setNewScheduleContent('');
      setIsImportant(false);
    }
  };

  const handleDeleteSchedule = async (index: number) => {
    setLoading(true);
    await deleteHandle(index);
  };

  const handleEditSchedule = (index: number) => {
    setEditingIndex(index);
    setEditingName(schedules[index].name);
  };
  const handleConfirmEdit = async (id: number) => {
    setLoading(true);
    try {
      await updateHandle({ id, name: editingName });
    } finally {
      setEditingName('');
    }
    setEditingIndex(null);
  };

  const handleCancelEdit = () => {
    setEditingIndex(null);
  };
  return (
    <Modal
      title="日程安排"
      className="schedule-modal"
      open={visible}
      footer={[
        <div className="schedule-footer" key="footer">
          <div className="schedule-footer-info">
            <div className="important-schedule">
              <span className="important-circle" /> 重要日程
            </div>
            <div className="normal-schedule">
              <span className="normal-circle" /> 普通日程
            </div>
          </div>
          <Button key="add" type="primary" onClick={() => handleAddSchedule()} disabled={loading}>
            新增
          </Button>
        </div>,
      ]}
      onCancel={onClose}
    >
      <div style={{ textAlign: 'right' }}>{selectedDate.format('YYYY-MM-DD')}</div>
      <div className="schedule-content" style={{ position: 'relative' }}>
        {loading && (
          <div className="loading-overlay">
            <Spin size="large" />
          </div>
        )}
        {schedules.map((schedule, index) => (
          <div key={schedule.id} className="schedule-item">
            <Popconfirm
              title={`是否标记为${schedule.degree === '重要' ? '普通' : '重要'}日程`}
              onConfirm={() => handleCheckboxChange(schedule.id, !scheduleStates[index])}
              disabled={loading} // 禁用 Popconfirm 的交互
            >
              <Checkbox
                className="round-checkbox"
                checked={scheduleStates[index]}
                disabled={loading}
              />
            </Popconfirm>
            {editingIndex === index ? (
              <>
                <Input
                  value={editingName}
                  onChange={(e) => setEditingName(e.target.value)}
                  disabled={loading}
                />
                <Button
                  type="primary"
                  onClick={() => handleConfirmEdit(schedule.id)}
                  disabled={loading}
                >
                  确认
                </Button>
                <Button onClick={handleCancelEdit} disabled={loading}>
                  取消
                </Button>
              </>
            ) : (
              <>
                <span>{schedule.name}</span>
                <div className="schedule-icon">
                  <EditOutlined
                    className="icon-spacing"
                    onClick={() => handleEditSchedule(index)}
                  />
                  <Popconfirm
                    title="是否要删除此日程"
                    onConfirm={() => handleDeleteSchedule(schedule.id)}
                  >
                    <DeleteOutlined className="icon-spacing" />
                  </Popconfirm>
                </div>
              </>
            )}
          </div>
        ))}
        <div className="schedule-item">
          <Checkbox
            className="round-checkbox"
            checked={isImportant}
            onChange={(e) => setIsImportant(e.target.checked)}
            disabled={loading}
          />
          <Input
            placeholder="请输入日程安排"
            value={newScheduleContent}
            onChange={(e) => setNewScheduleContent(e.target.value)}
            disabled={loading}
          />
        </div>
      </div>
    </Modal>
  );
};
export default ScheduleModal;
