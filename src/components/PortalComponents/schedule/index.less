.custom-schedule {
  .ant-card-head {
    min-height: 46px;
  }
  .ant-card-body {
    padding: 5px 14px;
  }

  .ant-picker-content {
    tr {
      th {
        padding: 6px 0 !important;
        border: 1px solid #fff;
        background-color: rgb(110, 218, 119);
        &::before {
          content: '周';
          display: inline;
        }
      }
    }
  }
  .ant-picker-calendar-mode-switch {
    display: none;
  }

  .ant-picker-content {
    // 调整单元格大小
    td {
      height: 45px;
      border-top: 2px solid #e2e2e2; // 添加灰色边框

      &.ant-picker-cell {
        .ant-picker-cell-inner {
          background-color: transparent !important; // 移除背景颜色
        }
      }
      &.ant-picker-cell-selected {
        border-top: 2px solid #1890ff; // 选中时边框变为蓝色
        .ant-picker-cell-inner {
          color: black !important;
          background-color: transparent !important; // 移除背景颜色
        }
      }
      &.ant-picker-cell-today {
        border-top: 2px solid #52c41a !important;
      }
      .ant-picker-cell-inner {
        display: flex;
        flex-direction: column;
        align-items: flex-start; // 日期靠右上角
        padding: 2px;
        .ant-picker-calendar-date-value {
          position: absolute;
          top: -10px;
          font-size: 12px; // 调小字号
          align-self: flex-end; // 日期靠右上角
        }
        .ant-picker-calendar-date-content {
          position: relative;
          top: 10px;
          padding-left: 10px; // 为红点留出空间
          width: 150%;
          transform: scale(0.8);
          white-space: nowrap;
          font-size: 12px;
          text-overflow: ellipsis; // 超出显示省略号
          &:before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 6px;
            height: 6px;
            background-color: red; // 小红点
            border-radius: 50%;
            display: none; // 默认不显示红点
          }
          &:not(:empty):before {
            display: block; // 有内容时显示红点
          }
        }
      }
      .ant-picker-calendar-date-today {
        &::before {
          border-color: transparent !important;
        }
      }
    }
  }

  .schedule-content {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: calc(100% - 10px);
  }
}
