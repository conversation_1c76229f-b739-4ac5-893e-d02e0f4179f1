/* eslint-disable */
import { RightOutlined } from '@ant-design/icons';
import { useDeepCompareEffect } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import React, { useState } from 'react';
import morentupian from '../../../assets/images/morentupian.png';
import './index.less';

const PictureList: React.FC<any> = (props) => {
  const { name, params, service } = props;

  const [list, setList] = useState<any[]>([]);

  const getList = () => {
    service &&
      service({ pageNo: 1, pageSize: 3, ...params }).then((res: any) => {
        const list = res.data.data || [];
        list.forEach((item: any) => {
          if (item.tgpUrls) {
            const tgpUrls = item.tgpUrls.split(',');
            item.firstTgpUrl = `${PREFIX_FILE}${PREFIX_SERVER}${tgpUrls[0]}`;
          } else {
            item.firstTgpUrl = morentupian;
          }
        });
        setList(list);
      });
  };

  useDeepCompareEffect(() => {
    getList();
  }, [params]);
  return (
    <div className="portalComponents-pictureList">
      <div className="block-box">
        <div className="block-top">
          <div className="block-head">
            <div className="block-title">
              <span className="big">{name.charAt(0)}</span>
              <span className="normal">{name.charAt(1)}</span>
              <span className="normal green">{`${name.charAt(2)}${name.charAt(3)}`}</span>
            </div>
            <div className="block-more" onClick={() => history.push(`/column?id=${params.moduleId}`)}>
              更多
              <RightOutlined />
            </div>
          </div>
        </div>

        <div className="block-cont">
          {list.map((item, index) => {
            return (
              <>
                {index < 3 && (
                  <div className="block-item" key={item.textId}>
                    <div className="block-img" onClick={() => history.push(`/columnDetail?mid=${params.moduleId}&tid=${item.textId}`)}>
                      <img src={item.firstTgpUrl} alt="" />
                    </div>
                    <div className="block-text">
                      <div className="block-title ellipsis">{item.textTitle}</div>
                      <div className="block-enter" onClick={() => history.push(`/columnDetail?mid=${params.moduleId}&tid=${item.textId}`)}>
                        点击进入
                      </div>
                    </div>
                  </div>
                )}
              </>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default PictureList;
