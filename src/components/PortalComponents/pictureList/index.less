.portalComponents-pictureList {
  padding-top: 50px;
  .block-box {
    width: 1260px;
    margin: 0 auto;

    .block-top {
      display: flex;
      justify-content: center;
      background: url('../../../assets/images/home/<USER>') no-repeat left 80px;

      .block-head {
        display: flex;
        height: 105px;
        background: url('../../../assets/images/home/<USER>') no-repeat left top;

        .block-title {
          padding-top: 42px;
          padding-left: 15px;
          color: #0076d7;
          font-weight: 500;
          font-size: 32px;
          font-family: Alibaba-PuHuiTi-M;
          letter-spacing: 5px;

          .big {
            font-weight: 500;
            font-size: 60px;
            font-family: Alibaba-PuHuiTi-M;
            letter-spacing: 5px;
          }

          .green {
            color: #00b39c;
          }
        }

        .block-more {
          display: flex;
          align-items: end;
          margin-left: 8px;
          padding-bottom: 2px;
          color: #92c2c8;
          font-weight: 400;
          font-size: 14px;
          font-family: Alibaba-PuHuiTi-R;
          cursor: pointer;

          .anticon {
            margin-bottom: 1px;
            margin-left: 2px;
            font-size: 12px;
          }
        }
      }
    }

    .block-cont {
      display: flex;
      justify-content: space-between;
      margin-top: 60px;

      .block-item {
        width: 400px;
        overflow: hidden;
        border-radius: 20px;

        .block-img {
          img {
            width: 400px;
            height: 266px;
          }
        }

        .block-text {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 120px;
          padding: 20px;
          background: #197eef;

          .block-title {
            color: #ffffff;
            font-weight: 500;
            font-size: 20px;
            font-family: Alibaba-PuHuiTi-M;
            text-align: center;
          }

          .block-enter {
            width: 80px;
            height: 28px;
            margin-top: 20px;
            color: #ffffff;
            font-weight: 400;
            font-size: 16px;
            font-family: Alibaba-PuHuiTi-R;
            line-height: 28px;
            text-align: center;
            background: #0f64d6;
            border-radius: 4px;
            cursor: pointer;
          }
        }
      }
    }
  }

  .ellipsis {
    /* 隐藏超出容器的文本 */
    overflow: hidden;
    /* 确保文本不会换行 */
    white-space: nowrap;
    /* 当文本溢出时，显示省略号 */
    text-overflow: ellipsis;
  }
}
