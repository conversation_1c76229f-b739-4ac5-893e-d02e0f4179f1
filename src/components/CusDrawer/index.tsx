import React, { useState, ReactNode, CSSProperties } from 'react';
import DrawerButton from './components/DrawerButton';
import './index.less';

interface DrawerProps {
  direction?: 'left' | 'right';
  buttonTop?: number;
  contentBeforeTitle?: ReactNode;
  customButton?: ReactNode;
  ExtraButtons?: ReactNode;
  title?: string;
  titleExtra?: ReactNode;
  children?: ReactNode;
  width?: string;
  height?: string;
  buttonIcon?: any;
  hideAllButtons?: boolean;
  headerStyle?: CSSProperties;
}

const CusDrawer: React.FC<DrawerProps> = ({
  direction = 'right',
  buttonTop = 50,
  customButton,
  title,
  titleExtra,
  children,
  width = '360px', // 默认宽度
  height = 'calc(100% - 126px)', // 默认高度
  ExtraButtons,
  buttonIcon,
  headerStyle = {},
  hideAllButtons = false,
  contentBeforeTitle = '',
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleDrawer = () => {
    setIsOpen(!isOpen);
  };

  const setDrawerOpen = () => {
    setIsOpen(true);
  };

  const renderExtraButtons = () => {
    if (ExtraButtons && React.isValidElement(ExtraButtons)) {
      return React.cloneElement(ExtraButtons as React.ReactElement, {
        onClick: (event: React.MouseEvent) => {
          if (ExtraButtons.props.onClick) {
            ExtraButtons.props.onClick(event);
          }
          setDrawerOpen();
        },
      });
    }
    return '';
  };

  return (
    <div
      className={`drawer ${direction} ${isOpen ? 'open' : 'closed'}`}
      style={{ width }} // 使用传入的宽度
    >
      <div className={`toggle-button-container ${direction}`} style={{ top: `${buttonTop}%` }}>
        {!hideAllButtons && (
          <>
            <DrawerButton
              ButtonIcon={buttonIcon}
              isOpen={isOpen}
              direction={direction}
              onClick={toggleDrawer}
              CustomButton={customButton}
            />
            {renderExtraButtons()}
          </>
        )}
      </div>
      <div className="drawer-header" style={headerStyle}>
        {contentBeforeTitle}
        <div className="drawer-title">{title}</div>
        <div className="drawer-title-extra">{titleExtra}</div>
      </div>
      <hr className="drawer-divider" />
      <div className="drawer-content" style={{ height }}>
        {children}
      </div>
    </div>
  );
};
export default CusDrawer;
