.drawer {
  position: absolute;
  top: 0;
  width: 360px;
  height: 100%;
  background-color: #fff;
  box-shadow: -2px 0 5px rgba(145, 145, 145, 0.5);
  transition: transform 0.3s ease;
  &.right {
    right: 0;
    transform: translateX(100%);
    &.open {
      transform: translateX(0);
    }
  }
  &.left {
    left: 0;
    transform: translateX(-100%);
    box-shadow: 2px 0 5px rgba(126, 126, 126, 0.5);
    &.open {
      transform: translateX(0);
    }
  }
  .drawer-header {
    display: flex; // 使用 flex 布局
    justify-content: space-between; // 在两端对齐
    align-items: center; // 垂直居中
    padding: 10px 20px; // 添加一些内边距
    padding-bottom: 0;
  }
  .drawer-title {
    font-size: 18px;
    font-weight: bold;
  }
  .drawer-title-extra {
    display: flex; // 确保按钮在一行
    gap: 10px; // 按钮之间的间距
  }
  .drawer-content {
    padding: 20px;
    height: calc(100% - 126px);
  }
  .toggle-button {
    position: absolute;
    top: 70px;
    transform: translateY(-50%);
    background-color: #007bff;
    color: #fff;
    border: none;
    padding: 10px;
    cursor: pointer;
    opacity: 0.5; // 默认状态下半透明
    transition: opacity 0.3s, transform 0.1s; // 添加过渡效果
    &:hover {
      opacity: 1; // 鼠标悬停时不透明
    }
    &.right {
      left: -34px;
    }
    &.left {
      right: -34px;
    }
  }
}
