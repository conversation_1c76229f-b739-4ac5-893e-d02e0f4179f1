import React from 'react';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import '../index.less';

interface DrawerButtonProps {
  isOpen: boolean;
  direction: 'left' | 'right';
  onClick: () => void;
  CustomButton?: any;
  ButtonIcon?: any;
}
const DrawerButton: React.FC<DrawerButtonProps> = ({
  isOpen,
  direction,
  onClick,
  ButtonIcon,
  CustomButton,
}) => {
  let IconComponent;
  if (isOpen) {
    IconComponent = direction === 'right' ? RightOutlined : LeftOutlined;
  } else {
    IconComponent = direction === 'right' ? LeftOutlined : RightOutlined;
  }
  return (
    <>
      {CustomButton ? (
        React.cloneElement(CustomButton as React.ReactElement, {
          onClick,
          className: `toggle-button ${direction} ${CustomButton.props.className || ''}`,
        })
      ) : (
        <button className={`toggle-button ${direction}`} type="button" onClick={onClick}>
          {ButtonIcon && <ButtonIcon />}
          {!ButtonIcon && <IconComponent />}
        </button>
      )}
    </>
  );
};
export default DrawerButton;
