import React from 'react';
import backIcon from '@/assets/images/icon_fanhui.png';

interface PageHeaderProps {
  title: string;
  onClick: () => void;
  showBackIcon?: boolean;
}
const PageHeader: React.FC<PageHeaderProps> = ({ title, onClick, showBackIcon = false }) => {
  return (
    <div className="common-page-header">
      {showBackIcon ? (
        <div className="add-header">
          <img src={backIcon} alt="" onClick={onClick} />
          <span>{title}</span>
        </div>
      ) : (
        title
      )}
    </div>
  );
};
export default PageHeader;
