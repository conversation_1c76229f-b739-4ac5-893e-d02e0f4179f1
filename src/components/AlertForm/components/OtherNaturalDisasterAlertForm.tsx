import React from 'react';
import { Form, Input, Col } from 'antd';
import { textAreaLengthRules } from '@/utils/form';

interface FormSectionProps {
  cardIndex?: number;
}

export const naturalFormItems = ['hazardType', 'description'];

const OtherNaturalDisasterAlertForm: React.FC<FormSectionProps> = ({ cardIndex }) => {
  const getName = (baseName: string) =>
    cardIndex !== undefined ? `${baseName}_${cardIndex}` : baseName;
  return (
    <>
      <Col span={24}>
        <h3 style={{ padding: '0 12px', paddingTop: '10px' }}>其他类自然灾害预警信息</h3>
        <hr style={{ marginBottom: '15px' }} />
      </Col>
      <Col span={8}>
        <Form.Item labelCol={{ span: 8 }} label="灾害类型" name={getName('hazardType')}>
          <Input />
        </Form.Item>
      </Col>
      <Col span={16}>
        <Form.Item
          rules={[textAreaLengthRules]}
          labelCol={{ span: 8 }}
          label="情况描述"
          name={getName('description')}
        >
          <Input.TextArea />
        </Form.Item>
      </Col>
    </>
  );
};
export default OtherNaturalDisasterAlertForm;
