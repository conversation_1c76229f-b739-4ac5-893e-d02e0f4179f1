import React from 'react';
import { Form, Input, Col, FormInstance } from 'antd';

interface FormSectionProps {
  cardIndex?: number;
  form?: FormInstance<any>;
  onAlertTypeChange?: (value: any) => void;
}

export const baseFormItems = ['name', 'type', 'level', 'source'];

const IgnoreInfoForm: React.FC<FormSectionProps> = () => {
  return (
    <>
      <Col span={24}>
        <h3 style={{ padding: '0 12px', paddingTop: '10px' }}>忽略信息</h3>
        <hr style={{ marginBottom: '15px' }} />
      </Col>
      <Col span={8}>
        <Form.Item label="处置人员" labelCol={{ span: 8 }} name="disposerName">
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item label="处置时间" labelCol={{ span: 8 }} name="disposerDate">
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item label="忽略原因" labelCol={{ span: 8 }} name="ignoreReason">
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item label="详细原因" labelCol={{ span: 8 }} name="ignoreReasonDescribe">
          <Input.TextArea />
        </Form.Item>
      </Col>
    </>
  );
};

export default IgnoreInfoForm;
