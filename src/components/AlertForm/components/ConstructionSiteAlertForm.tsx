import React from 'react';
import { Form, Input, Col, Select } from 'antd';
import MonitoringFields from './MonitoringFields';
import { codeLengthRules, codeRules, nameLengthRules } from '@/utils/form';
import PhoneInput from '@/components/BaseFormComponents/FormElement/PhoneInput';

interface FormSectionProps {
  cardIndex?: number;
}

export const constructionsFormItemNames = [
  'name',
  'construction',
  'unit',
  'enterpriseName',
  'enterpriseCreditCode',
  'contact',
  'phone',
];

const ConstructionSiteAlertForm: React.FC<FormSectionProps> = ({ cardIndex }) => {
  const getName = (baseName: string) =>
    cardIndex !== undefined ? `${baseName}_${cardIndex}` : baseName;
  return (
    <>
      <Col span={24}>
        <h3 style={{ padding: '0 12px', paddingTop: '10px' }}>建筑工地预警信息</h3>
        <hr style={{ marginBottom: '15px' }} />
      </Col>
      <Col span={8}>
        <Form.Item
          rules={[nameLengthRules]}
          labelCol={{ span: 8 }}
          label="工地名称"
          name={getName('name')}
        >
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item labelCol={{ span: 8 }} label="施工阶段" name={getName('construction')}>
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          rules={[nameLengthRules]}
          labelCol={{ span: 8 }}
          label="监理单位"
          name={getName('unit')}
        >
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          rules={[nameLengthRules]}
          labelCol={{ span: 8 }}
          label="企业名称"
          name={getName('enterpriseName')}
        >
          <Select />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          labelCol={{ span: 8 }}
          label="统一社会信用代码"
          name={getName('enterpriseCreditCode')}
          rules={[codeRules, codeLengthRules]}
        >
          <Input />
        </Form.Item>
      </Col>
      <MonitoringFields getName={getName} />
      <Col span={8}>
        <Form.Item labelCol={{ span: 8 }} label="联系人" name={getName('contact')}>
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <PhoneInput name={getName('phone')} />
      </Col>
    </>
  );
};
export default ConstructionSiteAlertForm;
