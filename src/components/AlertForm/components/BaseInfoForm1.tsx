import React, { useEffect, useState } from 'react';
import { Form, Input, Col, Select, DatePicker, FormInstance } from 'antd';
import { getAlertLevelist, getAlertTypeList } from '@/services/systemMaintenance/alert';
import useDict from '@/utils/useDict';
import { getOptionsFromDict, fetchOptions } from '@/utils/commonFunction';
import moment from 'moment';
import { nameLengthRules, textAreaLengthRules } from '@/utils/form';

interface FormSectionProps {
  cardIndex?: number;
  form?: FormInstance<any>;
  onAlertTypeChange?: (value: any) => void;
  detailType?: string;
  setLoading: React.Dispatch<React.SetStateAction<boolean>>;
}

export const baseFormItems = [
  'name',
  'type',
  'level',
  'source',
  'startDate',
  'item',
  'influenceRadius',
  'issuingAuthority',
  'measure',
  'owningRegion',
  'broad',
];

const originOptions: any[] = [];

const BasicInfoForm: React.FC<FormSectionProps> = ({
  cardIndex,
  form,
  onAlertTypeChange,
  detailType,
  setLoading,
}) => {
  const { value: warnSource } = useDict('warn_source');
  const { value: warnRegion } = useDict('warn_owning_region');

  const [alertOptions, setAlertOptions] = useState<any[]>([]);
  const [levelOptions, setLevelOptions] = useState<any[]>([]);
  const [sourceOptions, setSourceOptions] = useState<any[]>([]);
  const [regionOptions, setRegionOptions] = useState<any[]>([]);

  // 根据 cardIndex 是否存在来决定 name 的格式
  const getName = (baseName: string) =>
    cardIndex !== undefined ? `${baseName}_${cardIndex}` : baseName;

  const setOriginOptions = (data: any[]) => {
    originOptions.length = 0;
    originOptions.push(...data);
  };

  // 处理预警类型选择变化
  const handleAlertTypeChange = (value: any, option: any) => {
    if (onAlertTypeChange) {
      const selectedOption = originOptions.find(option => option.encoding === value);
      form?.setFieldsValue({ [getName('broad')]: selectedOption?.broad });
      onAlertTypeChange(option.label); // 调用父组件传入的回调
    }
  };

  // 详情页 type 初始化
  const setDetailType = () => {
    if (detailType) {
      const target = alertOptions.find(option => option.value === detailType);
      handleAlertTypeChange(detailType, target);
    }
  };

  useEffect(() => {
    fetchOptions(getAlertTypeList, setAlertOptions, 'name', 'encoding', setOriginOptions);
    fetchOptions(getAlertLevelist, setLevelOptions, 'name', 'id');
  }, []);

  useEffect(() => {
    if (warnSource.length > 0) {
      setSourceOptions(getOptionsFromDict(warnSource));
    }
  }, [warnSource]);

  useEffect(() => {
    if (warnRegion.length > 0) {
      setRegionOptions(getOptionsFromDict(warnRegion));
    }
  }, [warnRegion]);

  useEffect(() => {
    if (alertOptions.length > 0) {
      setTimeout(() => {
        setLoading(false);
      }, 200);
      if (detailType) {
        setDetailType();
      }
    }
  }, [detailType, alertOptions]);

  return (
    <>
      <Col span={24}>
        <h3 style={{ padding: '0 12px', paddingTop: '10px' }}>基础信息</h3>
        <hr style={{ marginBottom: '15px' }} />
      </Col>
      <Col span={8}>
        <Form.Item
          label="预警名称"
          labelCol={{ span: 8 }}
          name={getName('name')}
          rules={[{ required: true, message: '请输入预警名称' }, nameLengthRules]}
        >
          <Input placeholder="请输入预警名称" />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          label="预警类型"
          rules={[{ required: true, message: '请选择预警类型' }]}
          labelCol={{ span: 8 }}
          name={getName('type')}
        >
          <Select placeholder="请选择" options={alertOptions} onChange={handleAlertTypeChange} />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          label="预警级别"
          rules={[{ required: true, message: '请选择预警级别' }]}
          labelCol={{ span: 8 }}
          name={getName('level')}
        >
          <Select placeholder="请选择" options={levelOptions} />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          label="预警来源"
          rules={[{ required: true, message: '请选择预警来源' }]}
          name={getName('source')}
          labelCol={{ span: 8 }}
        >
          <Select placeholder="请选择" options={sourceOptions} />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          label="起始时间"
          name={getName('startDate')}
          labelCol={{ span: 8 }}
          getValueProps={value => ({
            value: value ? moment(value) : null,
          })}
          getValueFromEvent={(_date, dateString) => dateString}
        >
          <DatePicker showTime style={{ width: '100%' }} />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          labelCol={{ span: 8 }}
          label="警示事项"
          name={getName('item')}
          rules={[{ required: true, message: '请输入警示事项' }]}
        >
          <Input.TextArea placeholder="请输入警示事项" />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item labelCol={{ span: 8 }} label="可能影响范围" name={getName('influenceRadius')}>
          <Input placeholder="请输入" />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item labelCol={{ span: 8 }} label="发布机关" name={getName('issuingAuthority')}>
          <Input placeholder="请输入发布机关" />
        </Form.Item>
      </Col>
      <Col span={16}>
        <Form.Item
          rules={[textAreaLengthRules]}
          labelCol={{ span: 4 }}
          label="应采取防范措施"
          name={getName('measure')}
        >
          <Input.TextArea placeholder="请输入" />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          labelCol={{ span: 8 }}
          label="所属区域"
          name={getName('owningRegion')}
          rules={[{ required: true, message: '请选择所属区域' }]}
        >
          <Select placeholder="请选择" options={regionOptions} />
        </Form.Item>
      </Col>
      <Form.Item name={getName('broad')} />
    </>
  );
};

export default BasicInfoForm;
