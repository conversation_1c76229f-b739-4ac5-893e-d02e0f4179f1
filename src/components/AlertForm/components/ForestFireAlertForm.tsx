import React from 'react';
import { Form, Input, Col } from 'antd';
import { textAreaLengthRules } from '@/utils/form';

interface FormSectionProps {
  cardIndex?: number;
}

export const forestFormitems = ['description'];

const ForestFireAlertForm: React.FC<FormSectionProps> = ({ cardIndex }) => {
  const getName = (baseName: string) =>
    cardIndex !== undefined ? `${baseName}_${cardIndex}` : baseName;
  return (
    <>
      <Col span={24}>
        <h3 style={{ padding: '0 12px', paddingTop: '10px' }}>森林火灾预警信息</h3>
        <hr style={{ marginBottom: '15px' }} />
      </Col>
      <Col span={16}>
        <Form.Item
          labelCol={{ span: 4 }}
          label="情况描述"
          rules={[{ required: true, message: '请输入情况描述' }, textAreaLengthRules]}
          name={getName('description')}
        >
          <Input.TextArea />
        </Form.Item>
      </Col>
    </>
  );
};
export default ForestFireAlertForm;
