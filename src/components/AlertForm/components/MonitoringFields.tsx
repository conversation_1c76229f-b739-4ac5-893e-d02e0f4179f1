// frontend/sub_system_frontend/src/components/AlertForm/components/MonitoringFields.tsx
import React from 'react';
import { Form, Input, Col } from 'antd';

interface MonitoringFieldsProps {
  getName: (baseName: string) => string;
}
const MonitoringFields: React.FC<MonitoringFieldsProps> = ({ getName }) => {
  return (
    <>
      <Col span={8}>
        <Form.Item labelCol={{ span: 8 }} label="监测设备" name={getName('equipment')}>
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item labelCol={{ span: 8 }} label="监测因子" name={getName('divisor')}>
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item labelCol={{ span: 8 }} label="监测值" name={getName('onitoredValue')}>
          <Input />
        </Form.Item>
      </Col>
    </>
  );
};
export default MonitoringFields;
