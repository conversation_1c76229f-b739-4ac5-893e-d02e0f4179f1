import React from 'react';
import { Form, Input, Col, FormInstance } from 'antd';

interface FormSectionProps {
  cardIndex?: number;
  form?: FormInstance<any>;
  onAlertTypeChange?: (value: any) => void;
}

export const baseFormItems = ['name', 'type', 'level', 'source'];

const HandleInfoForm: React.FC<FormSectionProps> = () => {
  return (
    <>
      <Col span={24}>
        <h3 style={{ padding: '0 12px', paddingTop: '10px' }}>处置信息</h3>
        <hr style={{ marginBottom: '15px' }} />
      </Col>
      <Col span={8}>
        <Form.Item label="处置人员" labelCol={{ span: 8 }} name="disposerName">
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item label="处置时间" labelCol={{ span: 8 }} name="disposerDate">
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item label="处置内容" labelCol={{ span: 8 }} name="disposerContent">
          <Input />
        </Form.Item>
      </Col>
    </>
  );
};

export default HandleInfoForm;
