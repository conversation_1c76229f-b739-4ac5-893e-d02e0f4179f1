import React from 'react';
import { Form, Input, Col, Select } from 'antd';
import MonitoringFields from './MonitoringFields';
import { codeLengthRules, codeRules, nameLengthRules } from '@/utils/form';
import PhoneInput from '@/components/BaseFormComponents/FormElement/PhoneInput';

interface FormSectionProps {
  cardIndex?: number;
}

export const gasFormItems = [
  'place',
  'number',
  'enterprise_name',
  'enterprise_credit_code',
  'contact',
  'phone',
];

const GasPipelineAlertForm: React.FC<FormSectionProps> = ({ cardIndex }) => {
  const getName = (baseName: string) =>
    cardIndex !== undefined ? `${baseName}_${cardIndex}` : baseName;
  return (
    <>
      <Col span={24}>
        <h3 style={{ padding: '0 12px', paddingTop: '10px' }}>拓展信息</h3>
        <hr style={{ marginBottom: '15px' }} />
      </Col>
      <Col span={8}>
        <Form.Item labelCol={{ span: 8 }} label="管网位置" name={getName('place')}>
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          rules={[codeRules, codeLengthRules]}
          labelCol={{ span: 8 }}
          label="管道编号"
          name={getName('number')}
        >
          <Input />
        </Form.Item>
      </Col>
      <MonitoringFields getName={getName} />
      <Col span={8}>
        <Form.Item
          rules={[nameLengthRules]}
          labelCol={{ span: 8 }}
          label="企业名称"
          name={getName('enterprise_name')}
        >
          <Select />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          labelCol={{ span: 8 }}
          label="统一社会信用代码"
          name={getName('enterprise_credit_code')}
        >
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item labelCol={{ span: 8 }} label="联系人" name={getName('contact')}>
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <PhoneInput name={getName('phone')} />
      </Col>
    </>
  );
};
export default GasPipelineAlertForm;
