import React from 'react';
import { Form, Input, Col, Select } from 'antd';
import MonitoringFields from './MonitoringFields';
import { codeLengthRules, codeRules, nameLengthRules } from '@/utils/form';
import PhoneInput from '@/components/BaseFormComponents/FormElement/PhoneInput';

interface FormSectionProps {
  cardIndex?: number;
}

export const nonFormItems = [
  'enterpriseName',
  'enterpriseCreditCode',
  'enterpriseType',
  'mode',
  'contact',
  'phone',
];

const NonCoalMineAlertForm: React.FC<FormSectionProps> = ({ cardIndex }) => {
  const getName = (baseName: string) =>
    cardIndex !== undefined ? `${baseName}_${cardIndex}` : baseName;
  return (
    <>
      <Col span={24}>
        <h3 style={{ padding: '0 12px', paddingTop: '10px' }}>非煤矿山企业预警信息</h3>
        <hr style={{ marginBottom: '15px' }} />
      </Col>
      <Col span={8}>
        <Form.Item
          rules={[nameLengthRules]}
          labelCol={{ span: 8 }}
          label="企业名称"
          name={getName('enterpriseName')}
        >
          <Select />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          labelCol={{ span: 8 }}
          label="统一社会信用代码"
          name={getName('enterpriseCreditCode')}
          rules={[codeRules, codeLengthRules]}
        >
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item labelCol={{ span: 8 }} label="矿山类型" name={getName('enterpriseType')}>
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item labelCol={{ span: 8 }} label="开采方式" name={getName('mode')}>
          <Input />
        </Form.Item>
      </Col>
      <MonitoringFields getName={getName} />
      <Col span={8}>
        <Form.Item labelCol={{ span: 8 }} label="联系人" name={getName('contact')}>
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <PhoneInput name={getName('phone')} />
      </Col>
    </>
  );
};
export default NonCoalMineAlertForm;
