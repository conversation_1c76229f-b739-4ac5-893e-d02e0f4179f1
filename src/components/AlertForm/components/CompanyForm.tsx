import React from 'react';
import { Col, Input } from 'antd';

interface FormSectionProps {
  rectificationInfo: { rectificationRecordDate: string; content: string }[];
}

const CompanyForm: React.FC<FormSectionProps> = ({ rectificationInfo }) => {
  return (
    <>
      <Col span={24}>
        <h3 style={{ padding: '0 12px', paddingTop: '10px' }}>企业整改信息</h3>
        <hr style={{ marginBottom: '15px' }} />
      </Col>
      {rectificationInfo.map((info, index) => (
        <Col span={8} key={index} style={{ marginBottom: 15 }}>
          <div style={{ padding: '0 12px' }}>
            <div>{info.rectificationRecordDate}</div>
            <Input.TextArea value={info.content} readOnly />
          </div>
        </Col>
      ))}
    </>
  );
};
export default CompanyForm;
