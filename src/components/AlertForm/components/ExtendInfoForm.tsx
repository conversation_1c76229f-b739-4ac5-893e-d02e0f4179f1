import React from 'react';
import { Form, Input, Col } from 'antd';
import MonitoringFields from './MonitoringFields';
import { codeLengthRules, codeRules, nameLengthRules } from '@/utils/form';
import PhoneInput from '@/components/BaseFormComponents/FormElement/PhoneInput';

interface FormSectionProps {
  cardIndex?: number;
}

export const extendedInfoFormItems = [
  'enterpriseName',
  'enterpriseCreditCode',
  'species',
  'store',
  'contact',
  'phone',
];

const ExtendedInfoForm: React.FC<FormSectionProps> = ({ cardIndex }) => {
  // 根据 cardIndex 是否存在来决定 name 的格式
  const getName = (baseName: string) =>
    cardIndex !== undefined ? `${baseName}_${cardIndex}` : baseName;
  return (
    <>
      <Col span={24}>
        <h3 style={{ padding: '0 12px', paddingTop: '10px' }}>拓展信息</h3>
        <hr style={{ marginBottom: '15px' }} />
      </Col>
      <Col span={8}>
        <Form.Item
          labelCol={{ span: 8 }}
          label="企业名称"
          rules={[nameLengthRules]}
          name={getName('enterpriseName')}
        >
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          labelCol={{ span: 8 }}
          label="统一社会信用代码"
          name={getName('enterpriseCreditCode')}
          rules={[codeRules, codeLengthRules]}
        >
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item labelCol={{ span: 8 }} label="危化品种类" name={getName('species')}>
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item labelCol={{ span: 8 }} label="危化品存储量" name={getName('store')}>
          <Input />
        </Form.Item>
      </Col>
      <MonitoringFields getName={getName} />
      <Col span={8}>
        <Form.Item labelCol={{ span: 8 }} label="联系人" name={getName('contact')}>
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <PhoneInput name={getName('phone')} />
      </Col>
    </>
  );
};

export default ExtendedInfoForm;
