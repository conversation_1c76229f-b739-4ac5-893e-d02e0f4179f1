import { getAlertLevelist, getAlertTypeList } from '@/services/systemMaintenance/alert';
import { fetchOptions, getOptionsFromDict } from '@/utils/commonFunction';
import { nameLengthRules, textAreaLengthRules } from '@/utils/form';
import useDict from '@/utils/useDict';
import { UploadOutlined } from '@ant-design/icons';
import { Col, DatePicker, Form, FormInstance, Input, Radio, Select, Upload } from 'antd';
import moment from 'moment';
import React, { useEffect, useState } from 'react';

interface FormSectionProps {
  cardIndex?: number;
  form?: FormInstance<any>;
  onAlertTypeChange?: (value: any) => void;
  detailType?: string;
  setLoading: React.Dispatch<React.SetStateAction<boolean>>;
  openType?: 'add' | 'view' | 'edit';
}

export const baseFormItems = [
  'name',
  'type',
  'level',
  'startDate',
  'owningRegion',
  'reportingUnit',
  'isPublished',
  'issuingAuthority',
  'source',
  'item',
  'influenceRadius',
  'measure',
  'attachments',
];

const originOptions: any[] = [];

const BasicInfoForm: React.FC<FormSectionProps> = ({
  cardIndex,
  form,
  onAlertTypeChange,
  detailType,
  setLoading,
  openType,
}) => {
  const { value: warnSource } = useDict('warn_source');
  const { value: warnRegion } = useDict('warn_owning_region');

  const [alertOptions, setAlertOptions] = useState<any[]>([]);
  const [levelOptions, setLevelOptions] = useState<any[]>([]);
  const [sourceOptions, setSourceOptions] = useState<any[]>([]);

  const [regionOptions, setRegionOptions] = useState<any[]>([]);

  // 根据 cardIndex 是否存在来决定 name 的格式
  const getName = (baseName: string) =>
    cardIndex !== undefined ? `${baseName}_${cardIndex}` : baseName;

  const setOriginOptions = (data: any[]) => {
    originOptions.length = 0;
    originOptions.push(...data);
  };

  // 处理预警类型选择变化
  const handleAlertTypeChange = (value: any, option: any) => {
    if (onAlertTypeChange) {
      const selectedOption = originOptions.find(option => option.encoding === value);
      form?.setFieldsValue({ [getName('broad')]: selectedOption?.broad });
      onAlertTypeChange(option.label); // 调用父组件传入的回调
    }
  };

  // 详情页 type 初始化
  const setDetailType = () => {
    if (detailType) {
      const target = alertOptions.find(option => option.value === detailType);
      handleAlertTypeChange(detailType, target);
    }
  };

  useEffect(() => {
    // 只在组件首次挂载时获取预警类型和级别选项
    fetchOptions(getAlertTypeList, setAlertOptions, 'name', 'encoding', setOriginOptions);
    fetchOptions(getAlertLevelist, setLevelOptions, 'name', 'id');
  }, []);
  useEffect(() => {
    if (warnSource.length > 0) {
      setSourceOptions(getOptionsFromDict(warnSource));
    }
  }, [warnSource]);

  useEffect(() => {
    // 只有当warnRegion有值且regionOptions为空时才设置
    if (warnRegion && warnRegion.length > 0 && regionOptions.length === 0) {
      setRegionOptions(getOptionsFromDict(warnRegion));
    }
  }, [warnRegion, regionOptions.length]);

  useEffect(() => {
    if (alertOptions.length > 0) {
      setTimeout(() => {
        setLoading(false);
      }, 200);
      if (detailType) {
        setDetailType();
      }
    }
  }, [detailType, alertOptions]);

  return (
    <>
      <Col span={24}>
        <h3 style={{ padding: '0 12px', paddingTop: '10px' }}>预警基础信息</h3>
        <hr style={{ marginBottom: '15px' }} />
      </Col>
      <Col span={8}>
        <Form.Item
          label="预警名称"
          labelCol={{ span: 8 }}
          name={getName('name')}
          rules={[{ required: true, message: '请输入预警名称' }, nameLengthRules]}
        >
          <Input placeholder="请输入预警名称" />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          label="预警类型"
          rules={[{ required: true, message: '请选择预警类型' }]}
          labelCol={{ span: 8 }}
          name={getName('type')}
        >
          <Select
            placeholder="请选择预警类型"
            options={alertOptions}
            onChange={handleAlertTypeChange}
          />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          label="预警级别"
          rules={[{ required: true, message: '请选择预警级别' }]}
          labelCol={{ span: 8 }}
          name={getName('level')}
        >
          <Select placeholder="请选择预警级别" options={levelOptions} />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          label="预警时间"
          name={getName('startDate')}
          labelCol={{ span: 8 }}
          rules={[{ required: true, message: '请选择预警时间' }]}
          initialValue={openType === 'add' ? moment() : undefined}
          getValueProps={value => ({
            value: value ? moment(value) : null,
          })}
          getValueFromEvent={(_date, dateString) => dateString}
        >
          <DatePicker showTime style={{ width: '100%' }} />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item labelCol={{ span: 8 }} label="所属区域" name={getName('owningRegion')}>
          <Select placeholder="请选择所属区域" options={regionOptions} />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          label="填报单位"
          labelCol={{ span: 8 }}
          name={getName('reportingUnit')}
          rules={[{ required: true, message: '请输入填报单位' }]}
        >
          <Input placeholder="请输入填报单位" disabled />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          label="是否发布"
          labelCol={{ span: 8 }}
          name={getName('warningState')}
          initialValue={openType === 'add' ? '0' : undefined}
        >
          <Radio.Group>
            <Radio value="1">是</Radio>
            <Radio value="0">否</Radio>
          </Radio.Group>
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item labelCol={{ span: 8 }} label="发布单位" name={getName('issuingAuthority')}>
          <Input placeholder="请输入发布单位" />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          label="预警来源"
          name={getName('source')}
          labelCol={{ span: 8 }}
          initialValue={openType === 'add' ? '0' : undefined}
        >
          <Select placeholder="请选择预警来源" options={sourceOptions} disabled />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item
          labelCol={{ span: 2 }}
          label="预警事项"
          name={getName('item')}
          rules={[{ required: true, message: '请输入预警事项' }]}
        >
          <Input.TextArea placeholder="请输入预警事项" rows={3} />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item labelCol={{ span: 2 }} label="可能影响范围" name={getName('influenceRadius')}>
          <Input.TextArea placeholder="请输入可能影响范围" rows={3} />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item
          rules={[textAreaLengthRules]}
          labelCol={{ span: 2 }}
          label="应采取防范措施"
          name={getName('measure')}
        >
          <Input.TextArea placeholder="请输入应采取防范措施" rows={3} />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item
          labelCol={{ span: 2 }}
          label="附件"
          name={getName('influenceRadiusFile')}
          valuePropName="fileList"
          getValueFromEvent={e => {
            if (Array.isArray(e)) {
              return e;
            }
            return e && e.fileList;
          }}
        >
          <Upload
            name="file"
            listType="picture-card"
            accept="image/*,video/mp4"
            beforeUpload={() => false} // 阻止自动上传
          >
            <div>
              <UploadOutlined />
              <div style={{ marginTop: 8 }}>上传附件</div>
            </div>
          </Upload>
        </Form.Item>
      </Col>
      <Form.Item name={getName('broad')} />
    </>
  );
};

export default BasicInfoForm;
