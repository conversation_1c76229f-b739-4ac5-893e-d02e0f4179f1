// frontend/sub_system_frontend/src/components/AlertForm/AlertForm.tsx
import { FormInstance, Spin } from 'antd';
import React, { useEffect, useState } from 'react';
import BasicInfoForm, { baseFormItems } from './components/BaseInfoForm';
import ConstructionSiteAlertForm from './components/ConstructionSiteAlertForm';
import ExtendedInfoForm from './components/ExtendInfoForm';
import FloodDisasterAlertForm from './components/FloodDisasterAlertForm';
import ForestFireAlertForm from './components/ForestFireAlertForm';
import GasPipelineAlertForm from './components/GasPipelineAlertForm';
import GeologicalDisasterAlertForm from './components/GeologicalDisasterAlertForm';
import MeteorologicalDisasterAlertForm from './components/MeteorologicalDisasterAlertForm';
import NonCoalMineAlertForm from './components/NonCoalMineAlertForm';
import OtherNaturalDisasterAlertForm from './components/OtherNaturalDisasterAlertForm';
import OtherSafetyProductionAlertForm from './components/OtherSafetyProductionAlertForm';
import ParkSafetyAlertForm from './components/ParkSafetyAlertForm';

interface AlertFormProps {
  cardIndex?: number;
  form?: FormInstance<any>;
  type?: string;
  openType?: 'add' | 'view' | 'edit';
  record?: any;
}
const formComponents: { [key: string]: React.FC<{ cardIndex?: number }> } = {
  危化企业预警: ExtendedInfoForm, // 危险化学品预警表单
  建筑工地企业预警: ConstructionSiteAlertForm, // 建设工地预警表单
  洪涝灾害预警: FloodDisasterAlertForm, // 洪水灾害预警表单
  森林火灾预警: ForestFireAlertForm, // 森林火灾预警表单
  天然气管网预警: GasPipelineAlertForm, // 燃气管道预警表单
  地质灾害预警: GeologicalDisasterAlertForm, // 地质灾害预警表单
  气象灾害预警: MeteorologicalDisasterAlertForm, // 气象灾害预警表单
  非煤矿山企业预警: NonCoalMineAlertForm, // 非煤矿山预警表单
  '其他类（自然灾害）': OtherNaturalDisasterAlertForm, // 其他自然灾害预警表单
  '其他类（安全生产）': OtherSafetyProductionAlertForm, // 其他安全生产预警表单
  园区安全生产预警: ParkSafetyAlertForm, // 园区安全预警表单
};

const AlertForm: React.FC<AlertFormProps> = ({ cardIndex, form, type, openType, record }) => {
  const [currentForm, setCurrentForm] = useState('森林火灾预警');
  const [loading, setLoading] = useState(true);

  const changeForm = (value: string) => {
    // 使用对象映射来切换表单
    if (formComponents[value]) {
      setCurrentForm(value);
    } else {
      setCurrentForm('森林火灾预警'); // 默认表单
    }
  };

  const handleAlertTypeChange = (value: string) => {
    if (form && cardIndex !== undefined) {
      // 获取当前表单的所有字段
      const allFields = form.getFieldsValue();
      const currentFields = Object.keys(allFields).filter(field => field.endsWith(`_${cardIndex}`));
      // 清理非基础表单项的值
      currentFields.forEach(field => {
        const baseFieldName = field.split('_')[0];
        if (!baseFormItems.includes(baseFieldName)) {
          form.setFieldsValue({ [field]: undefined });
        }
      });
    }
    changeForm(value);
  };

  useEffect(() => {
    if (form) {
      const typeValue = form.getFieldValue(`type_${cardIndex}`);
      if (typeValue) {
        changeForm(typeValue);
      }
    }
  }, [cardIndex]);

  useEffect(() => {
    console.log('record:', record);
    form?.setFieldsValue(record);
  }, [record]);

  const CurrentFormComponent = formComponents[currentForm] || ExtendedInfoForm;
  return (
    <>
      <BasicInfoForm
        cardIndex={cardIndex}
        detailType={type}
        form={form}
        onAlertTypeChange={handleAlertTypeChange}
        setLoading={setLoading}
        openType={openType}
      />
      {loading ? (
        <Spin spinning={loading} style={{ width: '100%' }} />
      ) : (
        <CurrentFormComponent cardIndex={cardIndex} />
      )}
    </>
  );
};
export default AlertForm;
