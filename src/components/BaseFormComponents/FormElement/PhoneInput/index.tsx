import React from 'react';
import { Form, Input } from 'antd';
import { phoneLengthRules, phoneRules } from '@/utils/form';

interface PhoneInputProps {
  name: string;
}

const PhoneInput: React.FC<PhoneInputProps> = ({ name }) => {
  return (
    <Form.Item
      rules={[phoneRules, phoneLengthRules]}
      labelCol={{ span: 8 }}
      label="联系方式"
      name={name}
    >
      <Input />
    </Form.Item>
  );
};

export default PhoneInput;
