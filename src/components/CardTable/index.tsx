import React, { useState, useEffect } from 'react';
import { ColProps, Pagination, Spin } from 'antd';
import { request } from '@/utils/net';
import FilterForm, { CustomButton } from '../FilterForm';
import CardItem from './components/CardItem';
import './index.less';

interface CardTableProps {
  apiUrl: string;
  apiMethod?: 'get' | 'post';
  extraParams?: Record<string, any>;
  filterItems?: any[]; // 筛选项数组，可能为空
  customButtons?: CustomButton[];
  viewHandle?: (item: any) => void;
  delHandle?: (item: any) => void;
  updateTrigger?: boolean;
  buttonCol?: number;
  searchButtonStyle?: React.CSSProperties;
  hidePagenation?: boolean;
  outerPageNo?: number;
  showDownload?: boolean;
  cardWidth?: string;
  labelCol?: ColProps;
  getDataTotal?: (count: number) => void;
  hideActionButtons?: boolean;
  customCardComponent?: React.FC<{
    item: any;
    cardWidth?: string;
    viewHandle?: (item: any) => void;
    delHandle?: (item: any) => void;
    showDownload?: boolean;
  }>;
  cardExtraProps?: any;
  tableWrapStyle?: React.CSSProperties;
  getFormValues?: React.Dispatch<React.SetStateAction<Record<string, any>>>;
  showSizeChanger?: boolean;
}

const CardTable: React.FC<CardTableProps> = ({
  apiUrl,
  extraParams,
  apiMethod = 'get',
  filterItems,
  customButtons,
  viewHandle,
  delHandle,
  updateTrigger,
  buttonCol,
  searchButtonStyle,
  outerPageNo,
  hidePagenation = false,
  cardWidth,
  labelCol,
  showDownload,
  getDataTotal,
  getFormValues,
  cardExtraProps,
  hideActionButtons,
  customCardComponent: CustomCardComponent,
  tableWrapStyle, // 接收新的样式属性
  showSizeChanger = true,
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [data, setData] = useState([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false); // 新增 loading 状态
  const fetchData = async (params = {}) => {
    setLoading(true); // 开始请求时设置 loading 为 true
    try {
      const response = await request[apiMethod](apiUrl, {
        pageNo: currentPage,
        pageSize, // 使用状态中的 pageSize
        ...extraParams,
        ...params,
      });
      setData(response.data.data);
      setTotal(response.data.totalRecords);
      if (getDataTotal) getDataTotal(response.data.totalRecords);
    } catch (error) {
      console.error('Failed to fetch data:', error);
    } finally {
      setLoading(false); // 请求结束后设置 loading 为 false
    }
  };

  useEffect(() => {
    fetchData();
  }, [apiUrl, extraParams, currentPage, updateTrigger]);

  useEffect(() => {
    if (outerPageNo) setCurrentPage(outerPageNo);
  }, [outerPageNo]);

  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page);
    if (size) setPageSize(size); // 更新 pageSize
  };

  const handleSearch = (searchParams: Record<string, any>) => {
    setCurrentPage(1); // 重置到第一页
    fetchData(searchParams);
  };

  const handleReset = () => {
    setCurrentPage(1); // 重置到第一页
    fetchData();
  };

  return (
    <div className="card-table">
      {filterItems && filterItems.length > 0 && (
        <FilterForm
          onSearch={handleSearch}
          inlineButtons
          labelCol={labelCol}
          onReset={handleReset}
          filterItems={filterItems}
          hideActionButtons={hideActionButtons}
          customButtons={customButtons || []}
          buttonCol={buttonCol}
          getFormValues={getFormValues}
          searchButtonStyle={searchButtonStyle}
        />
      )}
      <div className={!hidePagenation ? 'table-wrap' : ''} style={tableWrapStyle}>
        {loading ? (
          <div className="loadin-container">
            <Spin tip="Loading" size="large" />
          </div>
        ) : (
          <>
            {CustomCardComponent ? (
              data.map((item: any) => (
                <CustomCardComponent
                  key={item.id}
                  item={item}
                  cardWidth={cardWidth}
                  viewHandle={() => viewHandle?.(item)}
                  delHandle={delHandle}
                  showDownload={showDownload}
                  {...(cardExtraProps || {})}
                />
              ))
            ) : (
              <div
                className="card-container"
                style={{
                  gridTemplateColumns: `repeat(auto-fill, minmax(${cardWidth || '347px'}, 1fr)`,
                }}
              >
                {data.map((item: any) => (
                  <div className="card-item" key={item.id}>
                    <CardItem
                      item={item}
                      cardWidth={cardWidth}
                      viewHandle={viewHandle}
                      delHandle={delHandle}
                      showDownload={showDownload}
                    />
                  </div>
                ))}
              </div>
            )}
          </>
        )}
      </div>
      {!hidePagenation && (
        <div className="pagination-info pagination-wrap">
          {/* 勿删，用于占位 */}
          <div />
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={total}
            showSizeChanger={showSizeChanger}
            showTotal={total => `共 ${total} 条`}
            onChange={handlePageChange}
            className="pagination"
          />
        </div>
      )}
    </div>
  );
};
export default CardTable;
