import React from 'react';
import { Card, Button } from 'antd';
import defaultSoftIcon from '@/assets/images/default_soft_icon.png';

interface CardItemProps {
  item: any;
  viewHandle?: (item: any) => void;
  delHandle?: (item: any) => void;
  cardWidth?: string;
  showDownload?: boolean;
}
const CardItem: React.FC<CardItemProps> = ({
  item,
  viewHandle,
  delHandle,
  cardWidth,
  showDownload = false,
}) => {
  const iconUrl = item.iconUrl ? `/storage${item.iconUrl}` : undefined;

  // 计算文件大小并转换为合适的单位
  const getSizeString = (sizeInBytes: string) => {
    const size = parseFloat(sizeInBytes);
    if (isNaN(size)) return '未知大小';
    if (size < 0.1 * 1024 * 1024) {
      // 小于 0.1 MB，转换为 KB
      return `${(size / 1024).toFixed(2)} KB`;
    }
    // 转换为 MB
    return `${(size / (1024 * 1024)).toFixed(2)} MB`;
  };
  const sizeString = getSizeString(item.size);

  return (
    <Card hoverable className="card" style={{ width: cardWidth || undefined }}>
      <div className="card-content">
        <div className="card-top">
          <div className="icon">
            <img src={iconUrl || defaultSoftIcon} alt={item.name} style={{ height: '50px' }} />
            <div className="file-size">大小：{sizeString}</div> {/* 添加文件大小信息 */}
          </div>
          <div className="info">
            <div className="title">{item.name}</div>
            <div className="update-time bg-color-tx">更新时间: {item.updateTime}</div>
            <div className="version bg-color-tx">版本: {item.version}</div>
          </div>
        </div>
        <div className="card-middle">
          <div className="bg-color-tx">{item.description}</div>
        </div>
        <div className="card-bottom">
          <Button type="primary" style={{ marginRight: '10px' }} onClick={() => viewHandle?.(item)}>
            查看
          </Button>
          {!showDownload && <Button onClick={() => delHandle?.(item)}>删除</Button>}
          {showDownload && (
            <a href={`/storage${item.fileUrl}`} target="_blank" rel="noopener noreferrer">
              <Button disabled={!item.fileUrl}>下载</Button>
            </a>
          )}
        </div>
      </div>
    </Card>
  );
};
export default CardItem;
