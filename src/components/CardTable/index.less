.card-table {
  height: 100%;
  .card {
    width: 350px;
    height: 175px;
    margin-bottom: 5px;
    .ant-card-body {
      padding: 12px !important;
    }
  }
  .table-wrap {
    overflow: auto;
    height: calc(100% - 96px);
  }
  .card-container {
    display: grid;
    gap: 16px; // 设置元素之间的间距
  }
  .card-item {
    width: 100%; // 让每个元素占据整个网格单元
    box-sizing: border-box;
  }
  .card-content {
    display: flex;
    flex-direction: column;
    height: 100%;

    .card-top {
      display: flex;
      height: 50px;
      font-size: 12px;
      margin-bottom: 15px;
      .icon {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        flex: 1;
        .file-size {
          font-size: 12px;
          color: rgb(124, 124, 124);
          transform: scale(0.9);
        }
      }
      .info {
        flex: 2;
        padding-left: 10px;
      }
    }
  }
  .bg-color-tx {
    color: rgb(124, 124, 124);
  }
  .card-middle {
    overflow: hidden;
    height: 55px;
    font-size: 13px;
    text-overflow: ellipsis;
  }
  .card-bottom {
    display: flex;
    justify-content: flex-end;
  }
  .card-bottom {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;

    button {
      height: 25px;
      width: 40%;
      border-radius: 6px;
    }
    a {
      width: 40%;
      button {
        width: 100%;
      }
    }
  }
  .action-button {
    width: 45%; /* 按钮宽度增加 */
  }
  .pagination-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 5px;
  }
  .loadin-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
  }
}
