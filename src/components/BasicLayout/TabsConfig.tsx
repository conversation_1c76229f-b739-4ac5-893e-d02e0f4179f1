/* eslint-disable */
/* eslint-disable */
import React, { memo } from 'react';
import routes from '../../../config/routes';

const dealFlatRoute = (data: any) => {
  let flatArr: any = [];
  data?.forEach((item: any) => {
    flatArr.push({
      ...item,
    });
    if (item.routes && item.routes.length > 0) {
      flatArr = [...flatArr, ...dealFlatRoute(item.routes)];
    }
  });
  return flatArr;
};

const flatRoutes = dealFlatRoute(routes || []);

const dynamicLoadComponent = (component: any) => {
  if (!component) {
    return require('../../pages/404').default;
  }
  const ass = require(`../../pages/${component}`);
  return ass.default;
};

const getTabComponent = (code: string, routresMap: any) => {

  let newCode = code
  if (code.indexOf('?') > -1) {
    newCode = code.split('?')[0]
  }
  let comp = routresMap[newCode]?.component?.split('/pages/')[1] || '';
  if (routresMap[newCode]?.redirect) {
    comp = routresMap[routresMap[newCode]?.redirect]?.component?.split('/pages/')[1] || '';
    // window.location.href = `/#${routresMap[code]?.redirect}`;
  }
  console.log('getTabComponent comp=====', comp);
  return comp === '404' ? require('../../pages/404').default : dynamicLoadComponent(comp);
};

interface LoadComponentProps {
  pageTabs: any,
  closeTagView: any,
  code: string,
  pageTabItem: any,
}

const routresMap = {};
flatRoutes?.forEach((item: any) => {
  routresMap[item.path] = item;
});

const LoadComponent: React.FC<LoadComponentProps> = ({ pageTabs, closeTagView, code, pageTabItem }) => {
  console.log('菜单tab===', pageTabs)
  const DynamicComp = getTabComponent(code, routresMap);
  return <DynamicComp pageTabs={pageTabs} closeTagView={closeTagView} pageTabItem={pageTabItem} /> || null;
};
export default memo(LoadComponent);
