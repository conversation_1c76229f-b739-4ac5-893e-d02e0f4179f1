import React, { useEffect, useRef, useState } from 'react';
import { notification } from 'antd';
import usePolling, { Notification } from '@/utils/usePolling';
import DefaultDescription from './components/DefaultDescription';

interface NotificationProProps {
  queryUrl: string;
  enableRedirect?: boolean;
  onMessageClick?: (message: any) => void;
  params?: Record<string, any>;
  onGetNotification?: (msg: Notification[]) => void;
}

const NotificationPro: React.FC<NotificationProProps> = ({
  enableRedirect = true,
  onMessageClick,
  queryUrl,
  params = {},
  onGetNotification,
}) => {
  const notifications = usePolling(queryUrl, params, 5000);
  const [clickedIds, setClickedIds] = useState<Set<string | number>>(new Set());
  const notificationKeyRef = useRef<string>('notification_pro_key');
  // 只要 notifications 或 clickedIds 变化就重新 open，利用 key 保持唯一性，实现内容刷新
  useEffect(() => {
    if (notifications?.length > 0) {
      if (onGetNotification) onGetNotification(notifications);
      notification.open({
        key: notificationKeyRef.current,
        message: '通知',
        description: (
          <DefaultDescription
            notifications={notifications}
            clickedIds={clickedIds}
            setClickedIds={setClickedIds}
            onMessageClick={onMessageClick}
            enableRedirect={enableRedirect}
          />
        ),
        placement: 'bottomRight',
        duration: 5,
        showProgress: true,
        pauseOnHover: true,
      });
    }
    // 依赖 notifications 和 clickedIds，保证点击后颜色能实时刷新
  }, [notifications, clickedIds]);
  return null;
};

export default NotificationPro;
