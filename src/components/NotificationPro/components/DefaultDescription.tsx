import React from 'react';

interface NotificationListProps {
  notifications: any[];
  clickedIds: Set<string | number>;
  setClickedIds: React.Dispatch<React.SetStateAction<Set<string | number>>>;
  onMessageClick?: (message: any) => void;
  enableRedirect: boolean;
}

const DefaultDescription: React.FC<NotificationListProps> = ({
  notifications,
  clickedIds,
  setClickedIds,
  onMessageClick,
  enableRedirect,
}) => (
  <div>
    {notifications.map(item => (
      <div
        key={item.id}
        style={{
          cursor: clickedIds.has(item.id) ? 'not-allowed' : 'pointer',
          color: clickedIds.has(item.id) ? '#D0D0D0' : '#1677ff',
          marginBottom: 4,
          userSelect: 'none',
        }}
        onClick={e => {
          e.stopPropagation();
          if (clickedIds.has(item.id)) return;
          setClickedIds(prev => {
            const newSet = new Set(prev);
            newSet.add(item.id);
            return newSet;
          });
          onMessageClick?.(item);
          if (enableRedirect && item.address) {
            window.location.href = item.address;
          }
        }}
      >
        {item.content}
      </div>
    ))}
  </div>
);

export default DefaultDescription;
