import React, { useEffect, useState } from 'react';
import CusDrawer from '../CusDrawer';
import <PERSON><PERSON>hart, { BardDataType } from './components/BarChart';
import PieChart, { PieDataType } from './components/PieChart';
import { AreaChartOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import './index.less';

interface HiddenDangerAnalysisProps {
  title: string;
  analysisData?: {
    allCount?: string | null;
    townsVillageCount?: BardDataType;
    typeCount?: PieDataType;
  };
}
const HiddenDangerAnalysis: React.FC<HiddenDangerAnalysisProps> = ({ title, analysisData }) => {
  const [pieData, setPieData] = useState<any[]>([]);
  const [barData, setBarData] = useState<any[]>([]);
  const [count, setCount] = useState('');
  useEffect(() => {
    if (analysisData && JSON.stringify(analysisData) !== '{}') {
      const { allCount, townsVillageCount = [], typeCount } = analysisData;
      if (townsVillageCount?.length) setBarData(townsVillageCount);
      if (typeCount?.length) setPieData(typeCount);
      if (allCount) setCount(allCount);
    }
  }, [analysisData]);

  return (
    <CusDrawer
      direction="right"
      title={`${title}（共${count}个）`}
      width="30%"
      height="calc(100% - 56px)"
      customButton={<Button className="custom-button" icon={<AreaChartOutlined />} />}
    >
      <div
        style={{ display: 'flex', height: '100%', flexDirection: 'column', alignItems: 'center' }}
      >
        <BarChart data={barData} />
        <PieChart data={pieData} />
      </div>
    </CusDrawer>
  );
};

export default HiddenDangerAnalysis;
