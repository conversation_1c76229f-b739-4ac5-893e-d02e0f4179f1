.info-card {
  position: relative;
  margin-bottom: 16px;
  .title {
    display: flex;
    align-items: center;
    .index-symbol {
      display: inline-block;
      width: 24px;
      height: 24px;
      line-height: 24px;
      border-radius: 50%;
      background-color: #e0e0e0;
      text-align: center;
      margin-right: 8px;
    }
  }
  .column {
    margin-bottom: 8px;
    strong {
      font-weight: bold;
    }
  }
}
.custom-button {
  transform: translateY(-50%);
  width: 50px;
  height: 50px;
  border-radius: 30px 0 0 30px; // 左半边是半圆
  font-size: 22px; // 调大字体
  &.right {
    left: -44px;
  }
  &.left {
    right: -44px;
  }
}
