import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';

export type PieDataType =
  | {
      type: string;
      totalCount: string;
    }[]
  | null;

interface PieChatProps {
  data: PieDataType;
}

const PieChart: React.FC<PieChatProps> = ({ data }) => {
  const pieChartRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    if (pieChartRef.current && data?.length) {
      const pieChart = echarts.init(pieChartRef.current);
      const pieChartOptions = {
        title: {
          text: '按隐患类型统计',
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)',
        },
        series: [
          {
            name: '危险类型',
            type: 'pie',
            radius: '80%',
            center: ['50%', '60%'],
            data: data.map(item => ({
              value: parseInt(item.totalCount, 10),
              name: item.type,
            })),
            label: {
              normal: {
                formatter: '{b}',
                textStyle: {
                  fontSize: 14,
                },
              },
            },
          },
        ],
      };
      pieChart.setOption(pieChartOptions);
    }
  }, [data]);
  return <div ref={pieChartRef} style={{ width: '100%', height: '35%' }} />;
};

export default PieChart;
