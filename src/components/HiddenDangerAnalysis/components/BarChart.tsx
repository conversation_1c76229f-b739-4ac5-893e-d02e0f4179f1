import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';

export type BardDataType =
  | {
      level: string;
      totalCount: string;
      townsVillage: string;
    }[]
  | null;

interface BarChartProps {
  data: BardDataType;
}

const BarChart: React.FC<BarChartProps> = ({ data }) => {
  const barChartRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    if (barChartRef.current && data?.length) {
      const barChart = echarts.init(barChartRef.current);
      // 提取 legend.data 和 yAxis.data
      const levels = Array.from(new Set(data.map(item => item.level)));
      const townsVillages = Array.from(new Set(data.map(item => item.townsVillage)));
      // 构建 series 数据
      const series = levels.map(level => ({
        name: level,
        type: 'bar',
        data: townsVillages.map(town => {
          const item = data.find(d => d.level === level && d.townsVillage === town);
          return item ? parseInt(item.totalCount, 10) : null;
        }),
        barWidth: 10,
        label: {
          show: true,
          position: 'right',
        },
      }));
      const barChartOptions = {
        title: {
          text: '按区域统计',
        },
        tooltip: {},
        grid: {
          left: 50,
          right: 15,
          top: 30,
          bottom: 55,
        },
        legend: {
          data: levels,
          bottom: 0,
        },
        xAxis: {},
        yAxis: {
          data: townsVillages,
        },
        series,
      };
      barChart.setOption(barChartOptions);
    }
  }, [data]);
  return (
    <div
      ref={barChartRef}
      style={{ width: '100%', height: 'calc(65% - 30px)', marginBottom: '30px' }}
    />
  );
};

export default BarChart;
