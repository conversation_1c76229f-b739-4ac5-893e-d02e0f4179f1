import { downloadFile } from '@/utils/commonFileFun';
import { <PERSON><PERSON>, Modal } from 'antd';
import CryptoJS from 'crypto-js';
import React, { useEffect, useState } from 'react';
// import { pdfjs } from 'react-pdf';
// import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
// import 'react-pdf/dist/esm/Page/TextLayer.css';
import './index.less';

// pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`;

const FilePreview: React.FC<{ path: string; open?: boolean; onClose?: () => void }> = ({ path, open, onClose }) => {
  const [title, setTitle] = useState('');
  // const [fileType, setFileType] = useState('');
  // const [pageCurrent, setPageCurrent] = useState(1);
  // const [total, setTotal] = useState(0);
  const [filePreviewUrl, setFilePreviewUrl] = useState('');

  // const onDocumentLoadSuccess = (args: any) => {
  //   setTotal(args.numPages);
  // };

  // const onChangePage = (page: number) => {
  //   setPageCurrent(page);
  // };

  useEffect(() => {
    if (path) {
      const urlParams = new URLSearchParams(path);
      const fileName = urlParams.get('fileName');
      // const suffix = getFileType(fileName || '', path);
      // setFileType(suffix);
      setTitle(fileName as string);
      const trans = CryptoJS.enc.Utf8.parse(downloadFile(path));
      setFilePreviewUrl(`${PRIVIEW_SERVER}?url=${encodeURIComponent(CryptoJS.enc.Base64.stringify(trans))}`);
    }
  }, [path]);

  return (
    <Modal
      className="file-preview-modal"
      title={title}
      width={854}
      styles={{ body: { height: 600 } }}
      open={open}
      onCancel={onClose}
      footer={[
        // fileType === 'pdf' ? (
        //   <Pagination current={pageCurrent} pageSize={1} total={total} size="small" onChange={onChangePage} />
        // ) : (
        //   <div style={{ flex: 1 }} />
        // ),
        <div style={{ flex: 1 }} />,
        <Button onClick={onClose}>关闭</Button>,
      ]}
    >
      {/* {fileType === 'pdf' ? (
        <div className="pdf-container">
          <Document file={`/storage${path}`} onLoadSuccess={onDocumentLoadSuccess} loading={<Spin size="large" />}>
            <Page pageNumber={pageCurrent} width={800} loading={<Spin size="large" />} />
          </Document>
        </div>
      ) : (
        <Image src={`/storage${path}`} width="100%" height="100%" />
      )} */}
      <iframe title="preview" src={filePreviewUrl} height="100%" width="100%" />
    </Modal>
  );
};

export default FilePreview;
