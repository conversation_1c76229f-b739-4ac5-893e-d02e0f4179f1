// src/components/HiddenDangerDrawer/components/TitleExtraButtons.tsx
import React from 'react';
import { Button, Dropdown, Menu, Upload, message } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import { exportFile } from '@/utils/commonFunction';

interface TitleExtraButtonsProps {
  importUrl: string;
  onImport?: () => void;
  exportUrl: string;
  handleExport: () => void;
  setUpdateTrigger: React.Dispatch<React.SetStateAction<boolean>>;
  updateTrigger: boolean;
  formValues: Record<string, any>;
  exportFileTitle: string;
  hideAllButtons: boolean;
  refreshHandle?: () => Promise<void>;
  templateUrl: string; // 模板下载地址参数
}

const TitleExtraButtons: React.FC<TitleExtraButtonsProps> = ({
  importUrl,
  onImport,
  handleExport,
  setUpdateTrigger,
  updateTrigger,
  exportUrl,
  formValues,
  exportFileTitle,
  hideAllButtons = false,
  templateUrl,
  refreshHandle,
}) => {
  const onExport = () => {
    exportFile(formValues, exportUrl, exportFileTitle);
    handleExport?.();
  };

  const uploadProps = {
    action: importUrl,
    headers: {
      access_token: localStorage.getItem('jishan_token') || '',
    },
    name: 'files',
    showUploadList: false,
    onChange: ({ file }: any) => {
      if (file.status === 'done') {
        message.success('文件上传成功');
        setUpdateTrigger(!updateTrigger);
        onImport?.();
        refreshHandle?.();
      } else if (file.status === 'error') {
        message.error('文件上传失败');
      }
    },
  };

  const importMenu = (
    <Menu>
      <Menu.Item key="download">
        <a
          href={`/storage/api/downloadFile?bucket=portal&fileName=${templateUrl}`}
          target="_blank"
          rel="noopener noreferrer"
        >
          模板下载
        </a>
      </Menu.Item>
      <Menu.Item key="upload">
        <Upload {...uploadProps}>
          <span>导入数据</span>
        </Upload>
      </Menu.Item>
    </Menu>
  );

  return (
    <>
      {!hideAllButtons && (
        <>
          <Dropdown overlay={importMenu}>
            <Button type="primary">
              导入 <DownOutlined />
            </Button>
          </Dropdown>
          <Button onClick={onExport} type="primary">
            导出
          </Button>
        </>
      )}
    </>
  );
};

export default TitleExtraButtons;
