import React from 'react';
import { Card, Button, Tag } from 'antd';
import '../index.less';

interface Column {
  label: string;
  key: string;
  render?: (v: any) => any;
}
interface InfoCardProps {
  columns: Column[];
  item: Record<string, any>;
  title: string | { key: string };
  tagKey?: string;
  colorRender?: (tagContent: string) => string;
  indexKey?: string;
  viewHandle: (item: any) => void; // 父组件传入的编辑事件处理函数
  delHandle: (item: any) => void; // 父组件传入的删除事件处理函数
}
const InfoCard: React.FC<InfoCardProps> = ({
  columns,
  item,
  title,
  tagKey,
  colorRender,
  indexKey,
  viewHandle,
  delHandle,
}) => {
  const getIndexSymbol = (index: string) => {
    return <span className="index-symbol">{parseInt(index, 10)}</span>;
  };
  const index = indexKey ? item[indexKey] : undefined;
  const indexSymbol = index !== undefined ? getIndexSymbol(index) : null;
  const cardTitle = typeof title === 'string' ? title : item[title.key];
  const tagContent = tagKey ? item[tagKey] : undefined;
  const tagColor = colorRender && tagContent ? colorRender(tagContent) : 'blue';

  return (
    <Card
      title={
        <div className="title">
          {indexSymbol} {cardTitle}
          {tagContent && (
            <Tag color={tagColor} className="tag">
              {tagContent}
            </Tag>
          )}
        </div>
      }
      className="info-card"
    >
      {columns.map(column => (
        <div key={column.key} className="column">
          <strong>{column.label}: </strong>
          {column.render ? column.render(item[column.key]) : item[column.key]}
        </div>
      ))}
      <div className="actions">
        <Button type="primary" onClick={viewHandle}>
          修改
        </Button>
        <Button type="primary" onClick={() => delHandle(item)}>
          删除
        </Button>
      </div>
    </Card>
  );
};
export default InfoCard;
