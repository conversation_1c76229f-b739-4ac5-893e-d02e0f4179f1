import React from 'react';
import { Button } from 'antd';
import { PlusSquareOutlined } from '@ant-design/icons';
import '../index.less';

interface AddButtonProps {
  onClick: () => void;
  hideAllButtons: boolean;
}

const AddButton: React.FC<AddButtonProps> = ({ onClick, hideAllButtons }) => {
  return !hideAllButtons ? (
    <Button className="hidden-danger-add-btn" onClick={onClick} icon={<PlusSquareOutlined />} />
  ) : (
    ''
  );
};

export default AddButton;
