import React from 'react';
import { Button } from 'antd';
import { LeftOutlined } from '@ant-design/icons';
import '../index.less';

interface BackButtonProps {
  onClick: () => void;
  showForm: boolean;
}

const BackButton: React.FC<BackButtonProps> = ({ onClick, showForm }) => {
  return showForm ? (
    <Button className="back-btn" onClick={onClick} type="link" icon={<LeftOutlined />} />
  ) : (
    ''
  );
};

export default BackButton;
