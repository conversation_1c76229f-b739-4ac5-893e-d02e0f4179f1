.info-card {
  position: relative;
  margin-bottom: 16px;
  .title {
    display: flex;
    align-items: center;
    .index-symbol {
      display: inline-block;
      width: 24px;
      height: 24px;
      line-height: 24px;
      border-radius: 50%;
      background-color: #e0e0e0;
      text-align: center;
      margin-right: 8px;
    }
    .tag {
      margin-left: auto;
    }
  }
  .column {
    margin-bottom: 8px;
    strong {
      font-weight: bold;
    }
  }
  .actions {
    width: 100%;
    text-align: right;
    right: 16px;
    button {
      margin-right: 8px;
    }
  }
}

.back-btn {
  position: absolute;
  left: 20px;
}

.hidden-danger-add-btn {
  position: absolute;
  left: 360px;
  bottom: 50px;
  width: 45px !important;
  height: 45px;
  font-size: 22px;
  color: rgb(12, 130, 226);
}

.hidden-danger-form {
  height: calc(100% - 10px);
  .danger-form_form-items {
    padding-right: 6px;
    height: calc(100% - 16px);
    margin-bottom: 10px;
    overflow: auto;
    .ant-form-item {
      margin-bottom: 8px;
    }
  }
  .danger-form_footer {
    width: 100%;
    display: flex;
    justify-content: center;
  }
}
