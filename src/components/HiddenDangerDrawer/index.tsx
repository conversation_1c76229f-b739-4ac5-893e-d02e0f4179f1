// src/components/HiddenDangerDrawer/index.tsx
import React, { useState } from 'react';
import CusDrawer from '../CusDrawer';
import { Button, Modal, Form } from 'antd';
import CardTable from '../CardTable';
import InfoCard from './components/InfoCard';
import TitleExtraButtons from './components/TitleExtraButtons';
import './index.less';
import BackButton from './components/BackButton';
import AddButton from './components/AddButton';

interface HiddenDangerDrawerProps {
  title: string;
  formTitle?: string;
  columns: { label: string; key: string }[];
  filterItems: any[];
  onImport?: () => void;
  onExport?: () => void;
  exportFileTitle?: string;
  exportUrl: string;
  importUrl: string;
  onEdit: (item: any) => Promise<boolean>;
  getEditInitValues: (id: string) => Promise<boolean | Record<string, any>>;
  onAdd: (formValues: any) => Promise<boolean>;
  onDelete: (item: any) => Promise<boolean>;
  formItems: React.ReactNode;
  listUrl: string;
  refreshHandle?: () => Promise<void>;
  tagKey?: string;
  colorRender?: (tagContent: string) => string;
  templateUrl: string;
}

let currentId = '';

const HiddenDangerDrawer: React.FC<HiddenDangerDrawerProps> = ({
  title,
  formTitle = '新增隐患管理',
  filterItems,
  columns,
  onImport,
  onExport,
  exportUrl,
  importUrl,
  onEdit,
  onAdd,
  onDelete,
  refreshHandle,
  exportFileTitle,
  getEditInitValues,
  formItems,
  listUrl,
  tagKey,
  colorRender,
  templateUrl,
}) => {
  const [formValues, setFormValues] = useState<Record<string, any>>({});
  const [updateTrigger, setUpdateTrigger] = useState(false);
  const [hideAllButtons, setHideAllButtons] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [formLoading, setFormLoading] = useState(false);
  const [editType, setEditType] = useState<'edit' | 'add'>('add');
  const [form] = Form.useForm();

  const handleExport = () => {
    onExport?.();
  };

  const editHandle = async (item: any) => {
    setFormLoading(true);
    const queryFlag = await getEditInitValues(item.id);
    setFormLoading(false);
    if (typeof queryFlag === 'object') {
      currentId = item.id;
      setHideAllButtons(true);
      setShowForm(true);
      setEditType('edit');
      form.setFieldsValue(queryFlag);
    }
  };

  const addHandle = () => {
    form.resetFields();
    setHideAllButtons(true);
    setShowForm(true);
    setEditType('add');
  };

  const resetHandle = () => {
    setHideAllButtons(false);
    setShowForm(false);
    form.resetFields();
  };

  const handleFormSubmit = async () => {
    try {
      setFormLoading(true); // 开始加载
      const values = await form.validateFields();
      if (editType === 'add') {
        const addRes = await onAdd(values);
        if (addRes) resetHandle();
      } else {
        const editRes = await onEdit(Object.assign(values, { id: currentId }));
        if (editRes) resetHandle();
      }
    } catch (error) {
      console.error('Form Validation Failed:', error);
    } finally {
      setFormLoading(false); // 结束加载
      refreshHandle?.();
    }
  };

  const deleteHandle = (item: any) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这条记录吗？',
      onOk: async () => {
        const deleteRes = await onDelete(item);
        if (deleteRes) setUpdateTrigger(!updateTrigger);
        refreshHandle?.();
      },
    });
  };
  return (
    <>
      <CusDrawer
        direction="left"
        title={showForm ? formTitle : title}
        contentBeforeTitle={<BackButton showForm={showForm} onClick={resetHandle} />}
        ExtraButtons={<AddButton hideAllButtons={hideAllButtons} onClick={addHandle} />}
        headerStyle={showForm ? { justifyContent: 'center' } : {}}
        hideAllButtons={hideAllButtons}
        height={showForm ? 'calc(100% - 58px)' : ''}
        titleExtra={
          <TitleExtraButtons
            importUrl={importUrl}
            onImport={onImport}
            hideAllButtons={hideAllButtons}
            handleExport={handleExport}
            setUpdateTrigger={setUpdateTrigger}
            updateTrigger={updateTrigger}
            formValues={formValues}
            exportUrl={exportUrl}
            exportFileTitle={exportFileTitle || '列表数据'}
            refreshHandle={refreshHandle}
            templateUrl={templateUrl}
          />
        }
      >
        {showForm ? (
          <Form form={form} layout="vertical" disabled={formLoading} className="hidden-danger-form">
            <div className="danger-form_form-items">{formItems}</div>
            <div className="danger-form_footer">
              <Button type="primary" onClick={handleFormSubmit} loading={formLoading}>
                确定
              </Button>
              <Button onClick={resetHandle} style={{ marginLeft: '18px' }}>
                取消
              </Button>
            </div>
          </Form>
        ) : (
          <CardTable
            filterItems={filterItems}
            apiUrl={listUrl}
            viewHandle={editHandle}
            labelCol={{ span: 8 }}
            delHandle={deleteHandle}
            customCardComponent={InfoCard as any}
            cardExtraProps={{
              columns,
              title: { key: 'name' },
              indexKey: 'rowId',
              tagKey,
              colorRender,
            }}
            updateTrigger={updateTrigger}
            showSizeChanger={false}
            hideActionButtons
            getFormValues={setFormValues}
            tableWrapStyle={{ justifyContent: 'normal' }}
          />
        )}
      </CusDrawer>
    </>
  );
};
export default HiddenDangerDrawer;
