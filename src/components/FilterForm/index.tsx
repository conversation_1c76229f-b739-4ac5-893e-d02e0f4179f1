import React, { useImperativeHandle, forwardRef } from 'react';
import {
  Form,
  Row,
  Col,
  Button,
  ColProps,
  FormInstance,
  Select,
  DatePicker,
  TreeSelect,
} from 'antd';
import ActionButtons from './ActionButtons'; // 导入新封装的按钮组件
import styles from './index.less';
import { getOptionsFromDict } from '@/utils/commonFunction';
import FixedWidthSelect from '../BaseFormComponents/FixedWidthSelect';
import FixedWidthRangePicker from '../BaseFormComponents/RangePicker';
import SearchInput from '../BaseFormComponents/SearchInput';

export interface CustomButton {
  text: string;
  bgColor?: string;
  color?: string;
  onClick: (form?: FormInstance<any>) => void;
}

export type FormItem = {
  name: string;
  label: string;
  component: React.ReactNode;
  span?: number;
  labelCol?: ColProps;
  showLabel?: boolean;
  extraProps?: Record<string, any>;
};

type VoidFn = () => void;

interface RefComp {
  resetHandle: VoidFn;
}

interface FilterFormProps {
  onSearch: (values: any) => void;
  onReset: () => void;
  filterItems: Array<FormItem>;
  inlineButtons?: boolean;
  customButtons?: CustomButton[];
  buttonCol?: number;
  searchButtonStyle?: React.CSSProperties;
  labelCol?: ColProps;
  customCompBeforForm?: any;
  comBeforeFormCol?: number;
  hideActionButtons?: boolean;
  getFormValues?: React.Dispatch<React.SetStateAction<Record<string, any>>>;
}
const FilterForm = forwardRef<RefComp, FilterFormProps>(
  (
    {
      onSearch,
      onReset,
      filterItems,
      inlineButtons,
      customButtons,
      buttonCol,
      searchButtonStyle = {},
      labelCol,
      customCompBeforForm,
      comBeforeFormCol,
      hideActionButtons = false,
      getFormValues,
    },
    ref,
  ) => {
    const [form] = Form.useForm();
    const handleSearch = () => {
      form.validateFields().then(values => {
        onSearch(values);
        if (getFormValues) getFormValues(values);
      });
    };

    const resetHandle = () => {
      form.resetFields();
      if (onReset) onReset();
    };

    useImperativeHandle(ref, () => ({
      resetHandle,
    }));

    const getLabelCol = (item: FormItem) => {
      if (item.showLabel) {
        if (item.labelCol) return { labelCol: item.labelCol };
        if (labelCol) return { labelCol };
      }
      return { labelCol: { span: 0 } };
    };

    const onCustomButtonClick = (button: CustomButton) => {
      button.onClick(form);
    };

    const getEventHandler = (component: any) => {
      switch (component.type) {
        case Select:
        case FixedWidthSelect:
        case DatePicker:
        case TreeSelect:
        case FixedWidthRangePicker:
          return { onChange: handleSearch };
        case SearchInput:
          return { onSearch: handleSearch };
        default:
          return { onBlur: handleSearch };
      }
    };

    return (
      <Form form={form} layout="inline">
        <Row gutter={[16, 8]} className={styles.row}>
          {customCompBeforForm ? <Col span={comBeforeFormCol || 8}>{customCompBeforForm}</Col> : ''}
          {filterItems.map(item => (
            <Col key={item.name} span={item.span || undefined}>
              <Form.Item
                name={item.name}
                label={item.label}
                className={styles.formItem}
                {...getLabelCol(item)}
                {...(item.extraProps || {})}
              >
                {/* 使用提取的事件处理函数 */}
                {/* @ts-ignore */}
                {React.cloneElement(item.component, getEventHandler(item.component))}
              </Form.Item>
            </Col>
          ))}
          {!hideActionButtons && // 根据 hideActionButtons 控制按钮是否显示
            (inlineButtons ? (
              <Col
                span={buttonCol || 8}
                className={styles.actionBtns}
                style={{ textAlign: 'right', ...searchButtonStyle }}
              >
                <ActionButtons onReset={resetHandle} />
              </Col>
            ) : (
              <Col span={24} className={styles.buttonCol}>
                <ActionButtons onReset={resetHandle} />
              </Col>
            ))}
          {customButtons && customButtons.length > 0 && (
            <div className={styles.customButtonContainer}>
              {customButtons.map((button, index) => (
                <Button
                  key={index}
                  style={{
                    backgroundColor: button.bgColor,
                    marginRight: '8px',
                    color: button.color,
                  }}
                  onClick={() => onCustomButtonClick(button)}
                >
                  {button.text}
                </Button>
              ))}
            </div>
          )}
        </Row>
      </Form>
    );
  },
);
export default FilterForm;

export const updateFormFilterSelect = (
  filterIndex: number,
  data: any,
  formFilters: any,
  placeholder = '请选择',
) => {
  if (data) {
    formFilters[filterIndex].component = (
      <FixedWidthSelect options={getOptionsFromDict(data)} placeholder={placeholder} />
    );
  }
};
