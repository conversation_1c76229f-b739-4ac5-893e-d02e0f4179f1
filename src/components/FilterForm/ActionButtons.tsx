import React from 'react';
import { Button, Form } from 'antd';

interface ActionButtonsProps {
  onReset: () => void;
}
const ActionButtons: React.FC<ActionButtonsProps> = ({ onReset }) => {
  return (
    <Form.Item style={{ display: 'flex' }}>
      {/* <Button type="primary" onClick={onSearch}>
        查询
      </Button> */}
      <Button onClick={onReset} style={{ marginLeft: 8 }}>
        重置
      </Button>
    </Form.Item>
  );
};
export default ActionButtons;
