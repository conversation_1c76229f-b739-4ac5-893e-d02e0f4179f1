import React from 'react';
import { Button } from 'antd';
import { PaperClipOutlined, DownloadOutlined, DeleteOutlined } from '@ant-design/icons';
import './index.less';

interface FileItemProps {
  fileName: string;
  downloadUrl: string;
  onDelete: () => void;
}

const OnlyReadUpload: React.FC<FileItemProps> = ({ fileName, downloadUrl, onDelete }) => {
  return (
    <div className="file-item">
      <PaperClipOutlined className="file-icon" />
      <span className="file-name">{fileName}</span>
      <div className="file-actions">
        <a href={`/storage${downloadUrl}`} download>
          <Button
            type="link"
            icon={<DownloadOutlined style={{ color: '#9c9c9c', fontSize: '12px' }} />}
          />
        </a>
        <Button
          type="link"
          icon={<DeleteOutlined style={{ color: '#9c9c9c', fontSize: '12px' }} />}
          onClick={onDelete}
        />
      </div>
    </div>
  );
};
export default OnlyReadUpload;
