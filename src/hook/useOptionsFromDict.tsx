// src/hooks/useOptionsFromDict.ts
import { useEffect, useState } from 'react';
import useDict from '@/utils/useDict';
import { getOptionsFromDict } from '@/utils/commonFunction';

const useOptionsFromDict = (dictCode: string) => {
  const { value: dictValue } = useDict(dictCode);
  const [options, setOptions] = useState<any[]>([]);

  useEffect(() => {
    if (dictValue.length > 0) {
      setOptions(getOptionsFromDict(dictValue));
    }
  }, [dictValue]);
  return options;
};

export default useOptionsFromDict;
