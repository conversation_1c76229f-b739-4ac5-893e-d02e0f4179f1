import React, { useEffect, useState } from 'react';
import { createCodeLabelMap, fetchOptions, getOptionsFromDict } from '@/utils/commonFunction';
import { getAlertTypeList, getAlertLevelist } from '@/services/systemMaintenance/alert';
import useDict from '@/utils/useDict';
import FixedWidthSelect from '@/components/BaseFormComponents/FixedWidthSelect';

export const useAlertConfig = (
  formFilters: any[],
  columns: any[],
  setColumns: React.Dispatch<React.SetStateAction<any[]>>,
  filtersIndex: { typeIndex: number; levelIndex: number },
) => {
  const { value: warnSource } = useDict('warn_source');
  const [typeOptions, setTypeOptions] = useState<any[]>([]);
  const [levelOptions, setLevelOptions] = useState<any[]>([]);

  const updateColumnRender = (options: any[], dataIndex: string) => {
    if (options.length > 0) {
      const newColumns = [...columns];
      const column = newColumns.find(col => col.dataIndex === dataIndex);
      if (column) {
        const map = createCodeLabelMap(options);
        column.renderTooltip = (code: string) => map[code];
        column.render = (code: string) => <span>{map[code]}</span>;
        setColumns(newColumns);
      }
    }
  };

  useEffect(() => {
    updateColumnRender(typeOptions, 'type');
  }, [typeOptions]);

  useEffect(() => {
    if (warnSource && warnSource.length > 0) {
      const sourceOptions = getOptionsFromDict(warnSource);
      updateColumnRender(sourceOptions, 'source');
    }
  }, [warnSource]);

  useEffect(() => {
    updateColumnRender(levelOptions, 'level');
  }, [levelOptions]);

  // 设置下拉框选项
  const setAlertTypeOptions = () => {
    const { typeIndex } = filtersIndex;
    formFilters[typeIndex].component = (
      <FixedWidthSelect placeholder="请选择预警类型" allowClear options={typeOptions} />
    );
  };

  const setAlertLevelOptions = () => {
    const { levelIndex } = filtersIndex;
    formFilters[levelIndex].component = (
      <FixedWidthSelect placeholder="请选择预警级别" allowClear options={levelOptions} />
    );
  };

  useEffect(() => {
    fetchOptions(getAlertTypeList, setTypeOptions, 'name', 'encoding');
    fetchOptions(getAlertLevelist, setLevelOptions, 'name', 'id');
  }, []);

  useEffect(() => {
    setAlertTypeOptions();
    setAlertLevelOptions();
  }, [typeOptions, levelOptions]);

  return {
    setAlertTypeOptions,
    setAlertLevelOptions,
  };
};
