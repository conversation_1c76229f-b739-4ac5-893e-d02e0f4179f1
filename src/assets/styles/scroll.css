:root {
  --scroll-theme-color: rgba(0, 0, 0, 0.15);
}

/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

/* 滚动槽 */
/* ::-webkit-scrollbar-track {
    box-shadow   : inset 0 0 6px #0000004d;
    border-radius: 10px;
  } */
/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  background-color: var(--scroll-theme-color);
  border-radius: 8px;
  /* background-color  : #323539; */
  /* -webkit-box-shadow: inset 0 0 6px #00000080; */
}

::-webkit-scrollbar-thumb:window-inactive {
  background-color: var(--scroll-theme-color);
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--scroll-theme-color);
  border-radius: 5px;
}

::-webkit-scrollbar-corner {
  /* background-color: #0d3148; */
  display: none !important;
}

/* 应用于所有情况
  @-moz-document url-prefix(chrome://), url-prefix(about:),
  url-prefix(file:///), url-prefix(http://), url-prefix(https://){
  */
/* 滚动条背景基本样式 */
scrollbar {
  /* 滚动条背景图案不显示 */
  position: relative !important;
  z-index: 999999999 !important;
  /* 更改滚动条的定位方式为相对 */
  overflow: hidden !important;
  background-color: transparent !important;
  /* 滚动条背景透明 */
  background-image: none !important;
  -moz-appearance: none !important;
  /* 把滚动条提到Z轴最上层 */
}

/* 滚动条按钮基本样式 */
scrollbar thumb {
  background-color: #3167d5 !important;
  border: 0px !important;
  /* 滚动条按钮边框 */
  border-color: #0064ff1a !important;
  border-radius: 10px !important;
  -moz-appearance: none !important;
  /* 滚动条按钮边框颜色和透明度 */
}

/* 滚动条按钮:鼠标悬停与点击拖动时基本样式 */
scrollbar:hover thumb,
scrollbar thumb:hover,
scrollbar thumb:active {
  background-color: #3167d5 !important;
  border: 0px !important;
}

/* 垂直滚动条 */
/* 把滚动条位置移到屏幕外，这里的像素应该等于垂直滚动条宽度的负值 */
scrollbar[orient='vertical'] {
  min-width: 5px !important;
  max-width: 5px !important;
  margin-left: -5px !important;
}

/* 垂直滚动条按钮的左边框样式 */
scrollbar thumb[orient='vertical'] {
  border-style: none none none solid !important;
}

/* 水平滚动条 */
/* 把滚动条位置移到屏幕外，这里的像素应该等于垂直滚动条宽度的负值 */
scrollbar[orient='horizontal'] {
  height: 2px !important;
  min-height: 2px !important;
  max-height: 2px !important;
  margin-top: -5px !important;
}

/* 水平滚动条按钮的上边框样式 */
scrollbar thumb[orient='horizontal'] {
  border-style: solid none none none !important;
}

/* 去除垂直与水平滚动条相交汇的角落 */
scrollbar scrollcorner {
  display: none !important;
}

/* 滚动条两端按钮不显示 */
scrollbar scrollbarbutton {
  display: none !important;
}
