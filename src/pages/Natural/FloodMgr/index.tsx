import React, { useEffect, useState } from 'react';
import { columns, filterItems } from './constants';
import HiddenDangerDrawer from '@/components/HiddenDangerDrawer';
import HiddenDangerAnalysis from '@/components/HiddenDangerAnalysis';
import { addFloodInfo, floodAnalysis, floodDetail, removeFlood } from '@/services/natural';
import { message } from 'antd';
import useOptionsFromDict from '@/hook/useOptionsFromDict';
import { createCodeLabelMap, getCurrentFormattedTime } from '@/utils/commonFunction';
import AddForm from './components/AddForm';
import moment from 'moment';
import FixedWidthSelect from '@/components/BaseFormComponents/FixedWidthSelect';

const FloodMgr: React.FC = () => {
  const regionOptions = useOptionsFromDict('warn_owning_region');
  const floodOptios = useOptionsFromDict('warn_flood_type');
  const [formFilters, setFormFilters] = useState<any[]>([...filterItems]);
  const [analysisData, setAnalysisData] = useState<any>({});
  const [tableColumns, setTableColumns] = useState<any[]>([...columns]);

  useEffect(() => {
    if (regionOptions.length > 0) {
      const updatedFormFilters = [...formFilters];
      updatedFormFilters[2].component = (
        <FixedWidthSelect options={regionOptions} placeholder="请选择隐患所属乡镇" />
      );
      setFormFilters(formFilters);
    }
  }, [regionOptions]);

  useEffect(() => {
    formFilters[1].component = (
      <FixedWidthSelect options={floodOptios} placeholder="请选择隐患类型" />
    );
    setFormFilters(formFilters);

    const newColumns = [...columns];
    const column = newColumns.find(col => col.key === 'type') as any;
    const typeMap = createCodeLabelMap(floodOptios);
    column.render = (v: any) => typeMap[v];
    setTableColumns(newColumns);
  }, [floodOptios]);

  const queryAnalysisDetail = async () => {
    const res = await floodAnalysis();
    if (res.code === '200') {
      setAnalysisData(res.data || {});
    } else message.error(res.msg);
  };

  useEffect(() => {
    queryAnalysisDetail();
  }, []);

  const getEditInitValues = async (id: string) => {
    const res = await floodDetail(id);
    if (res.code === '200') {
      const { data = {} } = res;
      // 将日期字符串转换为 moment 对象
      return {
        ...data,
        rectificationDate: data.rectificationDate ? moment(data.rectificationDate) : null,
        checkDate: data.checkDate ? moment(data.checkDate) : null,
      };
    }
    message.error(res.msg);
    return false;
  };

  const onDelete = async (item: any) => {
    const { id } = item;
    const res = await removeFlood(id);
    if (res.code === '200') return true;
    message.error(res.msg);
    return false;
  };

  const onAdd = async (values: any) => {
    // 将 moment 对象转换回字符串格式
    const formattedValues = {
      ...values,
      rectificationDate: values.rectificationDate
        ? values.rectificationDate.format('YYYY-MM-DD HH:mm:ss')
        : null,
      checkDate: values.checkDate ? values.checkDate.format('YYYY-MM-DD HH:mm:ss') : null,
    };
    const res = await addFloodInfo(formattedValues);
    if (res.code === '200') {
      message.success('保存成功！');
      return true;
    }
    message.error(res.msg);
    return false;
  };

  const colorRender = (v: string) => {
    if (v === '一般隐患') return '';
    if (v === '重大隐患') return 'red';
    return 'blue';
  };

  return (
    <div className="common-page">
      <HiddenDangerDrawer
        onDelete={onDelete}
        onEdit={onAdd}
        onAdd={onAdd}
        templateUrl="洪涝灾害隐患信息导入.xlsx&objectName=20250512/4814c38b-5bf5-4f5a-b433-0ad4239b7c33.xlsx"
        refreshHandle={queryAnalysisDetail}
        exportUrl={`${ALARMPREFIX}/warning/naturalDisaster/flood/exportFlood`}
        importUrl={`${ALARMPREFIX}/warning/naturalDisaster/flood/importFlood`}
        columns={tableColumns}
        filterItems={formFilters}
        title="洪涝灾害隐患管理"
        tagKey="level"
        colorRender={colorRender}
        formItems={<AddForm floodTypeOptions={floodOptios} regionOptions={regionOptions} />}
        exportFileTitle={`洪涝隐患管理统计数据 ${getCurrentFormattedTime()}`}
        getEditInitValues={getEditInitValues}
        listUrl={`${ALARMPREFIX}/warning/naturalDisaster/flood/floodList`}
      />
      <HiddenDangerAnalysis title="数据统计" analysisData={analysisData} />
    </div>
  );
};

export default FloodMgr;
