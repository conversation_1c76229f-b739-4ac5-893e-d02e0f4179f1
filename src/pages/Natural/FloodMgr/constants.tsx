import React from 'react';
import SearchInput from '@/components/BaseFormComponents/SearchInput';

export const filterItems = [
  {
    name: 'name',
    label: '隐患名称',
    component: <SearchInput placeholder="请输入隐患名称" />,
    showLabel: true,
    extraProps: {
      labelAlign: 'left',
    },
    span: 24,
  },
  {
    name: 'type',
    label: '隐患类型',
    component: <SearchInput placeholder="请输入" />,
    showLabel: true,
    labelAlign: 'left',
    extraProps: {
      labelAlign: 'left',
    },
    span: 24,
  },
  {
    name: 'level',
    labelAlign: 'left',
    showLabel: true,
    label: '隐患所属乡镇',
    component: <SearchInput placeholder="请输入" />,
    extraProps: {
      labelAlign: 'left',
    },
    span: 24,
  },
];

export const columns = [
  { label: '隐患类型', key: 'type' },
  { label: '所属单位', key: 'unit' },
  { label: '详细地址', key: 'address' },
];
