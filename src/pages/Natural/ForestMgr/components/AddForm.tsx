import React from 'react';
import { Col, Form, Input, Select, DatePicker } from 'antd';
import { commonPlaceHolder, requiredRule } from '@/utils/commonFunction';
import { nameLengthRules, phoneLengthRules, phoneRules, textAreaLengthRules } from '@/utils/form';

interface FormSectionProps {
  forestTypeOptions: any[];
  regionOptions: any[];
}
const AddForm: React.FC<FormSectionProps> = ({ forestTypeOptions, regionOptions }) => {
  return (
    <>
      <Col span={24}>
        <Form.Item
          label="隐患名称"
          name="name"
          rules={[requiredRule('请输入隐患名称'), nameLengthRules]}
        >
          <Input placeholder={commonPlaceHolder('隐患名称')} />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item label="隐患类型" name="type" rules={[requiredRule('请选择隐患类型')]}>
          <Select options={forestTypeOptions} placeholder="请选择隐患类型" />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item label="隐患等级" name="level" rules={[requiredRule('请选择隐患等级')]}>
          <Select
            options={[
              { label: '一般隐患', value: '一般隐患' },
              { label: '重大隐患', value: '重大隐患' },
            ]}
            placeholder="请选择隐患等级"
          />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item
          label="火灾区划等级"
          name="areaLevel"
          rules={[requiredRule('请选择火灾区划等级')]}
        >
          <Select
            options={[
              { label: '一般火险区', value: '一般火险区' },
              { label: '重要火灾区', value: '重要火灾区' },
            ]}
            placeholder="请选择火灾区划等级"
          />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item
          label="隐患所属乡镇"
          name="townsVillage"
          rules={[requiredRule('请选择隐患所属乡镇')]}
        >
          <Select options={regionOptions} placeholder="请选择隐患所属乡镇" />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item
          label="隐患地点"
          name="place"
          rules={[requiredRule('请输入隐患地点'), textAreaLengthRules]}
        >
          <Input.TextArea placeholder={commonPlaceHolder('隐患地点')} />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item label="经度" name="longitude" rules={[requiredRule('请输入经度')]}>
          <Input placeholder={commonPlaceHolder('经度')} />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item label="纬度" name="latitude" rules={[requiredRule('请输入纬度')]}>
          <Input placeholder={commonPlaceHolder('纬度')} />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item
          label="隐患具体描述"
          name="description"
          rules={[requiredRule('请输入隐患具体描述'), textAreaLengthRules]}
        >
          <Input.TextArea placeholder={commonPlaceHolder('隐患具体描述')} />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item
          label="隐患排查日期"
          name="checkDate"
          rules={[requiredRule('请选择隐患排查日期')]}
        >
          <DatePicker placeholder="请选择隐患排查日期" />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item label="隐患整改类别" name="rectification">
          <Select
            options={[
              { label: '立即整改', value: '立即整改' },
              { label: '限期整改', value: '限期整改' },
              { label: '长期推进', value: '长期推进' },
            ]}
            placeholder="请选择隐患整改类别"
          />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item
          label="负责人"
          name="dutyPerson"
          rules={[requiredRule('请输入负责人'), nameLengthRules]}
        >
          <Input placeholder={commonPlaceHolder('责任人')} />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item label="负责人电话" name="dutyPhone" rules={[phoneRules, phoneLengthRules]}>
          <Input placeholder={commonPlaceHolder('责任人电话')} />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item label="隐患整改时限" name="rectificationDate">
          <DatePicker placeholder="请选择隐患整改时限" />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item label="备注" name="remark" rules={[textAreaLengthRules]}>
          <Input.TextArea placeholder={commonPlaceHolder('备注')} />
        </Form.Item>
      </Col>
    </>
  );
};
export default AddForm;
