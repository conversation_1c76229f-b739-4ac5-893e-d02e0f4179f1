import React from 'react';
import SearchInput from '@/components/BaseFormComponents/SearchInput';

export const filterItems = [
  {
    name: 'name',
    label: '隐患名称',
    component: <SearchInput placeholder="请输入隐患名称" />,
    showLabel: true,
    extraProps: {
      labelAlign: 'left',
    },
    span: 24,
  },
  {
    name: 'type',
    label: '隐患类型',
    component: <SearchInput placeholder="请输入" />,
    labelAlign: 'left',
    showLabel: true,
    extraProps: {
      labelAlign: 'left',
    },
    span: 24,
  },
  {
    name: 'townsVillage',
    labelAlign: 'left',
    showLabel: true,
    label: '隐患所属乡镇',
    component: <SearchInput placeholder="请输入" />,
    extraProps: {
      labelAlign: 'left',
    },
    span: 24,
  },
];

export const columns = [
  { label: '隐患类型', key: 'type', render: (v: any) => v },
  { label: '险情等级', key: 'scaleLevel' },
  { label: '隐患所属乡镇', key: 'townsVillage', render: (v: any) => v },
  { label: '隐患所属村', key: 'village' },
];
