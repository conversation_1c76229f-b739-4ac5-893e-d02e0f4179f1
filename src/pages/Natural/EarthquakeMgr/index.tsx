import React, { useEffect, useState } from 'react';
import { columns, filterItems } from './constants';
import HiddenDangerDrawer from '@/components/HiddenDangerDrawer';
import HiddenDangerAnalysis from '@/components/HiddenDangerAnalysis';
import { addGeologyInfo, geologyAnalysis, geologyDetail, removeGeology } from '@/services/natural';
import { message } from 'antd';
import useOptionsFromDict from '@/hook/useOptionsFromDict';
import { createCodeLabelMap, getCurrentFormattedTime } from '@/utils/commonFunction';
import AddForm from './components/AddForm';
import FixedWidthSelect from '@/components/BaseFormComponents/FixedWidthSelect';

const EarthquakeMgr: React.FC = () => {
  const regionOptions = useOptionsFromDict('warn_owning_region');
  const geologyOptios = useOptionsFromDict('warn_geology_type');
  const [formFilters, setFormFilters] = useState<any[]>([...filterItems]);
  const [analysisData, setAnalysisData] = useState<any>({});
  const [tableColumns, setTableColumns] = useState<any[]>([...columns]);

  useEffect(() => {
    if (regionOptions.length > 0) {
      const updatedFormFilters = [...formFilters];
      updatedFormFilters[2].component = (
        <FixedWidthSelect options={regionOptions} placeholder="请选择隐患所属乡镇" />
      );
      setFormFilters(formFilters);

      const newColumns = [...tableColumns];
      const typeMap = createCodeLabelMap(regionOptions);
      newColumns[2].render = (v: any) => typeMap[v];
      setTableColumns(newColumns);
    }
  }, [regionOptions]);

  useEffect(() => {
    formFilters[1].component = (
      <FixedWidthSelect options={geologyOptios} placeholder="请选择隐患类型" />
    );

    const newColumns = [...tableColumns];
    const typeMap = createCodeLabelMap(geologyOptios);
    newColumns[0].render = (v: any) => typeMap[v];
    setTableColumns(newColumns);
  }, [geologyOptios]);

  const queryAnalysisDetail = async () => {
    const res = await geologyAnalysis();
    if (res.code === '200') {
      setAnalysisData(res.data || {});
    } else message.error(res.msg);
  };

  useEffect(() => {
    queryAnalysisDetail();
  }, []);

  const getEditInitValues = async (id: string) => {
    const res = await geologyDetail(id);
    if (res.code === '200') {
      return res.data;
    }
    message.error(res.msg);
    return false;
  };

  const onDelete = async (item: any) => {
    const { id } = item;
    const res = await removeGeology(id);
    if (res.code === '200') return true;
    message.error(res.msg);
    return false;
  };

  const onAdd = async (values: any) => {
    const res = await addGeologyInfo(values);
    if (res.code === '200') {
      message.success('保存成功！');
      return true;
    }
    message.error(res.msg);
    return false;
  };

  return (
    <div className="common-page">
      <HiddenDangerDrawer
        onDelete={onDelete}
        onEdit={onAdd}
        onAdd={onAdd}
        refreshHandle={queryAnalysisDetail}
        exportUrl={`${ALARMPREFIX}/warning/naturalDisaster/geology/exportGeolody`}
        importUrl={`${ALARMPREFIX}/warning/naturalDisaster/geology/importGeolody`}
        columns={tableColumns}
        filterItems={formFilters}
        templateUrl="地质灾害隐患信息导入.xlsx&objectName=20250512/d36eb503-81e9-498c-bfca-ab6a905a1e48.xlsx"
        title="地震灾害隐患管理"
        formItems={<AddForm geologyTypeOptiosn={geologyOptios} regionOptions={regionOptions} />}
        exportFileTitle={`地震隐患管理统计数据 ${getCurrentFormattedTime()}`}
        getEditInitValues={getEditInitValues}
        listUrl={`${ALARMPREFIX}/warning/naturalDisaster/geology/geologyList`}
      />
      <HiddenDangerAnalysis title="数据统计" analysisData={analysisData} />
    </div>
  );
};

export default EarthquakeMgr;
