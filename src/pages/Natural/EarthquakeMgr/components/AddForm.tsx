import React from 'react';
import { Col, Form, Input, Select } from 'antd';
import { commonPlaceHolder, requiredRule } from '@/utils/commonFunction';
import {
  codeLengthRules,
  codeRules,
  nameLengthRules,
  phoneLengthRules,
  phoneRules,
} from '@/utils/form';

interface FormSectionProps {
  geologyTypeOptiosn: any[];
  regionOptions: any[];
}
const AddForm: React.FC<FormSectionProps> = ({ geologyTypeOptiosn, regionOptions }) => {
  return (
    <>
      <Col span={24}>
        <Form.Item
          label="隐患名称"
          name="name"
          rules={[requiredRule('请输入隐患名称'), nameLengthRules]}
        >
          <Input placeholder={commonPlaceHolder('隐患名称')} />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item
          label="隐患野外编号"
          name="wildNumber"
          rules={[requiredRule('请输入隐患野外编号'), codeLengthRules, codeRules]}
        >
          <Input placeholder={commonPlaceHolder('隐患野外编号')} />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item label="隐患类型" name="type" rules={[requiredRule('请选择隐患类型')]}>
          <Select options={geologyTypeOptiosn} placeholder="请选择隐患类型" />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item label="险情等级" name="situationLevel" rules={[requiredRule('请选择险情等级')]}>
          <Select
            options={[
              { label: '小型', value: '小型' },
              { label: '中型', value: '中型' },
              { label: '大型', value: '大型' },
            ]}
            placeholder="请选择险情等级"
          />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item label="规模等级" name="scaleLevel" rules={[requiredRule('请选择规模等级')]}>
          <Select
            options={[
              { label: '小型', value: '小型' },
              { label: '中型', value: '中型' },
              { label: '大型', value: '大型' },
            ]}
            placeholder="请选择规模等级"
          />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item
          label="隐患所属乡镇"
          name="townsVillage"
          rules={[requiredRule('请选择隐患所属乡镇')]}
        >
          <Select options={regionOptions} placeholder="请选择隐患所属乡镇" />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item label="隐患所属村" name="village" rules={[requiredRule('请输入隐患所属村')]}>
          <Input placeholder={commonPlaceHolder('隐患所属村')} />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item label="经度" name="longitude" rules={[requiredRule('请输入经度')]}>
          <Input placeholder={commonPlaceHolder('经度')} />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item label="纬度" name="latitude" rules={[requiredRule('请输入纬度')]}>
          <Input placeholder={commonPlaceHolder('纬度')} />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item label="威胁人数" name="threatNumber">
          <Input placeholder={commonPlaceHolder('威胁人数')} />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item label="威胁财产" name="threatProperty">
          <Input placeholder={commonPlaceHolder('威胁财产')} />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item label="县级责任人" name="countyPerson" rules={[nameLengthRules]}>
          <Input placeholder={commonPlaceHolder('县级责任人')} />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item label="县级责任人职务" name="countyJob">
          <Input placeholder={commonPlaceHolder('县级责任人职务')} />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item label="县级责任人电话" name="countyPhone" rules={[phoneLengthRules, phoneRules]}>
          <Input placeholder={commonPlaceHolder('县级责任人电话')} />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item label="乡级责任人" name="villagePerson">
          <Input placeholder={commonPlaceHolder('乡级责任人')} />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item label="乡级责任人职务" name="villageJob">
          <Input placeholder={commonPlaceHolder('乡级责任人职务')} />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item
          label="乡级责任人电话"
          name="villagePhone"
          rules={[phoneLengthRules, phoneRules]}
        >
          <Input placeholder={commonPlaceHolder('乡级责任人电话')} />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item label="村委会责任人" name="villageCommitteePerson">
          <Input placeholder={commonPlaceHolder('村委会责任人')} />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item label="村委会责任人职务" name="villageCommitteeJob">
          <Input placeholder={commonPlaceHolder('村委会责任人职务')} />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item
          label="村委会责任人电话"
          name="villageCommitteePhone"
          rules={[phoneLengthRules, phoneRules]}
        >
          <Input placeholder={commonPlaceHolder('村委会责任人电话')} />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item label="隐患点监测人" name="monitorJob">
          <Input placeholder={commonPlaceHolder('隐患点监测人')} />
        </Form.Item>
      </Col>
      <Col span={24}>
        <Form.Item
          label="隐患点监测人电话"
          name="monitorPhone"
          rules={[phoneLengthRules, phoneRules]}
        >
          <Input placeholder={commonPlaceHolder('隐患点监测人电话')} />
        </Form.Item>
      </Col>
    </>
  );
};
export default AddForm;
