import { dataServiceGatewayByGet, listModuleInfo, listPissTextInfo } from '@/services/home/<USER>';
import { getColumnList } from '@/services/systemMaintenance/module';
import { getUserTemplateList } from '@/services/systemMaintenance/portalLayout';
import { RightOutlined } from '@ant-design/icons';
import { history } from '@umijs/max';
import React, { useEffect, useState } from 'react';
import { useLocation } from 'umi';
import weizhi from '../../assets/images/home/<USER>';
import ArticleList from './components/articleList';
import CardList from './components/cardList';
import PictureList from './components/pictureList';
import './index.less';

const ColumnLayout: React.FC<any> = () => {
  const location = useLocation();
  const query = new URLSearchParams(location.search);
  const id = query.get('id');
  const [activeMenu, setActiveMenu] = useState<any>(`${id || ''}`);
  const [layout, setLayout] = useState<any[]>([]);
  const [moduleList, setModuleList] = useState<any[]>([]);
  const [columnIdMap, setColumnIdMap] = useState<any>({});
  const [styleInfo, setStyleInfo] = useState<any>({});

  const getLayoutList = async () => {
    const userRes = await getUserTemplateList();

    // 用户模板
    if (userRes.code === '200') {
      const item = userRes.data || [];
      const userLayout = JSON.parse(item?.[0]?.layoutJson || '[]');
      setLayout(userLayout);
    }
  };
  const getStyleInfos = async (styleIds: any[], list: any[]) => {
    const res = await listModuleInfo({ pageIndex: 1, pageSize: 9999, styleIds });
    if (res.code === '200') {
      const temp: any = {};
      const tempColumnMap: any = {};

      res.data?.forEach((item: any) => {
        if (item.styleId) {
          temp[item.styleId] = item.styleCode;
        }
      });

      const layoutModules = layout.map(item => item.i);
      const obj: any = {};
      list.forEach((item: any) => {
        item.checked = false;
        obj[item.columnId] = item;
        tempColumnMap[item.columnId] = temp[item.styleId];
        if (layoutModules.includes(`${item.columnId || ''}`)) {
          item.checked = true;
        }
      });
      setModuleList(list?.filter(item => item.columnShow === 'Y' && item?.checked));
      setColumnIdMap(obj);
      setStyleInfo(tempColumnMap);
    }
  };

  const getModuleList = async () => {
    const res = await getColumnList({ pageSize: 9999, pageIndex: 1, orgId: userInfo?.orgId });
    if (res.code === '200') {
      const list = res.data?.data || [];
      const styleIds = list.map((item: any) => item.styleId)?.join(',');
      getStyleInfos(styleIds, list);
    }
  };

  useEffect(() => {
    getLayoutList();
  }, []);

  useEffect(() => {
    getModuleList();
  }, [layout]);

  return (
    <div className="column-layout">
      {/* <div className="column-header">
        <HomeHeader from="column" />
      </div>

      <div className="column-banner"> </div> */}

      <div className="column-wrapper">
        <div className="column-inner">
          <div className="column-breadcrumb">
            <img src={weizhi} alt="" />
            <span
              className="breadcrumb-text"
              style={{ cursor: 'pointer' }}
              onClick={() => history.push('/home-index')}
            >
              首页
            </span>
            <RightOutlined />
            <span className="breadcrumb-text">{columnIdMap[activeMenu]?.columnName}</span>
          </div>

          <div className="column-main">
            <div className="column-menu">
              {moduleList.map(item => {
                return (
                  <div
                    className={`column-menu-item ${
                      Number(item.columnId) === Number(activeMenu) ? 'active' : ''
                    }`}
                    key={item.columnId}
                    onClick={() => {
                      setActiveMenu(item.columnId);
                    }}
                  >
                    <span>{item.columnName}</span>
                  </div>
                );
              })}
            </div>
            <div className="column-container">
              {columnIdMap[activeMenu]?.styleId === 3 && (
                <CardList
                  params={{ columnId: activeMenu, isPublished: 1 }}
                  service={listPissTextInfo}
                />
              )}
              {columnIdMap[activeMenu]?.styleId === 6 && (
                <PictureList
                  params={{ columnId: activeMenu, isPublished: 1 }}
                  service={listPissTextInfo}
                />
              )}
              {(styleInfo[columnIdMap[activeMenu]?.columnId] === 'articlesList' ||
                styleInfo[columnIdMap[activeMenu]?.columnId] === 'pictureAndArticles') && (
                <ArticleList
                  params={{
                    columnId: activeMenu,
                    interfaceCode: columnIdMap[activeMenu]?.moduleDs,
                  }}
                  service={dataServiceGatewayByGet}
                />
              )}
            </div>
          </div>
        </div>
      </div>

      {/* <HomeFooter /> */}
    </div>
  );
};

export default ColumnLayout;
