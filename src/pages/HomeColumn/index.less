.column-layout {
  position: relative;

  .column-header {
    width: 100%;
    height: 180px;
    padding-right: 8.3%;
    padding-left: 8.3%;
    background: url('../../assets/images/home/<USER>') no-repeat center;
    background-size: 100% 100%;
  }

  .column-banner {
    height: 250px;
    background: url('../../assets/images/home/<USER>') no-repeat center;
    background-size: 100% 100%;
  }

  .column-wrapper {
    background: #f0f2f5;

    .column-inner {
      width: 1340px;
      margin: 0 auto;
      padding-bottom: 40px;

      .column-breadcrumb {
        display: flex;
        align-items: center;
        height: 68px;
        color: #666666;
        font-weight: 400;
        font-size: 20px;
        font-family: PingFangSC-Regular;

        img {
          margin-right: 5px;
        }

        .breadcrumb-text {
          padding: 0 5px;

          &:not(:last-child) {
            cursor: pointer;

            &:hover {
              color: #407add;
            }
          }
        }
      }

      .column-main {
        display: flex;
        justify-content: space-between;
        width: 100%;

        .column-menu {
          width: 320px;
          overflow: hidden;
          background: #e1efff;
          border-radius: 4px 0px 0px 4px;

          .column-menu-item {
            display: flex;
            align-items: center;
            height: 90px;
            padding-left: 28px;
            color: #666666;
            font-weight: 500;
            font-size: 20px;
            font-family: Alibaba-PuHuiTi-M;
            border-bottom: 1px solid rgba(46, 126, 251, 0.1);
            cursor: pointer;

            &.active {
              height: 140px;
              background: url('../../assets/images/home/<USER>') no-repeat;

              span {
                position: relative;
                display: inline-block;
                color: #ffffff;
                font-weight: 500;
                font-size: 28px;
                font-family: Alibaba-PuHuiTi-M;

                &::after {
                  position: absolute;
                  bottom: -10px;
                  left: 0;
                  display: inline-block;
                  width: 100%;
                  height: 3px;
                  background: #fff;
                  content: ' ';
                }
              }
            }

            &:last-child {
              border-bottom: none;
            }
          }
        }

        .column-container {
          width: 1020px;
          padding: 28px;
          background: #fff;
        }
      }
    }
  }
}
