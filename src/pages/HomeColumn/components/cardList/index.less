.homeColumn-cardList {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  background: #fff;

  .list-box {
    display: flex;
    flex-wrap: wrap;
    margin: -16px;
    padding-bottom: 20px;

    .list-item {
      width: 300px;
      height: 388px;
      margin: 16px;
      padding: 24px;
      border: 1px solid rgba(227, 236, 230, 1);
      cursor: pointer;

      .item-time {
        color: #666666;
        font-weight: 400;
        font-size: 14px;
        font-family: PingFangSC-Regular;
      }

      .item-title {
        height: 56px;
        margin-top: 10px;
        color: #333333;
        font-weight: 500;
        font-size: 20px;
        font-family: PingFangSC-Medium;
        line-height: 28px;
      }

      .item-img {
        height: 160px;
        margin-top: 10px;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .item-desc {
        height: 40px;
        margin-top: 10px;
        color: #666666;
        font-weight: 400;
        font-size: 14px;
        font-family: PingFangSC-Regular;
        line-height: 20px;
      }

      .item-more {
        display: flex;
        align-items: center;
        margin-top: 20px;
        color: #ffbf00;
        font-weight: 400;
        font-size: 14px;
        font-family: Alibaba-PuHuiTi-R;

        .anticon {
          margin-left: 5px;
        }
      }

      &:hover {
        background: #086ddf;
        box-shadow: 0px 0px 4px 2px rgba(25, 126, 239, 0.5);

        .item-time,
        .item-title,
        .item-desc,
        .item-more {
          color: #fff;
        }
      }
    }
  }

  .pagination-box {
    display: flex;
    justify-content: center;

    .ant-pagination .ant-pagination-item {
      border-radius: 50%;
    }

    .ant-pagination .ant-pagination-prev .ant-pagination-item-link,
    .ant-pagination .ant-pagination-next .ant-pagination-item-link {
      border-radius: 50%;
    }

    .ant-pagination .ant-pagination-item-active {
      background-color: #197eef;
      border-color: #197eef;
      border-radius: 50%;
    }

    .ant-pagination .ant-pagination-item-active a {
      color: #fff;
    }
  }

  .ellipsis {
    /* 隐藏超出容器的文本 */
    overflow: hidden;
    /* 确保文本不会换行 */
    white-space: nowrap;
    /* 当文本溢出时，显示省略号 */
    text-overflow: ellipsis;
  }

  .ellipsis-multiline {
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2; /* 设置为想要的行数 */
  }
}
