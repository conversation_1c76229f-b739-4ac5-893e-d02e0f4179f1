import { DoubleRightOutlined } from '@ant-design/icons';
import { useDeepCompareEffect } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { ConfigProvider, Empty, Pagination } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import React, { useState } from 'react';
import morentupian from '../../../../assets/images/morentupian.png';
import './index.less';

const CardList: React.FC<any> = (props) => {
  const { params, service } = props;
  const [searchParams, setSearchParams] = useState<{
    pageNo: number;
    pageSize: number;
  }>({ pageNo: 1, pageSize: 9 });
  const [list, setList] = useState<any[]>([]);
  const [total, setTotal] = useState<any>(0);

  const getList = () => {
    service &&
      service({ ...searchParams, ...params }).then((res: any) => {
        const list = res.data.data || [];
        list.forEach((item: any) => {
          if (item.tgpUrls) {
            const tgpUrls = item.tgpUrls.split(',');
            item.firstTgpUrl = `${PREFIX_FILE}${PREFIX_SERVER}${tgpUrls[0]}`;
          } else {
            item.firstTgpUrl = morentupian;
          }
        });
        setList(list);
        setTotal(res.data.totalRecords);
      });
  };

  useDeepCompareEffect(() => {
    getList();
  }, [params, searchParams]);

  return (
    <div className="homeColumn-cardList">
      <div className="list-box">
        {total > 0 ? (
          list.map((item) => {
            return (
              <div className="list-item" key={item.textId} onClick={() => history.push(`/columnDetail?mid=${params.moduleId}&tid=${item.textId}`)}>
                <div className="item-time">{item.createTime}</div>
                <div className="item-title ellipsis-multiline">{item.textTitle}</div>
                <div className="item-img">
                  <img src={item.firstTgpUrl} alt="" />
                </div>
                <div className="item-desc ellipsis-multiline">{item.textSummary}</div>
                <div className="item-more">
                  <span>更多详情</span>
                  <DoubleRightOutlined />
                </div>
              </div>
            );
          })
        ) : (
          <div style={{ width: '100%', paddingTop: 16 }}>
            <Empty description="暂无内容" />
          </div>
        )}
      </div>
      <div className="pagination-box">
        <ConfigProvider locale={zhCN}>
          <Pagination
            current={searchParams.pageNo}
            pageSize={searchParams.pageSize}
            total={total}
            showQuickJumper
            onChange={(page, pageSize) => {
              setSearchParams({
                pageNo: page,
                pageSize,
              });
              window.scrollTo({
                top: 0,
                behavior: 'smooth', // 为滚动添加动画效果
              });
            }}
          />
        </ConfigProvider>
      </div>
    </div>
  );
};

export default CardList;
