.homeColumn-articleList {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  background: #fff;

  .list-box {
    .list-item {
      margin-bottom: 25px;
      padding-bottom: 25px;
      border-bottom: 1px dashed rgba(227, 236, 230, 1);
      &.readed {
        .item-title,
        .item-desc,
        .item-time {
          color: rgba(128, 128, 128, 0.7) !important;
        }
      }

      .item-title {
        position: relative;
        padding-bottom: 8px;
        color: #333333;
        font-weight: 500;
        font-size: 20px;
        font-family: PingFangSC-Medium;
        cursor: pointer;

        &::before {
          position: absolute;
          bottom: -2px;
          left: 0;
          display: inline-block;
          width: 28px;
          height: 2px;
          background: #197eef;
          content: ' ';
        }
      }

      .item-desc {
        margin-top: 15px;
        color: #666666;
        font-weight: 400;
        font-size: 14px;
        font-family: PingFangSC-Regular;
        line-height: 26px;
      }

      .item-bottom {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 15px;

        .item-time {
          color: #666666;
          font-weight: 400;
          font-size: 14px;
          font-family: PingFangSC-Regular;
        }

        .item-more {
          display: flex;
          align-items: center;
          color: #ffbf00;
          font-weight: 400;
          font-size: 14px;
          font-family: Alibaba-PuHuiTi-R;
          cursor: pointer;

          .anticon {
            margin-left: 5px;
          }
        }
      }
    }
  }

  .pagination-box {
    display: flex;
    justify-content: center;

    .ant-pagination .ant-pagination-item {
      border-radius: 50%;
    }

    .ant-pagination .ant-pagination-prev .ant-pagination-item-link,
    .ant-pagination .ant-pagination-next .ant-pagination-item-link {
      border-radius: 50%;
    }

    .ant-pagination .ant-pagination-item-active {
      background-color: #197eef;
      border-color: #197eef;
      border-radius: 50%;
    }

    .ant-pagination .ant-pagination-item-active a {
      color: #fff;
    }
  }

  .ellipsis {
    /* 隐藏超出容器的文本 */
    overflow: hidden;
    /* 确保文本不会换行 */
    white-space: nowrap;
    /* 当文本溢出时，显示省略号 */
    text-overflow: ellipsis;
  }

  .ellipsis-multiline {
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2; /* 设置为想要的行数 */
  }
}
