import { DoubleRightOutlined } from '@ant-design/icons';
import { useDeepCompareEffect } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { ConfigProvider, Empty, Pagination } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import React, { useState } from 'react';
import './index.less';

const ArticleList: React.FC<any> = (props) => {
  const { params, service } = props;
  const [searchParams, setSearchParams] = useState<{
    pageNo: number;
    pageSize: number;
  }>({ pageNo: 1, pageSize: 10 });
  const [list, setList] = useState<any[]>([]);
  const [total, setTotal] = useState<any>(0);

  const getList = () => {
    service &&
      service({ ...searchParams, ...params, orgId: userInfo?.orgId }).then((res: any) => {
        setList(res.data.data || []);
        setTotal(res.data.totalRecords);
      });
  };

  useDeepCompareEffect(() => {
    getList();
  }, [params, searchParams]);

  return (
    <div className="homeColumn-articleList">
      <div className="list-box">
        {total > 0 ? (
          list.map((item) => {
            return (
              <div
                className={`list-item ${Number(item.readAlready || '0') > 0 && 'readed'}`}
                key={item.textId}
              >
                <div
                  className="item-title"
                  onClick={() =>
                    history.push(`/columnDetail?mid=${params.columnId}&tid=${item.textId}`)
                  }
                >
                  <div className="ellipsis">{item.textTitle}</div>
                </div>
                <div className="item-desc ellipsis-multiline">{item.textSummary}</div>
                <div className="item-bottom">
                  <div className="item-time">{item.createTime}</div>
                  <div
                    className="item-more"
                    onClick={() =>
                      history.push(`/columnDetail?mid=${params.columnId}&tid=${item.textId}`)
                    }
                  >
                    <span>更多详情</span>
                    <DoubleRightOutlined />
                  </div>
                </div>
              </div>
            );
          })
        ) : (
          <Empty description="暂无内容" />
        )}
      </div>
      <div className="pagination-box">
        <ConfigProvider locale={zhCN}>
          <Pagination
            current={searchParams.pageNo}
            pageSize={searchParams.pageSize}
            total={total}
            showQuickJumper
            onChange={(page, pageSize) => {
              setSearchParams({
                pageNo: page,
                pageSize,
              });
              window.scrollTo({
                top: 0,
                behavior: 'smooth', // 为滚动添加动画效果
              });
            }}
          />
        </ConfigProvider>
      </div>
    </div>
  );
};

export default ArticleList;
