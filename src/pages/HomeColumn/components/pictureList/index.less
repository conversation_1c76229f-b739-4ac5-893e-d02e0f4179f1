.homeColumn-pictureList {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  background: #fff;

  .list-box {
    display: flex;
    flex-wrap: wrap;
    margin: -16px;
    padding-bottom: 20px;

    .list-item {
      position: relative;
      width: 300px;
      height: 300px;
      margin: 16px;
      cursor: pointer;

      &.first {
        // width: 632px;
        height: 300px;
      }

      img {
        width: 100%;
        height: 100%;
      }

      .list-title {
        position: absolute;
        bottom: 12px;
        left: 0;
        display: inline-block;
        width: 100%;
        height: 38px;
        padding: 0 10px;
        color: #ffffff;
        font-weight: 500;
        font-size: 20px;
        font-family: Alibaba-PuHuiTi-M;
        line-height: 38px;
        background-image: linear-gradient(134deg, #fdd251 0%, #ed9c0c 100%);
      }
    }
  }

  .pagination-box {
    display: flex;
    justify-content: center;

    .ant-pagination .ant-pagination-item {
      border-radius: 50%;
    }

    .ant-pagination .ant-pagination-prev .ant-pagination-item-link,
    .ant-pagination .ant-pagination-next .ant-pagination-item-link {
      border-radius: 50%;
    }

    .ant-pagination .ant-pagination-item-active {
      background-color: #197eef;
      border-color: #197eef;
      border-radius: 50%;
    }

    .ant-pagination .ant-pagination-item-active a {
      color: #fff;
    }
  }

  .ellipsis {
    /* 隐藏超出容器的文本 */
    overflow: hidden;
    /* 确保文本不会换行 */
    white-space: nowrap;
    /* 当文本溢出时，显示省略号 */
    text-overflow: ellipsis;
  }

  .ellipsis-multiline {
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2; /* 设置为想要的行数 */
  }
}
