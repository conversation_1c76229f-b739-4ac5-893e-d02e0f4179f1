import { getColumnList } from '@/services/systemMaintenance/module';
import { RightOutlined } from '@ant-design/icons';
import { history } from '@umijs/max';
import { downloadFile } from '@/utils/commonFileFun';
// @ts-ignore
import CryptoJS from 'crypto-js';
// @ts-ignore
import { useLocation } from 'umi';
import '@wangeditor/editor/dist/css/style.css';
import React, { useEffect, useState } from 'react';
import weizhi from '../../assets/images/home/<USER>';
import { Form } from 'antd';
import './index.less';

const ColumnDetails: React.FC<any> = () => {
  const location = useLocation();
  const query = new URLSearchParams(location.search);

  const mid = query.get('mid') || '';
  const type = query.get('type') || '';
  const initValue = query.get('initValue') || '{}';
  const fileUrl = decodeURIComponent(query.get('fileUrl') || '');

  const [form] = Form.useForm();
  const [moduleTypeMap, setModuleTypeMap] = useState<any>({});
  const [title, setTitle] = useState('');
  const [filePreviewUrl, setFilePreviewUrl] = useState('');

  const getModuleList = async () => {
    const res = await getColumnList({ pageSize: 9999, pageIndex: 1 });
    if (res.code === '200') {
      const list = res.data?.data || [];
      const obj: any = {};
      list.forEach((item: any) => {
        obj[item.columnId] = item;
      });
      setModuleTypeMap(obj);
    }
  };

  useEffect(() => {
    if (fileUrl) {
      const urlParams = new URLSearchParams(fileUrl);
      const fileName = urlParams.get('fileName');
      setTitle(fileName as string);
      const trans = CryptoJS.enc.Utf8.parse(downloadFile(fileUrl));
      setFilePreviewUrl(
        // @ts-ignore
        `${PRIVIEW_SERVER}?url=${encodeURIComponent(CryptoJS.enc.Base64.stringify(trans))}`,
      );
    }
  }, [fileUrl]);

  useEffect(() => {
    if (type === 'soft') {
      const formValues = JSON.parse(initValue);
      form.setFieldsValue(formValues);
    }
  }, [type]);

  useEffect(() => {
    getModuleList();
  }, []);

  console.log('moduleTypeMap', moduleTypeMap);

  return (
    <div className="column-details">
      <div className="column-wrapper">
        <div className="column-inner">
          <div className="column-breadcrumb">
            <img src={weizhi} alt="" />
            <span
              className="breadcrumb-text"
              style={{ cursor: 'pointer' }}
              onClick={() => history.push('/home-index')}
            >
              首页
            </span>
            <RightOutlined />
            <span
              className="breadcrumb-text"
              style={{ cursor: 'pointer' }}
              onClick={() => history.push(`/column?id=${mid}`)}
            >
              {moduleTypeMap[mid]?.columnName}
            </span>
            <RightOutlined />
            <span className="breadcrumb-text">详情</span>
          </div>

          <div className="column-main">
            <div className="top-line"> </div>
            <div className="detail-box">
              <div className="detail-title">{title}</div>
              {/* <div className="detail-time">{details.textModifyTime}</div> */}
              <div className="detail-content">
                {type === 'rule' && (
                  <iframe title="preview" src={filePreviewUrl} height="600px" width="100%" />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* <HomeFooter /> */}
    </div>
  );
};

export default ColumnDetails;
