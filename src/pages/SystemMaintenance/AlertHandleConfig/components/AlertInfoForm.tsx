import React, { useEffect, useState } from 'react';
import { FormInstance, Row, message } from 'antd';
import AlertForm from '@/components/AlertForm';
import { getReportDetail } from '@/services/systemMaintenance/alert';
import HandleInfoForm from '@/components/AlertForm/components/HandleInfoForm';
import IgnoreInfoForm from '@/components/AlertForm/components/IgnoreInfoForm';

interface ModalFormProps {
  form?: FormInstance<any>;
  openType?: 'edit' | 'view';
  reportId: string;
}

const AlertInfoForm: React.FC<ModalFormProps> = ({ form, openType, reportId }) => {
  const [alertType, setAlertType] = useState('');
  const [isDisposer, setIsDisposer] = useState(false);
  const [isIgnore, setIsIgnore] = useState(false);

  const queryFormDetail = async () => {
    const res = await getReportDetail(reportId);
    if (res.code === '200') {
      const { data: [data] = [{}] } = res;
      if (data) {
        form?.setFieldsValue(data);
        const { type, disposerName, ignoreReason } = data;
        setAlertType(type);
        setIsIgnore(false);
        disposerName && setIsDisposer(true);
        ignoreReason && setIsIgnore(true);
      }
    } else message.error(res.msg || '获取详情失败！');
  };

  useEffect(() => {
    if (openType === 'view' && reportId !== '') {
      queryFormDetail();
    }
  }, [reportId, openType]);

  return (
    <div
      style={{
        border: '1px solid #ccc',
        marginBottom: '16px',
        padding: '16px',
        position: 'relative',
      }}
    >
      <Row gutter={[16, 0]}>
        <AlertForm type={alertType} form={form} />
        {isDisposer && !isIgnore && <HandleInfoForm />}
        {isDisposer && isIgnore && <IgnoreInfoForm />}
      </Row>
    </div>
  );
};
export default AlertInfoForm;
