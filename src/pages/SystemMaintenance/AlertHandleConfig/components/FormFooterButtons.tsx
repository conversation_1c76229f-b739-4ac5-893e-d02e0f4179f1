import React, { useState } from 'react';
import { Button, message } from 'antd';
import HandleModal from './HandleModal';
import { updateDispose } from '@/services/systemMaintenance/alert';

interface FormFooterButtonsProps {
  onCancel?: () => void;
  openType: 'view' | 'edit';
  reportId: string;
}
const FormFooterButtons: React.FC<FormFooterButtonsProps> = ({ openType, onCancel, reportId }) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [modalType, setModalType] = useState<'handle' | 'ignore' | null>(null);
  const [handleContent, setHandleContent] = useState('');
  const [ignoreReason, setIgnoreReason] = useState('其他');
  const [ignoreContent, setIgnoreContent] = useState('');

  const showModal = (type: 'handle' | 'ignore') => {
    setModalType(type);
    setIsModalVisible(true);
  };

  const setAlertHandled = async () => {
    const res = await updateDispose({
      id: reportId,
      disposerContent: handleContent,
      warningState: '1',
    });
    if (res.code === '200') return true;
    message.error(res.msg || '操作失败，请稍后重试！');
    return false;
  };

  const setAlertIgnored = async () => {
    const res = await updateDispose({
      id: reportId,
      warningState: '2',
      ignoreReason,
      ignoreReasonDescribe: ignoreContent,
    });
    if (res.code === '200') return true;
    message.error(res.msg || '操作失败，请稍后重试！');
    return false;
  };

  const handleOk = async () => {
    let updateRes = false;
    if (modalType === 'handle') {
      updateRes = await setAlertHandled();
    } else if (modalType === 'ignore') {
      updateRes = await setAlertIgnored();
    }
    if (!updateRes) return;
    message.success('操作成功！');
    setIsModalVisible(false);
    onCancel?.();
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  if (openType === 'view') {
    return null;
  }

  return (
    <div>
      <Button type="primary" onClick={() => showModal('handle')}>
        处置
      </Button>
      <Button danger type="primary" onClick={() => showModal('ignore')}>
        忽略
      </Button>
      <Button onClick={onCancel}>取消</Button>
      <HandleModal
        visible={isModalVisible}
        type={modalType!}
        onOk={handleOk}
        onCancel={handleCancel}
        handleContent={handleContent}
        setHandleContent={setHandleContent}
        ignoreReason={ignoreReason}
        setIgnoreReason={setIgnoreReason}
        ignoreContent={ignoreContent}
        setIgnoreContent={setIgnoreContent}
      />
    </div>
  );
};
export default FormFooterButtons;
