import { CheckCircleOutlined, CloseCircleOutlined, UploadOutlined } from '@ant-design/icons';
import { Button, Form, Input, Modal, Radio, Upload } from 'antd';
import React, { useState } from 'react';

interface AlertModalProps {
  visible: boolean;
  type: 'handle' | 'ignore';
  onOk: () => void;
  onCancel: () => void;
  handleContent: string;
  setHandleContent: (value: string) => void;
  ignoreReason: string;
  setIgnoreReason: (value: string) => void;
  ignoreContent: string;
  setIgnoreContent: (value: string) => void;
}
const HandleModal: React.FC<AlertModalProps> = ({
  visible,
  type,
  onOk,
  onCancel,
  handleContent,
  setHandleContent,
  ignoreReason,
  setIgnoreReason,
  ignoreContent,
  setIgnoreContent,
}) => {
  const [handleError, setHandleError] = useState(false);
  const handleSubmit = () => {
    if (type === 'handle' && !handleContent.trim()) {
      setHandleError(true);
      return;
    }
    setHandleError(false);
    onOk();
  };
  return (
    <Modal
      title={
        <span>
          {type === 'handle' ? (
            <CheckCircleOutlined style={{ color: '#81D157' }} />
          ) : (
            <CloseCircleOutlined style={{ color: 'red' }} />
          )}{' '}
          提示
        </span>
      }
      open={visible}
      onOk={handleSubmit}
      onCancel={onCancel}
      okText="提交"
      cancelText="取消"
    >
      <Form layout="vertical">
        {type === 'handle' ? (
          <>
            <p>确认处置该预警信息？</p>
            <Form.Item
              label="处置内容"
              name="handleContent"
              rules={[{ required: true, message: '处置内容不能为空' }]}
            >
              <Input.TextArea
                placeholder="请输入处置内容"
                value={handleContent}
                onChange={e => {
                  setHandleContent(e.target.value);
                  if (handleError) setHandleError(false);
                }}
                style={handleError ? { borderColor: 'red' } : {}}
              />
            </Form.Item>
            <Form.Item
              name="attachments"
              label="附件上传"
              valuePropName="fileList"
              getValueFromEvent={e => e.fileList}
            >
              <Upload name="file" action="/api/upload" listType="picture">
                <Button icon={<UploadOutlined />}>点击上传</Button>
              </Upload>
            </Form.Item>
          </>
        ) : (
          <>
            <p>确认忽略该预警信息？</p>
            <Form.Item label="忽略原因" name="ignoreReason">
              <Radio.Group onChange={e => setIgnoreReason(e.target.value)} value={ignoreReason}>
                <Radio value="无需处理">无需处理</Radio>
                <Radio value="误报">误报</Radio>
                <Radio value="其他">其他</Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item label="原因说明" name="ignoreContent">
              <Input.TextArea
                placeholder="请输入原因"
                value={ignoreContent}
                onChange={e => setIgnoreContent(e.target.value)}
              />
            </Form.Item>
          </>
        )}
      </Form>
    </Modal>
  );
};
export default HandleModal;
