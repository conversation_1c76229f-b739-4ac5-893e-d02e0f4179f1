import React from 'react';
import SearchInput from '@/components/BaseFormComponents/SearchInput';

export const HomeTitle = '预警处置';

export const EditPageTitle = '预警处置';

export const filterItems = [
  {
    name: 'name',
    label: '预警名称',
    component: <SearchInput placeholder="请输入" />,
  },
  {
    name: 'type',
    label: '预警类型',
    component: <SearchInput placeholder="请输入" />,
  },
  { name: 'level', label: '预警级别', component: <SearchInput placeholder="请输入" /> },
];

export const tableColumns: any[] = [
  { title: '序号', dataIndex: 'rowId', width: 80 },
  { title: '预警名称', dataIndex: 'name', ellipsis: true },
  { title: '预警类型', dataIndex: 'type', width: 150, ellipsis: true },
  { title: '预警来源', dataIndex: 'source', width: 150 },
  { title: '预警等级', dataIndex: 'level', width: 150 },
  { title: '起始时间', dataIndex: 'createTime', width: 150 },
  { title: '预警状态', dataIndex: 'warningState', width: 150 },
];

export const headerTabs = [
  { key: 'all', label: '全部' },
  { key: 'untreated', label: '未处置' },
  { key: 'treated', label: '已处置' },
  { key: 'ignore', label: '已忽略' },
] as const;

export type HeaderTabKey = (typeof headerTabs)[number]['key'];
