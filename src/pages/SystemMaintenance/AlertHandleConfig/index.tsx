import React, { useState, useEffect } from 'react';
import PageHeader from '@/components/PageHeader';
import ListRequest from '@/components/ListRequest';
import { filterItems, HomeTitle, tableColumns, headerTabs, HeaderTabKey } from './constants';
import useDict from '@/utils/useDict';
import ModalForm from '@/components/ModalForm';
import { createCodeLabelMap, getOptionsFromDict } from '@/utils/commonFunction';
import OnlyTabs from '@/components/OnlyTabs';
import styles from './index.less';
import AlertInfoForm from './components/AlertInfoForm';
import FormFooterButtons from './components/FormFooterButtons';
import { useAlertConfig } from '@/hook/useAlertConfig';
import CommonLinkButton from '@/components/BaseFormComponents/CommonLinkButton';

const AlertHandleConfig: React.FC = () => {
  const { value: warningState } = useDict('warning_state');

  const [formTitle, setFormTitle] = useState(HomeTitle);
  const [formVisible, setFormVisible] = useState(false);
  const [columns, setColumns] = useState([...tableColumns]);
  const [formFilters, setFormFilters] = useState([...filterItems]);
  const [openType, setOpenType] = useState<'view' | 'edit'>('view');
  const [msgTypes, setMsgTypes] = useState<any[]>([...headerTabs]);
  const [activeHeaderTab, setActiveHeaderTab] = useState<HeaderTabKey>('all');
  const [countData, setCountData] = useState<Record<string, any>>({});
  const [extraParams, setExtraParams] = useState<Record<string, any>>({});
  const [updateTrigger, setUpdateTrigger] = useState(false);
  const [reportId, setReportId] = useState('');

  const { setAlertTypeOptions, setAlertLevelOptions } = useAlertConfig(
    formFilters,
    columns,
    setColumns,
    { typeIndex: 1, levelIndex: 2 },
  );

  const editAlterInfo = (record: any) => {
    setReportId(record.id);
    setOpenType('edit');
    setFormVisible(true);
  };

  const viewAlertInfo = (record: any) => {
    setReportId(record.id);
    setOpenType('view');
    setFormVisible(true);
  };

  const setOperateColumns = () => {
    const actionColumn = {
      title: '操作',
      width: 200,
      noTooltip: true,
      render: (_text: any, record: any) => (
        <div>
          <CommonLinkButton onClick={() => viewAlertInfo(record)}>详情</CommonLinkButton>
          {!['1', '2'].includes(record.warningState) && (
            <CommonLinkButton onClick={() => editAlterInfo(record)}>处置</CommonLinkButton>
          )}
        </div>
      ),
    };
    setColumns([...tableColumns, actionColumn]);
  };

  const setStatesColumn = () => {
    const stateOptions = getOptionsFromDict(warningState);
    const stateMap = createCodeLabelMap(stateOptions);
    columns[6].render = (code: string) => {
      return <span>{stateMap[code]}</span>;
    };
    columns[6].renderTooltip = (code: string) => stateMap[code];
    setColumns([...columns]);
  };

  const setTabsLabelCount = (countData: any) => {
    const stateOptions = getOptionsFromDict(warningState);
    const { allCount, ...others } = countData;
    const msgTypes = [
      { key: 'all', label: '全部' },
      ...stateOptions.map(item => ({ key: item.value, label: item.label })),
    ];
    const updatedMsgTypes = msgTypes.map(tab => {
      const newTab = { ...tab } as any;
      switch (tab.key) {
        case 'all':
          newTab.label = `全部 ${allCount || ''}`;
          break;
        default: {
          const stateOption = stateOptions.find(option => option.value === tab.key);
          if (stateOption) {
            // 从 countData 中获取对应的计数值
            const count = others[tab.key];
            newTab.label = `${stateOption.label} ${count || ''}`;
          }
          break;
        }
      }
      return newTab;
    });
    setMsgTypes(updatedMsgTypes);
  };

  const serviceResHandle = (res: any) => {
    const { disposeList = {}, disposeCount = [{}] } = res;
    const [countData] = disposeCount;
    setCountData(countData);
    return {
      data: [...disposeList.data],
      totalRecords: disposeList.totalRecords,
    };
  };

  const turnToListPage = () => {
    setFormVisible(false);
    setFormTitle(HomeTitle);
  };

  const onContactTypeChange = (key: HeaderTabKey) => {
    setActiveHeaderTab(key);
    if (key !== 'all') {
      setExtraParams({ warningState: key });
    } else setExtraParams({});
    setUpdateTrigger(!updateTrigger);
  };

  useEffect(() => {
    setOperateColumns();
    setStatesColumn();
    if (warningState.length > 0) setTabsLabelCount(countData);
  }, [warningState]);

  useEffect(() => {
    setFormFilters(formFilters);
    setOperateColumns();
    setAlertTypeOptions();
    setAlertLevelOptions();
  }, [formFilters]);

  useEffect(() => {
    setTabsLabelCount(countData);
  }, [countData]);

  return (
    <div className="common-page">
      {!formVisible ? (
        <div className={`common-page-header ${styles.tabsHeader}`}>
          <OnlyTabs
            tabs={msgTypes as any}
            activeKey={activeHeaderTab}
            onTabClick={key => onContactTypeChange(key as HeaderTabKey)}
          />
        </div>
      ) : (
        <PageHeader showBackIcon={formVisible} title={formTitle} onClick={turnToListPage} />
      )}
      {!formVisible && (
        <div className="common-page-body">
          <ListRequest
            apiUrl={`${ALARMPREFIX}/warning/safeproduction/dispose/disposeList`}
            pageSize={10}
            filterItems={formFilters}
            columns={columns}
            inlineButtons
            updateTrigger={updateTrigger}
            serviceResHandle={serviceResHandle}
            extraParams={extraParams}
            method="GET"
            buttonCol={1}
            searchButtonStyle={{ marginLeft: 8 }}
            labelCol={{ span: 8 }}
            scrollYDelta={48}
          />
        </div>
      )}
      <ModalForm
        openType="view"
        visible={formVisible}
        formComp={<AlertInfoForm reportId={reportId} openType="view" />}
        onClose={turnToListPage}
        showDefaultButtons={false}
        customButtons={
          <FormFooterButtons reportId={reportId} openType={openType} onCancel={turnToListPage} />
        }
      />
    </div>
  );
};

export default AlertHandleConfig;
