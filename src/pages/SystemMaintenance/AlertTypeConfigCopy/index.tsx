import CommonLinkButton from '@/components/BaseFormComponents/CommonLinkButton';
import { CustomButton, updateFormFilterSelect } from '@/components/FilterForm';
import ListRequest from '@/components/ListRequest';
import ModalForm from '@/components/ModalForm';
import PageHeader from '@/components/PageHeader';
import { deleteAlertTypeInfo, saveAlertTypeInfo } from '@/services/systemMaintenance/alert';
import { getRecordInitValue } from '@/utils/commonFunction';
import useDict from '@/utils/useDict';
import { message, Popconfirm } from 'antd';
import React, { useEffect, useState } from 'react';
import AlertTypeForm, { formItems } from './components/AlertTypeForm';
import { EditPageTitle, filterItems, HomeTitle, tableColumns } from './constants';

const customButtons: CustomButton[] = [
  { text: '新增', color: '#ffffff', bgColor: '#3164F6', onClick: () => {} },
];

const AlertTypeConfig: React.FC = () => {
  const { value: alertType } = useDict('warn_broad');
  const { value: aiType } = useDict('warn_aitype');

  const [formTitle, setFormTitle] = useState(HomeTitle);
  const [formVisible, setFormVisible] = useState(false);
  const [columns, setColumns] = useState([...tableColumns]);
  const [formFilters, setFormFilters] = useState([...filterItems]);
  const [openType, setOpenType] = useState<'add' | 'edit'>('add');
  const [initValue, setInitValue] = useState<Record<string, any>>();
  const [typeId, setTypeId] = useState('');
  const [updateTrigger, setUpdateTrigger] = useState(false);

  customButtons[0].onClick = () => {
    setOpenType('add');
    setFormVisible(true);
    setFormTitle(EditPageTitle);
  };

  const editAlterType = (record: any) => {
    setTypeId(record.id);
    const initValue = getRecordInitValue(record, formItems);
    setInitValue(initValue);
    setOpenType('edit');
    setFormVisible(true);
    setFormTitle(EditPageTitle);
  };

  const delAlterType = async (record: any) => {
    const res = await deleteAlertTypeInfo(record.id);
    if (res.code === '200') {
      message.success('删除成功！');
      setUpdateTrigger(!updateTrigger);
    } else {
      message.error(res.msg);
    }
  };

  const saveAlertType = async (formData: any) => {
    const extraInfo: Record<string, string> = {};
    if (openType === 'edit') extraInfo.id = typeId;
    const res = await saveAlertTypeInfo(Object.assign(formData, extraInfo));
    if (res.code === '200') {
      message.success('保存成功！');
      setUpdateTrigger(!updateTrigger);
      return true;
    }
    message.error(res.msg);
    return false;
  };

  const setOperateColumns = () => {
    const actionColumn = {
      title: '操作',
      width: 200,
      noTooltip: true,
      render: (_text: any, record: any) => (
        <div>
          <CommonLinkButton onClick={() => editAlterType(record)}>修改</CommonLinkButton>
          <Popconfirm
            title=" "
            description="你确定要删除该预警类型吗？"
            onConfirm={() => delAlterType(record)}
            okText="确认"
            cancelText="取消"
          >
            <CommonLinkButton>删除</CommonLinkButton>
          </Popconfirm>
        </div>
      ),
    };
    setColumns([...tableColumns, actionColumn]);
  };

  const turnToListPage = () => {
    setFormVisible(false);
    setFormTitle(HomeTitle);
  };

  useEffect(() => {
    updateFormFilterSelect(2, alertType, formFilters, '请选择预警大类');
    setFormFilters(formFilters);
    setOperateColumns();
  }, [alertType]);

  useEffect(() => {
    setOperateColumns();
  }, [updateTrigger]);

  return (
    <div className="common-page">
      <PageHeader showBackIcon={formVisible} title={formTitle} onClick={turnToListPage} />
      {!formVisible && (
        <div className="common-page-body">
          <ListRequest
            apiUrl={`${ALARMPREFIX}/warning/safeproduction/sort/sortList`}
            pageSize={10}
            filterItems={formFilters}
            columns={columns}
            updateTrigger={updateTrigger}
            customButtons={customButtons}
            inlineButtons
            method="GET"
            buttonCol={1}
            searchButtonStyle={{ marginLeft: 0 }}
            labelCol={{ span: 8 }}
            scrollYDelta={48}
            expandable={{
              // 可以指定子节点的字段名，默认为 'children'
              childrenColumnName: 'children',
              // 可以指定哪些行可以展开
              rowExpandable: record => record.children && record.children.length > 0,
              // 展开事件回调
              onExpand: (expanded, record) => {
                console.log('Row expanded:', expanded, record);
              },
            }}
          />
        </div>
      )}
      <ModalForm
        openType={openType}
        visible={formVisible}
        initialValues={initValue}
        onSubmit={saveAlertType}
        formComp={<AlertTypeForm openType={openType} aiType={aiType} alertType={alertType} />}
        onClose={turnToListPage}
      />
    </div>
  );
};

export default AlertTypeConfig;
