import React, { useEffect, useState } from 'react';
import { Form, Input, Col, Select, FormInstance } from 'antd';
import { getOptionsFromDict } from '@/utils/commonFunction';

interface ModalFormProps {
  alertType: any[];
  aiType: any[];
  form?: FormInstance<any>;
  openType: 'add' | 'edit';
  formInitValue?: Record<string, any>;
}

export const formItems = ['source', 'name', 'broad', 'encoding', 'perceptualType'];

const AlertTypeForm: React.FC<ModalFormProps> = ({
  alertType,
  aiType,
  form,
  openType,
  formInitValue,
}) => {
  const [alertOptions, setAlertOptions] = useState<any[]>([]);
  const [aiOptions, setAiOptions] = useState<any[]>([]);
  const [isAiSelected, setIsAiSelected] = useState<boolean>(false);

  useEffect(() => {
    setAlertOptions(getOptionsFromDict(alertType));
  }, [alertType]);

  useEffect(() => {
    setAiOptions(getOptionsFromDict(aiType));
  }, [aiType]);

  useEffect(() => {
    if (form && formInitValue && openType === 'edit' && JSON.stringify(formInitValue) !== '{}') {
      const sourceValue = formInitValue.source;
      setIsAiSelected(sourceValue === 'AI感知');
    }
  }, [formInitValue]);

  const handleSourceChange = (value: string) => {
    setIsAiSelected(value === 'AI感知');
    if (value !== 'AI感知' && form) {
      form.setFieldsValue({ releaseScope: undefined, name: '' });
    }
  };

  const handleAiTypeChange = (_value: string, option: any) => {
    if (form) {
      form.setFieldsValue({ name: option.label });
    }
  };

  return (
    <>
      <Col span={8}>
        <Form.Item
          label="新增来源"
          labelCol={{ span: 8 }}
          name="source"
          rules={[{ required: true, message: '请选择新增来源' }]}
        >
          <Select onChange={handleSourceChange}>
            <Select.Option value="AI感知">AI感知</Select.Option>
            <Select.Option value="其他">其他</Select.Option>
          </Select>
        </Form.Item>
      </Col>
      {isAiSelected && (
        <>
          <Col span={8}>
            <Form.Item
              label="AI感知预警类型"
              name="perceptualType"
              labelCol={{ span: 8 }}
              rules={[{ required: true, message: '请输入AI感知预警类型' }]}
            >
              <Select options={aiOptions} onChange={handleAiTypeChange} />
            </Form.Item>
          </Col>
        </>
      )}
      <Col span={8}>
        <Form.Item
          label="预警类型名称"
          name="name"
          labelCol={{ span: 8 }}
          rules={[{ required: true, message: '请输入预警类型名称' }]}
        >
          <Input disabled={isAiSelected} />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item labelCol={{ span: 8 }} label="预警类型编码" name="encoding">
          <Input disabled placeholder="自动生成" />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          labelCol={{ span: 8 }}
          label="预警大类"
          name="broad"
          rules={[{ required: true, message: '请选择预警大类' }]}
        >
          <Select options={alertOptions} />
        </Form.Item>
      </Col>
    </>
  );
};
export default AlertTypeForm;
