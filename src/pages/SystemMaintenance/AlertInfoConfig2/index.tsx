import React, { useState, useEffect } from 'react';
import PageHeader from '@/components/PageHeader';
import ListRequest from '@/components/ListRequest';
import { EditPageTitle, filterItems, HomeTitle, tableColumns } from './constants';
import { CustomButton } from '@/components/FilterForm';
import { Button, message } from 'antd';
import ModalForm from '@/components/ModalForm';
import AlertInfoForm from './components/AlertInfoForm';
import { saveReportList } from '@/services/systemMaintenance/alert';
import { useAlertConfig } from '@/hook/useAlertConfig';

const customButtons: CustomButton[] = [
  { text: '新增', color: '#ffffff', bgColor: '#3164F6', onClick: () => {} },
];

const AlertInfoConfig: React.FC = () => {
  const [formTitle, setFormTitle] = useState(HomeTitle);
  const [formVisible, setFormVisible] = useState(false);
  const [columns, setColumns] = useState([...tableColumns]);
  const [formFilters, setFormFilters] = useState([...filterItems]);
  const [openType, setOpenType] = useState<'add' | 'view'>('add');
  const [updateTrigger, setUpdateTrigger] = useState(false);
  const [reportId, setReportId] = useState('');

  const { setAlertTypeOptions, setAlertLevelOptions } = useAlertConfig(
    formFilters,
    columns,
    setColumns,
    { typeIndex: 1, levelIndex: 2 },
  );

  customButtons[0].onClick = () => {
    setOpenType('add');
    setFormVisible(true);
    setFormTitle(EditPageTitle);
  };

  const viewAlertInfo = (record: any) => {
    setReportId(record.id);
    setOpenType('view');
    setFormVisible(true);
    setFormTitle(EditPageTitle);
  };

  const saveAlertInfo = async (formData: any) => {
    const groupedData: { [key: number]: { [key: string]: any } } = {};
    // 遍历 formData 的键值对
    Object.entries(formData).forEach(([key, value]) => {
      // 使用下划线分割键名
      const [fieldName, indexStr] = key.split('_');
      const index = parseInt(indexStr, 10);
      // 如果该索引还没有初始化，则初始化一个空对象
      if (!groupedData[index]) {
        groupedData[index] = {};
      }
      // 将数据存入对应的索引对象中
      groupedData[index][fieldName] = value;
    });
    // 将分组后的对象转换为数组
    const resultArray = Object.values(groupedData);

    const res = await saveReportList({ batchParam: resultArray });
    if (res.code === '200') {
      message.success('保存成功！');
      setUpdateTrigger(!updateTrigger);
      return true;
    }
    message.error(res.msg);
    return false;
  };

  const setOperateColumns = () => {
    const actionColumn = {
      title: '操作',
      width: 120,
      noTooltip: true,
      render: (_text: any, record: any) => (
        <div>
          <Button type="link" onClick={() => viewAlertInfo(record)}>
            详情
          </Button>
        </div>
      ),
    };
    setColumns([...tableColumns, actionColumn]);
  };

  const turnToListPage = () => {
    setFormVisible(false);
    setFormTitle(HomeTitle);
  };

  useEffect(() => {
    setFormFilters(formFilters);
    setOperateColumns();
    setAlertTypeOptions();
    setAlertLevelOptions();
  }, [formFilters]);

  return (
    <div className="common-page">
      <PageHeader showBackIcon={formVisible} title={formTitle} onClick={turnToListPage} />
      {!formVisible && (
        <div className="common-page-body">
          <ListRequest
            apiUrl={`${ALARMPREFIX}/warning/safeproduction/report/reportList`}
            pageSize={10}
            filterItems={formFilters}
            columns={columns}
            customButtons={customButtons}
            inlineButtons
            method="GET"
            buttonCol={1}
            searchButtonStyle={{ marginLeft: -24 }}
            scrollYDelta={48}
          />
        </div>
      )}
      <ModalForm
        openType={openType}
        visible={formVisible}
        onSubmit={saveAlertInfo}
        formComp={<AlertInfoForm reportId={reportId} openType={openType} />}
        onClose={turnToListPage}
      />
    </div>
  );
};

export default AlertInfoConfig;
