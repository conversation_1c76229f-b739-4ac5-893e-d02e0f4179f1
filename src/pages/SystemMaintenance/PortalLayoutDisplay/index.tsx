import ArticlesList from '@/components/PortalComponents/articlesList';
import CommonLink from '@/components/PortalComponents/commonLink';
import DataComp from '@/components/PortalComponents/dataComp';
import DigitalApp from '@/components/PortalComponents/digitalApp';
import GraphicCards from '@/components/PortalComponents/graphicCards';
import PictureAndArticles from '@/components/PortalComponents/pictureAndArticles';
import PictureLinkLeft from '@/components/PortalComponents/pictureLinkLeft';
import PictureLinkRight from '@/components/PortalComponents/pictureLinkRight';
import PictureList from '@/components/PortalComponents/pictureList';
import Schedule from '@/components/PortalComponents/schedule';
import TodoList from '@/components/PortalComponents/todoList';
import { dataServiceGatewayByGet } from '@/services/home';
import { getColumnList, listModuleInfo } from '@/services/systemMaintenance/module';
import {
  getUserTemplateList,
  listLayout,
  saveUserTemplate,
} from '@/services/systemMaintenance/portalLayout';
import { Button, Checkbox, message } from 'antd';
import React, { useEffect, useRef, useState } from 'react';

import GridLayout from 'react-grid-layout';
import './index.less';
import './reactGridLayout.less';

// 新增组件注意在这里导入组件并做好映射
const compMap: any = {
  articlesList: ArticlesList,
  commonLink: CommonLink,
  dataComp: DataComp,
  graphicCards: GraphicCards,
  pictureAndArticles: PictureAndArticles,
  pictureLinkLeft: PictureLinkLeft,
  pictureLinkRight: PictureLinkRight,
  pictureList: PictureList,
  schedule: Schedule,
  todoList: TodoList,
  digitalApp: DigitalApp,
};

const PortalLayout: React.FC<any> = () => {
  const [layout, setLayout] = useState<any[]>([]);
  const [moduleList, setModuleList] = useState<any[]>([]);
  const [styleInfo, setStyleInfo] = useState<any>({});
  const [isEdit, setIsEdit] = useState(false);
  const [curTemplate, setCurTemplate] = useState<any>({});
  const [moduleTypeMap, setModuleTypeMap] = useState<any>({});
  const getStyleInfos = async (styleIds: any[], list: any[]) => {
    const res = await listModuleInfo({ pageIndex: 1, pageSize: 9999, styleIds });
    if (res.code === '200') {
      const temp: any = {};
      const temp2: any = {};

      res.data?.forEach((item: any) => {
        if (item.styleId) {
          temp[item.styleId] = item.styleCode;
        }
      });

      const layoutModules = layout.map(item => item.i);
      const obj: any = {};
      list.forEach((item: any) => {
        item.checked = false;
        obj[item.columnId] = item;
        item.styleId = `${item.styleId}`;
        temp2[item.columnId] = temp[item.styleId];
        if (layoutModules.includes(`${item.columnId}`)) {
          item.checked = true;
        }
      });
      setModuleList(list);
      setStyleInfo(temp2);
      setModuleTypeMap(obj);
    }
  };

  const getModuleList = async () => {
    const res = await getColumnList({ pageSize: 9999, pageIndex: 1, orgId: userInfo?.orgId });
    if (res.code === '200') {
      const list = res.data?.data || [];
      const styleIds = list.map((item: any) => item.styleId)?.join(',');
      getStyleInfos(styleIds, list);
    }
  };

  const getTempLayout = async () => {
    const tempLayoutRes = await listLayout();
    const itemTemp = tempLayoutRes.data || [];
    const tempLayout = JSON.parse(itemTemp?.[0]?.layoutJson || '[]');

    setCurTemplate(itemTemp?.[0] || {});
    setLayout(tempLayout);
  };

  const getUserLayout = async () => {
    const userRes = await getUserTemplateList();

    // 用户模板
    if (userRes.code === '200') {
      const item = userRes.data || [];
      const userLayout = JSON.parse(item?.[0]?.layoutJson || '[]');
      if (userLayout.length > 0) {
        setCurTemplate(item?.[0] || {});

        setLayout(userLayout);
      } else {
        getTempLayout();
      }
    }
  };

  useEffect(() => {
    getUserLayout();
    getModuleList();
    window.onscroll = onScroll;

    window.addEventListener('scroll', onScroll);
  }, []);

  function onScroll() {
    const element = document.querySelector('.cardList'); // 目标元素

    const triggerOffset = 100; // 触发吸顶的滚动距离
    if (element) {
      if (window.scrollY > triggerOffset) {
        element.style.position = 'fixed';
        element.style.top = '20px';
        element.style.left = '165px'; // 根据需求调整位置
        element.style.width = '120px'; // 确保宽度不变
      } else {
        element.style.position = 'absolute';
        element.style.top = '20px';
        element.style.left = '-120px';
      }
    }
  }
  const scrollListener = useRef<any>(null);

  useEffect(() => {
    if (isEdit && !scrollListener.current) {
      window.addEventListener('scroll', onScroll);
      scrollListener.current = true;
    }

    return () => {
      if (scrollListener.current) {
        window.removeEventListener('scroll', onScroll);
        scrollListener.current = false;
      }
    };
  }, [isEdit]);

  useEffect(() => {
    getModuleList();
  }, [layout]);

  const onLayoutChange = (layer: any) => {
    console.log('onLayoutChange==', layer);
    setLayout(layer);
  };

  const geneDom = () => {
    if (Object.keys(moduleTypeMap).length === 0) {
      return null;
    }
    return layout.map(lay => {
      const layoutItem = moduleTypeMap[lay.i];
      let Comp: any = null;
      const currentStyleInfo =
        ['pie', 'line', 'bar'].indexOf(styleInfo[lay.i]) >= 0 ? 'dataComp' : styleInfo[lay.i];
      Comp = compMap?.[currentStyleInfo];

      if (!Comp) {
        return null;
      }
      return (
        <div
          data-grid={{ ...lay, minW: 3, maxW: 12 }}
          key={lay.i}
          className={`${isEdit ? '' : 'react-grid-item-view'}`}
        >
          <div
            style={{ pointerEvents: isEdit ? 'none' : 'auto', height: '100%' }}
            onClick={e => {
              if (isEdit) {
                e.stopPropagation();
              }
            }}
          >
            <Comp
              name={layoutItem.columnName}
              params={{
                // ...moduleTypeMap[lay.i],
                columnId: moduleTypeMap[lay.i].columnId,
                interfaceCode: moduleTypeMap[lay.i].moduleDs,
                styleCode: styleInfo[lay.i],
                moduleId: moduleTypeMap[lay.i].moduleId,
              }}
              service={dataServiceGatewayByGet}
            />
          </div>
        </div>
      );
    });
  };

  const onCheckboxChange = (e: any, item: any) => {
    const { checked } = e.target;
    console.log('onCheckboxChange==', checked, item);
    const _moduleList = JSON.parse(JSON.stringify(moduleList));
    _moduleList.forEach((i: any) => {
      if (i.columnId === item.columnId) {
        i.checked = checked;
      }
    });
    setModuleList(_moduleList);

    const layConfig = JSON.parse(JSON.stringify(layout));
    if (checked) {
      layConfig.push({
        w: 6,
        h: 30,
        x: 0,
        y: 9999, // puts it at the bottom
        i: `${item.columnId}`,
        minW: 3,
        maxW: 12,
        static: false,
      });
    } else {
      const index = layConfig.findIndex((i: any) => Number(i.i) === Number(item.columnId));
      if (index !== -1) {
        layConfig.splice(index, 1);
      }
    }
    setLayout(layConfig);
  };

  const save = () => {
    saveUserTemplate({
      ...curTemplate,
      tempName: `模块_${1}`,
      // layoutJson:
      //   '[{"w":10,"h":52,"x":0,"y":0,"i":"7","minW":6,"maxW":12,"moved":false,"static":false},{"w":6,"h":30,"x":0,"y":52,"i":"8","minW":6,"maxW":12,"moved":false,"static":false}]',
      layoutJson: JSON.stringify(layout),
      userName: userInfo?.username,
      columnId: moduleList
        .filter(item => item.checked)
        ?.map(item => item.columnId)
        ?.join(','),
    }).then(res => {
      if (res.code === '200') {
        message.success('保存成功');
        getUserLayout();
        setIsEdit(false);
      } else {
        message.error(res.msg);
      }
    });
  };

  return (
    <div className="portalLayout-wrapper-view">
      <div className="config-layout" onClick={() => setIsEdit(!isEdit)}>
        配置
      </div>
      {isEdit ? (
        <div className="cardList">
          <div className="columnName">
            栏目信息
            <a
              style={{ fontSize: '12px', color: '#1890ff', marginLeft: '10px' }}
              onClick={() => getTempLayout()}
            >
              恢复默认
            </a>
          </div>
          {moduleList.map(item => {
            return (
              <div>
                <Checkbox
                  key={item.moduleId}
                  checked={item.checked}
                  onChange={e => onCheckboxChange(e, item)}
                >
                  {item.columnName}
                </Checkbox>
              </div>
            );
          })}
          <div className="btns">
            <Button type="primary" onClick={() => save()}>
              保存
            </Button>
          </div>
        </div>
      ) : null}
      <div className="right">
        {/* <img className="topImg" src={homeTop} alt="" /> */}
        <div className="grid-layout" id="grid-layout">
          <GridLayout
            className="layout"
            cols={12}
            rowHeight={1}
            margin={[10, 10]}
            isBounded={false}
            isDraggable={isEdit}
            isResizable={isEdit}
            onLayoutChange={onLayoutChange}
            layout={layout}
            width={1340}
          >
            {geneDom()}
          </GridLayout>
        </div>
      </div>
    </div>
  );
};

export default PortalLayout;
