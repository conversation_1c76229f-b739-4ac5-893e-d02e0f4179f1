.react-grid-layout {
  position: relative;
  transition: height 200ms ease;
}
.react-grid-item {
  transition: all 200ms ease;
  transition-property: left, top;
}
.react-grid-item.cssTransforms {
  transition-property: transform;
}
.react-grid-item.resizing {
  z-index: 1;
  will-change: width, height;
}

.react-grid-item.react-draggable-dragging {
  z-index: 3;
  transition: none;
  will-change: transform;
}

.react-grid-item.react-grid-placeholder {
  z-index: 2;
  background: gray;
  opacity: 0.2;
  transition-duration: 100ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
}

.react-grid-item > .react-resizable-handle {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 20px;
  height: 20px;
  cursor: se-resize;
}

.react-grid-item > .react-resizable-handle::after {
  position: absolute;
  right: 3px;
  bottom: 3px;
  width: 5px;
  height: 5px;
  border-right: 2px solid rgba(0, 0, 0, 0.4);
  border-bottom: 2px solid rgba(0, 0, 0, 0.4);
  content: '';
}

.grid-item-header {
  height: 25px;
  color: #1a2238;
  font-size: 16px;

  .grid-item-title {
    display: inline-block;
    min-width: 80px;
    background: url('../../../assets/images/systemMaintenance/bar.png') left bottom no-repeat;
  }
}

.grid-item-cont {
  flex: auto;
  padding-top: 10px;
  overflow-y: auto;
}

.remove {
  position: absolute;
  top: 1px;
  right: 7px;
  z-index: 999;
  display: none;
  color: #666;
  cursor: pointer;
}
.react-grid-item {
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  overflow-y: auto;
  background-color: transparent;
  border: 3px solid #00b4ff;
  // padding: 10px;
  border-radius: 8px;
}

.react-grid-item-view {
  border: none;
  border-radius: 0;

  .react-resizable-handle {
    display: none;
  }
}

.react-grid-item:hover .remove {
  display: inline-block;
}

.react-resizable-handle {
  visibility: hidden;
}

.react-grid-item:hover .react-resizable-handle {
  visibility: visible;
}
