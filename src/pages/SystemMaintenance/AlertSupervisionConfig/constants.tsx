import SearchInput from '@/components/BaseFormComponents/SearchInput';
import React from 'react';

export const HomeTitle = '预警督办';

export const EditPageTitle = '预警督办详情';

export const filterItems = [
  {
    name: 'name',
    label: '预警名称',
    component: <SearchInput placeholder="请输入预警名称" allowClear />,
  },
  {
    name: 'type',
    label: '预警类型',
    component: <SearchInput placeholder="请输入" allowClear />,
  },
  { name: 'level', label: '预警级别', component: <SearchInput placeholder="请输入" /> },
  {
    name: 'handleState',
    label: '督办状态',
    component: <SearchInput placeholder="请输入" allowClear />,
  },
];

export const tableColumns: any[] = [
  { title: '序号', dataIndex: 'rowId', width: 80 },
  { title: '预警名称', dataIndex: 'name', ellipsis: true },
  { title: '预警类型', dataIndex: 'type', width: 150, ellipsis: true },
  { title: '预警来源', dataIndex: 'source', width: 150 },
  { title: '预警等级', dataIndex: 'level', width: 150 },
  { title: '起始时间', dataIndex: 'createTime', width: 150 },
  { title: '督办状态', dataIndex: 'handleState', width: 150 },
];
