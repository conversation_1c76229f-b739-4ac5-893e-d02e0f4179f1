import React from 'react';
import { Divider, Typography } from 'antd';
import SupervisionDetail from './SupervisionDetail';

interface SupervisionInfoProps {
  handles: any[];
  stateOptions: any[];
}

const SupervisionInfo: React.FC<SupervisionInfoProps> = ({ handles, stateOptions }) => {
  console.log('handles: ', handles);
  return (
    <div style={{ padding: '16px' }}>
      <Typography.Title level={5}>督办信息</Typography.Title>
      <Divider style={{ margin: '12px 0', borderColor: 'gray' }} />
      {handles.map(handle => (
        <SupervisionDetail
          key={handle.id}
          stateOptions={stateOptions}
          date={handle.createTime}
          supervisor={handle.createBy}
          deadline={handle.deadline}
          content={handle.content}
          company={handle.enterprise}
          handleState={handle.handleState}
        />
      ))}
    </div>
  );
};

export default SupervisionInfo;
