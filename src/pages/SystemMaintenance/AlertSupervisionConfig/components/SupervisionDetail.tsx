import React from 'react';
import { Typography, Tag } from 'antd';
import '../index.less';

interface SupervisionDetailProps {
  date: string;
  supervisor: string;
  deadline: string;
  content: string;
  company: string;
  stateOptions: { label: string; value: string }[];
  handleState: string;
}
const SupervisionDetail: React.FC<SupervisionDetailProps> = ({
  date,
  supervisor,
  deadline,
  content,
  company,
  handleState,
  stateOptions,
}) => {
  // 找到匹配的 option，忽略第一个元素
  const matchedOption = stateOptions.slice(1).find(option => option.value === handleState);
  const tagLabel = matchedOption ? matchedOption.label : '';
  // 根据 stateOptions 的长度和 handleState 的位置来决定颜色，忽略第一个元素
  const getTagColor = () => {
    const index = stateOptions.slice(1).findIndex(option => option.value === handleState);
    if (stateOptions.length - 1 <= 3) {
      return ['lightblue', '#D58D16', '#D53416'][index] || 'red';
    }
    return 'red';
  };
  return (
    <div className="supervision-detail">
      <div className="header">
        <div className="circle"></div>
        <Typography.Text>{date}</Typography.Text>
      </div>
      <div className="content">
        <div className="line"></div>
        <div>
          <Tag color={getTagColor()}>{tagLabel}</Tag>
          <div className="item">
            <div className="label">督办人:</div>
            <div>{supervisor}</div>
          </div>
          <div className="item">
            <div className="label">整改期限:</div>
            <div>{deadline}</div>
          </div>
          <div className="item">
            <div className="label">内容:</div>
            <div>{content}</div>
          </div>
          <div className="item">
            <div className="label">被督办企业:</div>
            <div>{company}</div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default SupervisionDetail;
