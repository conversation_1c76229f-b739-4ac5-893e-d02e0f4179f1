import React, { useState } from 'react';
import { Button, message } from 'antd';
import SupervisionModal from './SupervisionModal';
import { updateSupervisionInfo } from '@/services/systemMaintenance/alert';

interface FormFooterButtonsProps {
  onCancel?: () => void;
  openType: 'view' | 'edit';
  reportId: string;
  handleTitle: string;
  handleValue: string;
}
const SupervisionButtons: React.FC<FormFooterButtonsProps> = ({
  openType,
  handleTitle,
  onCancel,
  reportId,
  handleValue,
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [deadline, setDeadline] = useState('');
  const [content, setContent] = useState('');
  const [company, setCompany] = useState('');
  const showModal = () => {
    setIsModalVisible(true);
  };
  const handleOk = async () => {
    const res = await updateSupervisionInfo({
      warningId: reportId,
      deadline,
      content,
      enterprise: company,
      handleState: handleValue,
    });
    if (res.code === '200') {
      message.success('督办成功！');
      setIsModalVisible(false);
      onCancel?.();
    } else {
      message.error(res.msg || '督办失败，请稍后重试！');
    }
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  if (openType === 'view') {
    return null;
  }

  return (
    <div>
      {openType === 'edit' && (
        <>
          <Button type="primary" onClick={() => showModal()}>
            {handleTitle}
          </Button>
          <Button onClick={onCancel}>取消</Button>
          <SupervisionModal
            visible={isModalVisible}
            title={handleTitle}
            onOk={handleOk}
            onCancel={handleCancel}
            deadline={deadline}
            setDeadline={setDeadline}
            content={content}
            setContent={setContent}
            company={company}
            setCompany={setCompany}
          />
        </>
      )}
    </div>
  );
};
export default SupervisionButtons;
