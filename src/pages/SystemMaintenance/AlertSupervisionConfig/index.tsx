import React, { useState, useEffect } from 'react';
import PageHeader from '@/components/PageHeader';
import ListRequest from '@/components/ListRequest';
import { EditPageTitle, HomeTitle, tableColumns, filterItems } from './constants';
import ModalForm from '@/components/ModalForm';
import { createCodeLabelMap, getOptionsFromDict } from '@/utils/commonFunction';
import { useAlertConfig } from '@/hook/useAlertConfig';
import HandleForm from './components/HandleForm';
import SupervisionButtons from './components/SupervisionButtons';
import useDict from '@/utils/useDict';
import CommonLinkButton from '@/components/BaseFormComponents/CommonLinkButton';
import FixedWidthSelect from '@/components/BaseFormComponents/FixedWidthSelect';

const AlertSupervisionConfig: React.FC = () => {
  const { value: handleState } = useDict('warn_handle_state');

  const [formTitle, setFormTitle] = useState(HomeTitle);
  const [formVisible, setFormVisible] = useState(false);
  const [columns, setColumns] = useState([...tableColumns]);
  const [openType, setOpenType] = useState<'view' | 'edit'>('view');
  const [formFilters, setFormFilters] = useState([...filterItems]);
  const [levelId, setLevelId] = useState('');
  const [handleTitle, setHandleTitle] = useState('');
  const [handleValue, setHandleValue] = useState('');

  const { setAlertTypeOptions, setAlertLevelOptions } = useAlertConfig(
    formFilters,
    columns,
    setColumns,
    { typeIndex: 1, levelIndex: 2 },
  );

  const editHandleInfo = (record: any, action: { label: string; value: any }) => {
    setHandleTitle(action.label);
    setHandleValue(action.value);
    setLevelId(record.id);
    setOpenType('edit');
    setFormTitle(EditPageTitle);
    setFormVisible(true);
  };

  const viewHandleInfo = (record: any) => {
    setLevelId(record.id);
    setOpenType('view');
    setFormTitle(EditPageTitle);
    setFormVisible(true);
  };

  const saveSupervisionInfo = () => {
    return true;
  };

  const getActionLabel = (handleState: string, handleOptions: { label: any; value: any }[]) => {
    // 将 handleState 转换为数字以便计算
    const stateValue = parseInt(handleState, 10);
    // 找到当前 handleState 对应的 option 的索引
    const currentIndex = handleOptions.findIndex(
      option => parseInt(option.value, 10) === stateValue,
    );
    // 如果找到了当前索引，并且有下一个选项，则返回下一个选项的 label
    if (currentIndex !== -1 && currentIndex < handleOptions.length - 1) {
      return handleOptions[currentIndex + 1];
    }
    // 如果没有下一个选项，则返回最后一个选项的 label
    return handleOptions[handleOptions.length - 1];
  };

  const setOperateColumns = () => {
    const handleOptions = getOptionsFromDict(handleState);

    const actionColumn = {
      title: '操作',
      width: 200,
      noTooltip: true,
      render: (_text: any, record: any) => {
        const { handleState } = record;
        const action = getActionLabel(handleState || '0', handleOptions);
        return (
          <div>
            <CommonLinkButton type="link" onClick={() => viewHandleInfo(record)}>
              详情
            </CommonLinkButton>
            {action && (
              <CommonLinkButton type="link" onClick={() => editHandleInfo(record, action)}>
                {action.label}
              </CommonLinkButton>
            )}
          </div>
        );
      },
    };
    setColumns([...tableColumns, actionColumn]);
  };

  const updateColumnRender = (options: any[], dataIndex: string) => {
    if (options.length > 0) {
      const newColumns = [...columns];
      const column = newColumns.find(col => col.dataIndex === dataIndex);
      if (column) {
        const map = createCodeLabelMap(options);
        column.render = (code: string) => <span>{map[code] || '-'}</span>;
        setColumns(newColumns);
      }
    }
  };

  const setHandleTypeOptions = (handleOptions: any[]) => {
    formFilters[3].component = (
      <FixedWidthSelect placeholder="请选择督办状态" allowClear options={handleOptions} />
    );
    setFormFilters(formFilters);
  };

  const turnToListPage = () => {
    setFormVisible(false);
    setFormTitle(HomeTitle);
  };

  useEffect(() => {
    if (handleState.length > 0) {
      const handleOptions = getOptionsFromDict(handleState);
      updateColumnRender(handleOptions, 'handleState');
      setHandleTypeOptions(handleOptions);
      setOperateColumns();
    }
  }, [handleState]);

  useEffect(() => {
    setFormFilters(formFilters);
    setOperateColumns();
    setAlertTypeOptions();
    setAlertLevelOptions();
  }, [formFilters]);

  return (
    <div className="common-page">
      <PageHeader showBackIcon={formVisible} title={formTitle} onClick={turnToListPage} />
      {!formVisible && (
        <div className="common-page-body">
          <ListRequest
            apiUrl={`${ALARMPREFIX}/warning/safeproduction/handle/handleList`}
            pageSize={10}
            filterItems={formFilters}
            columns={columns}
            inlineButtons
            method="GET"
            buttonCol={1}
            labelCol={{ span: 8 }}
            searchButtonStyle={{ marginLeft: 8 }}
            scrollYDelta={48}
          />
        </div>
      )}
      <ModalForm
        openType="view"
        visible={formVisible}
        onSubmit={saveSupervisionInfo}
        formComp={<HandleForm handleState={handleState} reportId={levelId} openType="view" />}
        onClose={turnToListPage}
        showDefaultButtons={false}
        customButtons={
          <SupervisionButtons
            handleValue={handleValue}
            handleTitle={handleTitle}
            reportId={levelId}
            openType={openType}
            onCancel={turnToListPage}
          />
        }
      />
    </div>
  );
};

export default AlertSupervisionConfig;
