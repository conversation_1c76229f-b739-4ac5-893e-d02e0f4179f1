.alert-type-form {
  width: 100%;

  .section-title {
    margin: 16px 0;
    color: black;
    font-weight: 600;
    font-size: 16px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 8px;
  }
  .ant-divider {
    margin: 16px 0;
    color: #1890ff;
    font-weight: 600;

    &.ant-divider-with-text-left {
      &::before {
        width: 5%;
      }

      .ant-divider-inner-text {
        padding-left: 0;
        color: #1890ff;
        font-weight: 600;
        font-size: 16px;
      }
    }
  }

  .ant-form-item {
    margin-bottom: 16px;
  }

  .ant-form-item-label {
    > label {
      color: #262626;
      font-weight: 500;
    }
  }

  .attribute-section {
    margin-top: 16px;
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;

    .attribute-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;

      .attribute-title {
        color: #262626;
        font-weight: 600;
        font-size: 16px;
      }
    }

    .ant-table {
      .ant-table-thead > tr > th {
        color: #262626;
        font-weight: 600;
        background: #f0f0f0;
      }

      .ant-table-tbody > tr > td {
        padding: 8px 16px;
      }

      .action-buttons {
        .ant-btn-link {
          height: auto;
          margin-right: 8px;
          padding: 0;

          &:last-child {
            margin-right: 0;
          }
        }
      }
    }
  }

  .attribute-modal {
    .ant-form-item {
      margin-bottom: 20px;
    }

    .ant-form-item-label > label {
      font-weight: 500;
    }

    .ant-input,
    .ant-select {
      border-radius: 4px;
    }

    .ant-radio-group {
      .ant-radio-wrapper {
        margin-right: 16px;
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .ant-col {
      margin-bottom: 16px;
    }

    .attribute-section {
      padding: 12px;

      .ant-table {
        font-size: 12px;

        .ant-table-thead > tr > th,
        .ant-table-tbody > tr > td {
          padding: 6px 8px;
        }
      }
    }
  }

  // 表单验证样式
  .ant-form-item-has-error {
    .ant-input,
    .ant-select-selector {
      border-color: #ff4d4f;
      box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
    }
  }

  // 禁用状态样式
  .ant-input[disabled] {
    color: #00000040;
    background-color: #f5f5f5;
    border-color: #d9d9d9;
    cursor: not-allowed;
  }

  // 多选标签样式
  .ant-select-multiple {
    .ant-select-selection-item {
      color: #1890ff;
      background: #f0f8ff;
      border-color: #1890ff;
    }
  }

  // 单选按钮组样式
  .ant-radio-group {
    .ant-radio-wrapper {
      &:hover {
        .ant-radio-inner {
          border-color: #1890ff;
        }
      }
    }

    .ant-radio-wrapper-checked {
      .ant-radio-inner {
        background-color: #1890ff;
        border-color: #1890ff;
      }
    }
  }

  // 按钮样式
  .ant-btn {
    font-weight: 500;
    border-radius: 4px;

    &.ant-btn-primary {
      background: #1890ff;
      border-color: #1890ff;
      box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);

      &:hover {
        background: #40a9ff;
        border-color: #40a9ff;
      }
    }

    &.ant-btn-link {
      &.ant-btn-dangerous {
        color: #ff4d4f;

        &:hover {
          color: #ff7875;
        }
      }
    }
  }
}
