import CommonLinkButton from '@/components/BaseFormComponents/CommonLinkButton';
import { getOptionsFromDict } from '@/utils/commonFunction';
import { PlusOutlined } from '@ant-design/icons';
import {
  Button,
  Col,
  Form,
  FormInstance,
  Input,
  message,
  Modal,
  Radio,
  Row,
  Select,
  Space,
  Table,
} from 'antd';
import React, { useEffect, useState } from 'react';
import styles from './AlertTypeForm.less';

interface ModalFormProps {
  alertType: any[];
  aiType: any[];
  form?: FormInstance<any>;
  openType: 'add' | 'edit' | 'addSub';
  formInitValue?: Record<string, any>;
}

interface AttributeItem {
  id: string;
  name: string;
  identifier: string;
  dataType: string;
  required: boolean;
  display: boolean;
}

export const formItems = [
  'source',
  'name',
  'broad',
  'encoding',
  'perceptualType',
  'disposalTarget',
  'needDisposal',
  'belongUnit',
  'attributes',
  'parentName',
];

const AlertTypeForm: React.FC<ModalFormProps> = ({
  alertType,
  aiType,
  form,
  openType,
  formInitValue,
}) => {
  const [alertOptions, setAlertOptions] = useState<any[]>([]);
  const [aiOptions, setAiOptions] = useState<any[]>([]);
  const [isAiSelected, setIsAiSelected] = useState<boolean>(false);
  const [attributes, setAttributes] = useState<AttributeItem[]>([]);
  const [attributeModalVisible, setAttributeModalVisible] = useState(false);
  const [editingAttribute, setEditingAttribute] = useState<AttributeItem | null>(null);
  const [attributeForm] = Form.useForm();

  // 处置对象选项
  const disposalTargetOptions = [
    { label: '企业用户', value: '企业用户' },
    { label: '监管部门', value: '监管部门' },
    { label: '应急部门', value: '应急部门' },
  ];

  // 归属单位选项（模拟数据）
  const belongUnitOptions = [
    { label: '安全监管局', value: '安全监管局' },
    { label: '应急管理局', value: '应急管理局' },
    { label: '消防救援队', value: '消防救援队' },
    { label: '环保局', value: '环保局' },
  ];

  // 数据类型选项
  const dataTypeOptions = [
    { label: '字符串', value: '字符串' },
    { label: '整数', value: '整数' },
    { label: '浮点数', value: '浮点数' },
  ];

  useEffect(() => {
    setAlertOptions(getOptionsFromDict(alertType));
  }, [alertType]);

  useEffect(() => {
    setAiOptions(getOptionsFromDict(aiType));
  }, [aiType]);

  // 初始化默认属性
  const initDefaultAttributes = () => {
    const defaultAttributes: AttributeItem[] = [
      {
        id: '1',
        name: '企业名称',
        identifier: 'enterprise_name',
        dataType: '字符串',
        required: true,
        display: true,
      },
      {
        id: '2',
        name: '企业统一社会信用代码',
        identifier: 'unified_social_credit_code',
        dataType: '字符串',
        required: true,
        display: true,
      },
      {
        id: '3',
        name: '设备类型',
        identifier: 'equipment_type',
        dataType: '字符串',
        required: false,
        display: true,
      },
      {
        id: '4',
        name: '监测值',
        identifier: 'monitoring_value',
        dataType: '浮点数',
        required: false,
        display: true,
      },
      {
        id: '5',
        name: '监测值单位',
        identifier: 'monitoring_value_unit',
        dataType: '字符串',
        required: false,
        display: true,
      },
    ];
    setAttributes(defaultAttributes);
  };

  useEffect(() => {
    if (
      form &&
      formInitValue &&
      (openType === 'edit' || openType === 'addSub') &&
      JSON.stringify(formInitValue) !== '{}'
    ) {
      const sourceValue = formInitValue.source;
      setIsAiSelected(sourceValue === 'AI感知');
      // 初始化属性数据
      if (formInitValue.attributes) {
        setAttributes(formInitValue.attributes);
      }
      if (openType === 'addSub') {
        // 新增时初始化默认属性
        initDefaultAttributes();
      }
    }
  }, [formInitValue, openType]);

  // 新增属性
  const handleAddAttribute = () => {
    setEditingAttribute(null);
    attributeForm.resetFields();
    setAttributeModalVisible(true);
  };

  // 编辑属性
  const handleEditAttribute = (attribute: AttributeItem) => {
    setEditingAttribute(attribute);
    attributeForm.setFieldsValue(attribute);
    setAttributeModalVisible(true);
  };

  // 删除属性
  const handleDeleteAttribute = (id: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个属性吗？',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        const newAttributes = attributes.filter(attr => attr.id !== id);
        setAttributes(newAttributes);
        if (form) {
          form.setFieldsValue({ attributes: newAttributes });
        }
        message.success('删除成功');
      },
    });
  };

  // 保存属性
  const handleSaveAttribute = async () => {
    try {
      const values = await attributeForm.validateFields();

      if (editingAttribute) {
        // 编辑模式
        const newAttributes = attributes.map(attr =>
          attr.id === editingAttribute.id ? { ...attr, ...values } : attr,
        );
        setAttributes(newAttributes);
        if (form) {
          form.setFieldsValue({ attributes: newAttributes });
        }
        message.success('修改成功');
      } else {
        // 新增模式
        const newAttribute: AttributeItem = {
          id: Date.now().toString(),
          ...values,
        };
        const newAttributes = [...attributes, newAttribute];
        setAttributes(newAttributes);
        if (form) {
          form.setFieldsValue({ attributes: newAttributes });
        }
        message.success('新增成功');
      }

      setAttributeModalVisible(false);
      attributeForm.resetFields();
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 属性表格列定义
  const attributeColumns = [
    {
      title: '属性名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
    },
    {
      title: '属性标识',
      dataIndex: 'identifier',
      key: 'identifier',
      width: 200,
    },
    {
      title: '数据类型',
      dataIndex: 'dataType',
      key: 'dataType',
      width: 100,
    },
    {
      title: '是否必填',
      dataIndex: 'required',
      key: 'required',
      width: 100,
      render: (required: boolean) => (required ? '是' : '否'),
    },
    {
      title: '是否显示',
      dataIndex: 'display',
      key: 'display',
      width: 100,
      render: (display: boolean) => (display ? '是' : '否'),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_: any, record: AttributeItem) => (
        <Space size="small" className={styles['action-buttons']}>
          <CommonLinkButton onClick={() => handleEditAttribute(record)}>编辑</CommonLinkButton>
          <CommonLinkButton onClick={() => handleDeleteAttribute(record.id)}>删除</CommonLinkButton>
        </Space>
      ),
    },
  ];

  return (
    <div className={styles['alert-type-form']}>
      {/* 类型基础信息 */}
      <Col span={24}>
        <h3 className={styles['section-title']}>类型基础信息</h3>
      </Col>

      <Row gutter={16}>
        <Col span={8}>
          <Form.Item label="预警父类名称" name="parentName" labelCol={{ span: 8 }}>
            <Input disabled placeholder="请输入预警父类名称" />
          </Form.Item>
        </Col>

        <Col span={8}>
          <Form.Item
            label="预警类型名称"
            name="name"
            labelCol={{ span: 8 }}
            rules={[{ required: true, message: '请输入预警类型名称' }]}
          >
            <Input placeholder="请输入预警类型名称" />
          </Form.Item>
        </Col>

        <Col span={8}>
          <Form.Item
            label="是否需要处置"
            name="needDisposal"
            labelCol={{ span: 8 }}
            initialValue={true}
          >
            <Radio.Group>
              <Radio value={true}>是</Radio>
              <Radio value={false}>否</Radio>
            </Radio.Group>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={8}>
          <Form.Item label="预警类型编码" name="encoding" labelCol={{ span: 8 }}>
            <Input disabled placeholder="-" />
          </Form.Item>
        </Col>

        <Col span={8}>
          <Form.Item label="处置对象" name="disposalTarget" labelCol={{ span: 8 }}>
            <Select options={disposalTargetOptions} placeholder="请选择处置对象">
              <Select.Option value="企业用户">企业用户</Select.Option>
              <Select.Option value="监管部门">监管部门</Select.Option>
              <Select.Option value="应急部门">应急部门</Select.Option>
            </Select>
          </Form.Item>
        </Col>

        <Col span={8}>
          <Form.Item label="归属单位" name="belongUnit" labelCol={{ span: 8 }}>
            <Select
              mode="multiple"
              options={belongUnitOptions}
              placeholder="请选择归属单位，支持多选"
              maxTagCount="responsive"
            />
          </Form.Item>
        </Col>
      </Row>

      {/* 类型附加属性 */}
      <Col span={24}>
        <h3 className={styles['section-title']}>类型附加属性</h3>
      </Col>

      <Col span={24}>
        <div className={styles['attribute-section']}>
          <div className={styles['attribute-header']}>
            <span className={styles['attribute-title']}>属性配置</span>
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAddAttribute}>
              新增属性
            </Button>
          </div>

          <Table
            columns={attributeColumns}
            dataSource={attributes}
            rowKey="id"
            pagination={false}
            size="small"
            bordered
          />
        </div>
      </Col>

      {/* 属性编辑弹框 */}
      <Modal
        title={editingAttribute ? '编辑属性' : '新增属性'}
        open={attributeModalVisible}
        onOk={handleSaveAttribute}
        onCancel={() => {
          setAttributeModalVisible(false);
          attributeForm.resetFields();
        }}
        okText="确定"
        cancelText="取消"
        width={600}
        className={styles['attribute-modal']}
      >
        <Form
          form={attributeForm}
          layout="vertical"
          initialValues={{
            required: false,
            display: true,
          }}
        >
          <Form.Item
            label="属性名称"
            name="name"
            rules={[{ required: true, message: '请输入属性名称' }]}
          >
            <Input placeholder="请输入属性名称" />
          </Form.Item>

          <Form.Item
            label="属性标识"
            name="identifier"
            rules={[
              { required: true, message: '请输入属性标识' },
              {
                pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/,
                message: '属性标识只能包含字母、数字和下划线，且不能以数字开头',
              },
            ]}
          >
            <Input placeholder="请输入属性标识，如：enterprise_name" />
          </Form.Item>

          <Form.Item
            label="数据类型"
            name="dataType"
            rules={[{ required: true, message: '请选择数据类型' }]}
          >
            <Select options={dataTypeOptions} placeholder="请选择数据类型" />
          </Form.Item>

          <Form.Item
            label="是否必填"
            name="required"
            rules={[{ required: true, message: '请选择是否必填' }]}
          >
            <Radio.Group>
              <Radio value={true}>是</Radio>
              <Radio value={false}>否</Radio>
            </Radio.Group>
          </Form.Item>

          <Form.Item
            label="是否显示"
            name="display"
            rules={[{ required: true, message: '请选择是否显示' }]}
          >
            <Radio.Group>
              <Radio value={true}>是</Radio>
              <Radio value={false}>否</Radio>
            </Radio.Group>
          </Form.Item>
        </Form>
      </Modal>

      {/* 隐藏的表单项用于存储属性数据 */}
      <Form.Item name="attributes" style={{ display: 'none' }}>
        <Input />
      </Form.Item>
    </div>
  );
};

export default AlertTypeForm;
