import React from 'react';
import { Input } from 'antd';
import SearchInput from '@/components/BaseFormComponents/SearchInput';

export const HomeTitle = '预警分类';

export const EditPageTitle = '预警分类编辑';

export const filterItems = [
  {
    name: 'name',
    label: '预警类型名称',
    component: <SearchInput placeholder="请输入预警类型名称" allowClear />,
  },
  {
    name: 'encoding',
    label: '预警类型编码',
    component: <SearchInput placeholder="请输入预警类型编码" allowClear />,
  },
  { name: 'broad', label: '预警大类', component: <Input placeholder="请输入" /> },
];

export const tableColumns: any[] = [
  { title: '序号', dataIndex: 'rowId', width: 80 },
  { title: '预警类型名称', dataIndex: 'name', ellipsis: true },
  { title: '预警类型编码', dataIndex: 'code', width: 180, ellipsis: true },
  { title: '类型来源', dataIndex: 'source', width: 180 },
  { title: '创建时间', dataIndex: 'createTime', width: 180 },
];
