import CommonLinkButton from '@/components/BaseFormComponents/CommonLinkButton';
import { CustomButton, updateFormFilterSelect } from '@/components/FilterForm';
import ListRequest from '@/components/ListRequest';
import ModalForm from '@/components/ModalForm';
import PageHeader from '@/components/PageHeader';
import { deleteAlertTypeInfo, saveAlertTypeInfo } from '@/services/systemMaintenance/alert';
import useDict from '@/utils/useDict';
import { message, Popconfirm } from 'antd';
import React, { useEffect, useState } from 'react';
import AlertTypeForm from './components/AlertTypeForm';
import { EditPageTitle, filterItems, HomeTitle, tableColumns } from './constants';

const customButtons: CustomButton[] = [
  { text: '新增', color: '#ffffff', bgColor: '#3164F6', onClick: () => {} },
];

const AlertTypeConfig: React.FC = () => {
  const { value: alertType } = useDict('warn_broad');
  const { value: aiType } = useDict('warn_aitype');

  const [formTitle, setFormTitle] = useState(HomeTitle);
  const [formVisible, setFormVisible] = useState(false);
  const [columns, setColumns] = useState([...tableColumns]);
  const [formFilters, setFormFilters] = useState([...filterItems]);
  const [openType, setOpenType] = useState<'add' | 'edit' | 'addSub'>('add');
  const [initValue, setInitValue] = useState<Record<string, any>>();
  const [typeId, setTypeId] = useState('');
  const [updateTrigger, setUpdateTrigger] = useState(false);
  const [expandedRowKeys, setExpandedRowKeys] = useState<any[]>([]);

  customButtons[0].onClick = () => {
    setOpenType('add');
    setFormVisible(true);
    setFormTitle(EditPageTitle);
  };

  const editAlterType = (record: any) => {
    setTypeId(record.id);
    // const initValue = getRecordInitValue(record, formItems);
    setInitValue({ ...record, parentName: record.name });
    setOpenType('edit');
    setFormVisible(true);
    setFormTitle(EditPageTitle);
  };

  const delAlterType = async (record: any) => {
    // 递归收集所有需要删除的子级ID
    const collectAllChildrenIds = (item: any): string[] => {
      let ids: string[] = [];
      if (item.children && item.children.length > 0) {
        item.children.forEach((child: any) => {
          ids.push(child.id);
          ids = ids.concat(collectAllChildrenIds(child));
        });
      }
      return ids;
    };

    // 收集所有需要删除的ID（包括当前记录和所有子级）
    const allIdsToDelete = [record.id, ...collectAllChildrenIds(record)];

    // 逐个删除所有相关记录
    const deletePromises = allIdsToDelete.map(id => deleteAlertTypeInfo(id));
    try {
      const results = await Promise.all(deletePromises);

      // 检查是否有删除失败的情况
      const failedResults = results.filter(res => res.code !== '200');
      if (failedResults.length > 0) {
        message.error(`部分删除失败: ${failedResults.map(res => res.msg).join(', ')}`);
      } else {
        message.success('删除成功！');
      }

      // 清除展开状态
      setExpandedRowKeys(expandedRowKeys.filter(key => key !== record.id));
      setUpdateTrigger(!updateTrigger);
    } catch (error) {
      message.error('删除过程中发生错误');
    }
  };

  const saveAlertType = async (formData: any) => {
    const extraInfo: Record<string, string> = {};
    if (openType === 'edit') extraInfo.id = typeId;
    const res = await saveAlertTypeInfo(Object.assign(formData, extraInfo));
    if (res.code === '200') {
      message.success('保存成功！');
      setUpdateTrigger(!updateTrigger);
      return true;
    }
    message.error(res.msg);
    return false;
  };

  function addSubType(record: any) {
    setTypeId(record.id);
    // const initValue = getRecordInitValue(record, formItems);
    setInitValue({ ...record, parentName: record.name, name: '' });
    setOpenType('addSub');
    setFormVisible(true);
    setFormTitle(EditPageTitle);
  }

  const setOperateColumns = () => {
    const actionColumn = {
      title: '操作',
      width: 200,
      noTooltip: true,
      render: (_text: any, record: any) => (
        <div>
          <CommonLinkButton onClick={() => addSubType(record)}>新增子类型</CommonLinkButton>
          <CommonLinkButton onClick={() => editAlterType(record)}>编辑</CommonLinkButton>
          {record?.parentId !== 0 && (
            <Popconfirm
              title=" "
              description="你确定要删除该预警类型吗？"
              onConfirm={() => delAlterType(record)}
              okText="确认"
              cancelText="取消"
            >
              <CommonLinkButton>删除</CommonLinkButton>
            </Popconfirm>
          )}
        </div>
      ),
    };
    setColumns([...tableColumns, actionColumn]);
  };

  const turnToListPage = () => {
    setFormVisible(false);
    setFormTitle(HomeTitle);
  };

  useEffect(() => {
    updateFormFilterSelect(2, alertType, formFilters, '请选择预警大类');
    setFormFilters(formFilters);
    setOperateColumns();
  }, [alertType]);

  useEffect(() => {
    setOperateColumns();
  }, [updateTrigger]);

  return (
    <div className="common-page">
      <PageHeader showBackIcon={formVisible} title={formTitle} onClick={turnToListPage} />
      {!formVisible && (
        <div className="common-page-body">
          <ListRequest
            apiUrl={`/api/warning/safeproduction/sort/sortList`}
            pageSize={10}
            filterItems={formFilters}
            columns={columns}
            updateTrigger={updateTrigger}
            customButtons={[]}
            inlineButtons
            method="GET"
            buttonCol={1}
            searchButtonStyle={{ marginLeft: 0 }}
            labelCol={{ span: 8 }}
            scrollYDelta={48}
            expandable={{
              // 可以指定子节点的字段名，默认为 'children'
              childrenColumnName: 'children',
              // 可以指定哪些行可以展开
              rowExpandable: record => record.children && record.children.length > 0,
              // 展开事件回调
              onExpand: (expanded, record) => {
                setExpandedRowKeys(
                  expanded
                    ? [...expandedRowKeys, record.id]
                    : [...expandedRowKeys.filter(key => key !== record.id)],
                );
              },
              expandedRowKeys: expandedRowKeys,
            }}
          />
        </div>
      )}
      <ModalForm
        openType={openType}
        visible={formVisible}
        initialValues={initValue}
        onSubmit={saveAlertType}
        formComp={<AlertTypeForm openType={openType} aiType={aiType} alertType={alertType} />}
        onClose={turnToListPage}
      />
    </div>
  );
};

export default AlertTypeConfig;
