import React, { useState } from 'react';
import { Button, DatePicker } from 'antd';
import '../index.less';

export type DateType = 'season' | 'month' | 'year' | '';

interface TimeSelectorProps {
  queryType: DateType;
  onRequestTypeChange: (type: DateType) => void;
  onDateRangeChange: (range: { startTime: string; endTime: string } | null) => void;
}

const TimeSelector: React.FC<TimeSelectorProps> = ({
  queryType,
  onRequestTypeChange,
  onDateRangeChange,
}) => {
  const [dateRange, setDateRange] = useState<[moment.Moment, moment.Moment] | null>(null);
  const handleRangeChange = (dates: [moment.Moment, moment.Moment] | null) => {
    setDateRange(dates);
    if (dates) {
      // 选中日期后，取消按钮选中
      onRequestTypeChange('');
      const [start, end] = dates;
      const formattedRange = {
        startTime: start.format('YYYY-MM-DD'),
        endTime: end.format('YYYY-MM-DD'),
      };
      onDateRangeChange(formattedRange);
    } else {
      // 日期清空时，默认选中“月”
      onRequestTypeChange('month');
      onDateRangeChange(null);
    }
  };

  const handleButtonClick = (type: DateType) => {
    // 按钮点击时，清空日期
    setDateRange(null);
    onRequestTypeChange(type);
    onDateRangeChange(null);
  };

  return (
    <div className="time-selector">
      <Button
        type={queryType === 'month' ? 'primary' : 'default'}
        onClick={() => handleButtonClick('month')}
      >
        月
      </Button>
      <Button
        type={queryType === 'season' ? 'primary' : 'default'}
        onClick={() => handleButtonClick('season')}
      >
        季
      </Button>
      <Button
        type={queryType === 'year' ? 'primary' : 'default'}
        onClick={() => handleButtonClick('year')}
        className="btn"
      >
        年
      </Button>
      自定义范围：
      {/* @ts-ignore */}
      <DatePicker.RangePicker value={dateRange} onChange={handleRangeChange} />
    </div>
  );
};

export default TimeSelector;
