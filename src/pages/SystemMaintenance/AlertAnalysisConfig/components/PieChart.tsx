// PieChart.tsx
import React, { useEffect, useRef, useState } from 'react';
import * as echarts from 'echarts';

interface PieChartProps {
  typeCount?: any[];
}
const PieChart: React.FC<PieChartProps> = ({ typeCount }) => {
  const pieChartRef = useRef<HTMLDivElement>(null);
  const [pieData, setPieData] = useState<any[]>([]);

  useEffect(() => {
    if (Array.isArray(typeCount)) {
      setPieData(
        typeCount.map(type => ({ value: parseInt(type.totalCount, 10), name: type.type })),
      );
    }
  }, [typeCount]);

  useEffect(() => {
    if (pieChartRef.current && pieData.length) {
      const pieChart = echarts.init(pieChartRef.current);
      const pieOption = {
        title: {
          text: '预警信息分类统计',
          left: 'left',
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)',
        },
        legend: {
          orient: 'vertical',
          left: '65%',
          top: '50px',
          icon: 'circle',
          type: 'scroll',
          formatter: (name: string) => {
            const item = pieData.find(d => d.name === name);
            const total = pieData.reduce((sum, d) => sum + d.value, 0);
            const percentage = (((item?.value || 0) / total) * 100).toFixed(2);
            return `   {name|${name}}\n{value|${item?.value}条    占比${percentage}%}`;
          },
          textStyle: {
            rich: {
              name: {
                fontWeight: 'bold', // 加粗
                lineHeight: 20,
                align: 'left',
                padding: [0, 0, 0, 15],
                fontSize: 15,
              },
              value: {
                fontSize: 13,
                lineHeight: 20,
                align: 'left',
                padding: [0, 0, 0, 15],
              },
            },
          },
        },
        series: [
          {
            name: '访问来源',
            type: 'pie',
            radius: '75%',
            center: ['30%', '50%'],
            label: {
              show: false,
            },
            data: pieData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
          },
        ],
      };
      pieChart.setOption(pieOption);
    }
  }, [pieData]);
  return <div ref={pieChartRef} style={{ width: '100%', height: '100%' }} />;
};
export default PieChart;
