import { statisticAnalysisList } from '@/services/systemMaintenance/alert';
import { Col, Empty, message, Row } from 'antd';
import React, { useEffect, useState } from 'react';
import AlertOverview from './components/AlertOverview';
import <PERSON><PERSON><PERSON> from './components/BarChart';
import Pie<PERSON><PERSON> from './components/PieChart';
import RankingChart from './components/RankingChart';
import TimeSelector, { DateType } from './components/TimeSelector';
import './index.less';

const ChartComponent: React.FC = () => {
  const [qryType, setRequestType] = useState<DateType>('season');
  const [typeCount, setTypeCount] = useState<any[]>([]);
  const [barData, setBarData] = useState<any[]>([]);
  const [warningCount, setWarningCount] = useState<any[]>([]);
  const [rankData, setRankData] = useState<any[]>([]);
  const [dateRange, setDateRange] = useState<{ startTime: string; endTime: string }>({
    startTime: '',
    endTime: '',
  });

  const queryDataList = async () => {
    const params = {
      qryType,
      ...dateRange,
    };
    const res = await statisticAnalysisList(params);
    if (res.code === '200') {
      const { data: { levelCount, owningRegionCount, typeCount, dataInfo } = {} } = res;
      setBarData(levelCount);
      setTypeCount(typeCount);
      setRankData(owningRegionCount);
      setWarningCount(dataInfo);
    } else message.error(res.msg);
  };

  const handleRequestTypeChange = (type: DateType) => {
    setRequestType(type);
  };

  const onDateRangeChange = (range: { startTime: string; endTime: string } | null) => {
    if (range) {
      setDateRange(range);
    } else setDateRange({ startTime: '', endTime: '' });
  };

  useEffect(() => {
    queryDataList();
    const handleResize = () => {
      // Resize logic if needed
    };
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  useEffect(() => {
    queryDataList();
  }, [qryType, dateRange]);

  return (
    <div className="alert-analysis-page common-page ">
      <Row className="spacer-row">
        <TimeSelector
          queryType={qryType}
          onDateRangeChange={onDateRangeChange}
          onRequestTypeChange={handleRequestTypeChange}
        />
      </Row>
      <Row className="overview-row">
        <AlertOverview warningCount={warningCount} />
      </Row>
      <Row className="charts-row">
        <Col span={10} className="chart-col">
          {typeCount?.length > 0 ? (
            <PieChart typeCount={typeCount} />
          ) : (
            <Empty description="暂无数据" image={Empty.PRESENTED_IMAGE_SIMPLE} />
          )}
        </Col>
        <Col span={6}>
          {rankData?.length > 0 ? (
            <RankingChart rankData={rankData} />
          ) : (
            <Empty description="暂无数据" image={Empty.PRESENTED_IMAGE_SIMPLE} />
          )}
        </Col>
        <Col span={8}>
          {barData?.length > 0 ? (
            <BarChart barData={barData} />
          ) : (
            <Empty description="暂无数据" image={Empty.PRESENTED_IMAGE_SIMPLE} />
          )}
        </Col>
      </Row>
    </div>
  );
};
export default ChartComponent;
