.alert-analysis-page {
  background-color: transparent;
  .spacer-row {
    display: flex;
    align-items: center;
    justify-content: end;
    height: calc(10% - 10px);
    padding: 0 12px;
    background-color: #fff;
    margin-bottom: 10px;
  }
  .overview-row {
    height: 30%;
    background-color: #fff;
  }
  .charts-row {
    height: 58%;
    margin-top: 1%;
    background-color: #fff;
    padding-top: 1%;
    .chart-col {
      padding: 12px;
    }
  }
}

.alert-info-card {
  display: flex;
  flex: 0 0 calc(50% - 8px);
  min-width: 0; // 防止内容溢出
  position: relative;
  top: 10%;
  border: 1px solid #ccc;
  width: 50%;
  height: 80%;
  padding: 14px;
  background-color: #fff;
  .alert-info-card__left {
    flex: 0 0 35%;
    padding-right: 16px;
    border-right: 1px solid #ccc;
    .alert-info-card__title {
      width: 100%;
      text-align: center;
      font-weight: bold;
      margin-bottom: 8px;
      font-size: 20px;
    }
    .alert-info-card__total {
      width: 100%;
      text-align: center;
      font-size: 18px;
    }
  }
  .alert-info-card__right {
    position: relative;
    overflow: hidden;
    display: flex;
    flex-wrap: wrap;
    flex: 1;
    padding-bottom: 0;
    .alert-info-card__alert {
      display: flex;
      justify-content: center;
      width: 50%;
      margin-bottom: 8px;
      .alert-info-card__alert-title {
        font-weight: bold;
        font-size: 15px;
      }
    }
    .alert-info-card__carousel-wrapper {
      position: relative;
      .page-btn {
        position: absolute;
        top: 50%;
        border: 0;
        transform: translateY(-50%);
        z-index: 1;
        &:first-child {
          left: 5px;
        }
        &:nth-child(2) {
          right: 0;
        }
      }
      .alert-info-card__carousel {
        display: flex;
        width: 100%;
        height: 100%;
        transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
      }
      .alert-info-card__page {
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        flex-shrink: 0;
        box-sizing: border-box;
        padding: 0 12px 0 12px; /* 把 padding 放到这里 */
      }
    }
  }
}

.alert-overview {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  padding: 10px 12px;
  .alert-title {
    font-size: 17px;
    font-weight: bold;
    width: 100%;
  }
  .alert-overview__left {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    flex: 0 0 15%;
    padding-right: 16px;
    background-color: #f2f2f2;

    .alert-overview__total-title {
      width: 100%;
      text-align: center;
      font-size: 22px;
      font-weight: bold;
    }
    .alert-overview__total-count {
      width: 100%;
      text-align: center;
      font-size: 18px;
    }
  }
  .alert-overview__right {
    display: flex;
    flex: 1;
    background-color: #f2f2f2;
    gap: 16px; // 用于分隔两个 AlertInfoCard
    padding-right: 12px;
    overflow-x: auto;
  }
}

.time-selector {
  .btn {
    margin-right: 15px;
  }
}
