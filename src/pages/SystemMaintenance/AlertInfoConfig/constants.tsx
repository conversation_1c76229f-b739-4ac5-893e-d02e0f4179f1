import React from 'react';
import SearchInput from '@/components/BaseFormComponents/SearchInput';
import { Select, Tag } from 'antd';

export const HomeTitle = '预警信息填报';

export const EditPageTitle = '预警信息填报';

export const filterItems = [
  {
    name: 'name',
    label: '预警名称',
    component: <SearchInput placeholder="请输入预警名称" />,
  },
  {
    name: 'type',
    label: '预警类型',
    component: <Select placeholder="请选择预警类型" allowClear />,
  },
  {
    name: 'level',
    label: '预警级别',
    component: <Select placeholder="请选择预警级别" allowClear />
  },
];

// 预警级别颜色映射
const levelColorMap: { [key: string]: string } = {
  '一级': '#ff4d4f', // 红色
  '二级': '#ff7a45', // 橙色
  '三级': '#faad14', // 黄色
  '四级': '#52c41a', // 绿色
};

export const tableColumns: any[] = [
  { title: '序号', dataIndex: 'rowId', width: 80 },
  { title: '预警名称', dataIndex: 'name', ellipsis: true, width: 200 },
  {
    title: '预警类型',
    dataIndex: 'type',
    width: 150,
    ellipsis: true,
  },
  {
    title: '预警级别',
    dataIndex: 'level',
    width: 120,
    render: (level: string) => (
      <Tag color={levelColorMap[level] || 'default'}>
        {level}
      </Tag>
    )
  },
  {
    title: '预警时间',
    dataIndex: 'startDate',
    width: 180,
    render: (time: number) => {
      if (!time) return '-';
      return new Date(time).toLocaleString('zh-CN');
    }
  },
  { title: '预警事项', dataIndex: 'item', ellipsis: true, width: 200 },
  {
    title: '预警来源',
    dataIndex: 'source',
    width: 120,
    render: (source: string) => {
      const sourceMap: { [key: string]: string } = {
        '0': '人工上报',
        '1': '系统监测',
        '2': '第三方接入',
        '3': '其他'
      };
      return sourceMap[source] || source;
    }
  },
  {
    title: '发布状态',
    dataIndex: 'warningState',
    width: 100,
    render: (status: string) => {
      // 根据API数据，warningState为"0"表示未发布，其他值或null表示已发布
      const isPublished = status !== '0' && status !== null;
      return (
        <Tag color={isPublished ? 'green' : 'orange'}>
          {isPublished ? '已发布' : '未发布'}
        </Tag>
      );
    }
  },
];
