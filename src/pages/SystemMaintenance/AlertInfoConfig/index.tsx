import { CustomButton } from '@/components/FilterForm';
import ListRequest from '@/components/ListRequest';
import ModalForm from '@/components/ModalForm';
import PageHeader from '@/components/PageHeader';
import { useAlertConfig } from '@/hook/useAlertConfig';
import { saveReportList } from '@/services/systemMaintenance/alert';
import { Button, message, Popconfirm, Space } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import AlertInfoForm from './components/AlertInfoForm';
import { EditPageTitle, filterItems, HomeTitle, tableColumns } from './constants';

const customButtons: CustomButton[] = [
  { text: '新增', color: '#ffffff', bgColor: '#3164F6', onClick: () => {} },
];

const AlertInfoConfig: React.FC = () => {
  const [formTitle, setFormTitle] = useState(HomeTitle);
  const [formVisible, setFormVisible] = useState(false);
  const [columns, setColumns] = useState([...tableColumns]);
  const formFilters = useMemo(() => [...filterItems], []);
  const [openType, setOpenType] = useState<'add' | 'view' | 'edit'>('add');
  const [updateTrigger, setUpdateTrigger] = useState(false);
  const [reportId, setReportId] = useState('');

  const { setAlertTypeOptions, setAlertLevelOptions } = useAlertConfig(
    formFilters,
    columns,
    setColumns,
    { typeIndex: 1, levelIndex: 2 },
  );

  customButtons[0].onClick = () => {
    setOpenType('add');
    setFormVisible(true);
    setFormTitle(EditPageTitle);
  };

  const viewAlertInfo = (record: any) => {
    setReportId(record.id);
    setOpenType('view');
    setFormVisible(true);
    setFormTitle('预警详情');
  };

  const editAlertInfo = (record: any) => {
    setReportId(record.id);
    setOpenType('edit');
    setFormVisible(true);
    setFormTitle('编辑预警信息');
  };

  const deleteAlertInfo = async (record: any) => {
    try {
      // 这里应该调用删除API
      // const res = await deleteAlert(record.id);
      // if (res.code === '200') {
      message.success('删除成功！');
      setUpdateTrigger(!updateTrigger);
      // } else {
      //   message.error(res.msg || '删除失败！');
      // }
    } catch (error) {
      message.error('删除失败！');
    }
  };

  const publishAlertInfo = async (record: any) => {
    try {
      // 这里应该调用发布API
      // const res = await publishAlert(record.id);
      // if (res.code === '200') {
      message.success('发布成功！');
      setUpdateTrigger(!updateTrigger);
      // } else {
      //   message.error(res.msg || '发布失败！');
      // }
    } catch (error) {
      message.error('发布失败！');
    }
  };

  const saveAlertInfo = async (formData: any) => {
    const groupedData: { [key: number]: { [key: string]: any } } = {};
    // 遍历 formData 的键值对
    Object.entries(formData).forEach(([key, value]) => {
      // 使用下划线分割键名
      const [fieldName, indexStr] = key.split('_');
      const index = parseInt(indexStr, 10);
      // 如果该索引还没有初始化，则初始化一个空对象
      if (!groupedData[index]) {
        groupedData[index] = {};
      }
      // 将数据存入对应的索引对象中
      groupedData[index][fieldName] = value;
    });
    // 将分组后的对象转换为数组
    const resultArray = Object.values(groupedData);

    const res = await saveReportList({ batchParam: resultArray });
    if (res.code === '200') {
      message.success('保存成功！');
      setUpdateTrigger(!updateTrigger);
      return true;
    }
    message.error(res.msg);
    return false;
  };

  const setOperateColumns = () => {
    const actionColumn = {
      title: '操作',
      width: 200,
      noTooltip: true,
      render: (_text: any, record: any) => {
        // 根据API数据，warningState为"0"表示未发布，其他值或null表示已发布
        const isPublished = record.warningState !== '0' && record.warningState !== null;
        return (
          <Space size="small">
            <Button type="link" size="small" onClick={() => viewAlertInfo(record)}>
              详情
            </Button>
            {!isPublished && (
              <>
                <Button type="link" size="small" onClick={() => editAlertInfo(record)}>
                  编辑
                </Button>
                <Popconfirm
                  title="确定要删除这条预警信息吗？"
                  onConfirm={() => deleteAlertInfo(record)}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button type="link" size="small" danger>
                    删除
                  </Button>
                </Popconfirm>
                <Button type="link" size="small" onClick={() => publishAlertInfo(record)}>
                  发布
                </Button>
              </>
            )}
          </Space>
        );
      },
    };
    setColumns([...tableColumns, actionColumn]);
  };

  const turnToListPage = () => {
    setFormVisible(false);
    setFormTitle(HomeTitle);
  };

  useEffect(() => {
    setOperateColumns();
    setAlertTypeOptions();
    setAlertLevelOptions();
  }, []);

  return (
    <div className="common-page">
      <PageHeader showBackIcon={formVisible} title={formTitle} onClick={turnToListPage} />
      {!formVisible && (
        <div className="common-page-body">
          <ListRequest
            apiUrl={`${ALARMPREFIX}/warning/safeproduction/report/reportList`}
            pageSize={10}
            filterItems={formFilters}
            columns={columns}
            customButtons={customButtons}
            inlineButtons
            method="GET"
            buttonCol={1}
            searchButtonStyle={{ marginLeft: -24 }}
            scrollYDelta={48}
            updateTrigger={updateTrigger}
          />
        </div>
      )}
      {formVisible && (
        <ModalForm
          openType={openType}
          visible={formVisible}
          onSubmit={saveAlertInfo}
          formComp={<AlertInfoForm reportId={reportId} openType={openType} />}
          onClose={turnToListPage}
        />
      )}
    </div>
  );
};

export default AlertInfoConfig;
