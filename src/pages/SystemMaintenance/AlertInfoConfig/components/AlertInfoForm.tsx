import AlertForm from '@/components/AlertForm';
import { getReportDetail } from '@/services/systemMaintenance/alert';
import { PlusOutlined } from '@ant-design/icons';
import { Button, FormInstance, Modal, Row, message } from 'antd';
import React, { useEffect, useState } from 'react';

interface ModalFormProps {
  form?: FormInstance<any>;
  openType?: 'add' | 'view' | 'edit';
  reportId: string;
}

const AlertTypeForm: React.FC<ModalFormProps> = ({ form, openType, reportId }) => {
  const [formCards, setFormCards] = useState<number[]>([0]);
  const [alertType, setAlertType] = useState('');
  const [cardIndexCounter, setCardIndexCounter] = useState<number>(1); // 新增计数器
  const [record, setRecord] = useState<any>({});
  const queryFormDetail = async () => {
    const res = await getReportDetail(reportId);

    if (res.code === '200') {
      const { data: [data] = [{}] } = res;

      if (data) {
        // 设置预警类型，需要根据type字段获取对应的预警类型名称
        const { type } = data;
        if (type) {
          setAlertType(type);
        }
        setRecord(data);
      }
    } else {
      message.error(res.msg || '获取详情失败！');
    }
  };

  useEffect(() => {
    if ((openType === 'view' || openType === 'edit') && reportId !== '') {
      queryFormDetail();
    } else if (openType === 'add') {
      // 新增模式时重置表单，只在dataLoaded为false时执行，避免重复执行
      form?.resetFields();
      setAlertType('');
    }
  }, [reportId, openType]);

  const handleCopy = (index: number) => {
    const newCardIndex = cardIndexCounter; // 使用计数器
    setCardIndexCounter(cardIndexCounter + 1); // 增加计数器
    const currentValues = form?.getFieldsValue() || {};
    const newValues: Record<string, any> = {};
    // 复制当前卡片的表单项值
    Object.keys(currentValues).forEach(key => {
      if (key.endsWith(`_${index}`)) {
        const newKey = key.replace(`_${index}`, `_${newCardIndex}`);
        newValues[newKey] = currentValues[key];
      }
    });
    form?.setFieldsValue(newValues);
    setFormCards([...formCards, newCardIndex]);
  };

  const handleDelete = (index: number) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个表单吗？',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        setFormCards(formCards.filter((_, i) => i !== index));
      },
    });
  };

  return (
    <>
      {formCards.map((cardIndex, index) => (
        <div
          key={cardIndex}
          style={{
            border: '1px solid #ccc',
            marginBottom: '16px',
            padding: '16px',
            position: 'relative',
          }}
        >
          {index !== 0 && (
            <Button
              type="primary"
              danger
              style={{ position: 'absolute', top: '16px', right: '16px', zIndex: 10 }}
              onClick={() => handleDelete(index)}
            >
              删除
            </Button>
          )}
          <Row gutter={[16, 0]}>
            <AlertForm
              type={alertType}
              record={record}
              cardIndex={openType === 'add' ? cardIndex : undefined}
              form={form}
              openType={openType}
            />
          </Row>
          {index === formCards.length - 1 && openType === 'add' && (
            <Button
              type="primary"
              block
              icon={<PlusOutlined />}
              onClick={() => handleCopy(cardIndex)}
              style={{ marginTop: '16px' }}
            >
              复制
            </Button>
          )}
        </div>
      ))}
    </>
  );
};
export default AlertTypeForm;
