.portalLayout-wrapper {
  position: relative;
  display: flex;
  height: calc(100vh);

  .left {
    display: flex;
    flex-direction: column;
    width: 360px;
    height: 100%;
    padding: 20px 20px 0 20px;
    background: #fff;

    .left-title {
      color: #333;
      font-weight: 500;
      font-size: 14px;
      font-family: PingFangSC-Medium;
    }

    .left-banner {
      height: 96px;
      margin-top: 10px;
      background-image: url('../../../assets/images/systemMaintenance/quanbubuju_beijing.png');
      background-size: 100% 100%;

      .banner-cont {
        display: flex;
        justify-content: space-between;
        padding-top: 15px;
        padding-right: 82px;
        padding-left: 30px;
        color: #fff;

        .banner-left {
          display: flex;
          align-items: center;

          .banner-title {
            margin-left: 22px;
            font-weight: 400;
            font-size: 14px;
            font-family: PingFangSC-Regular;
          }
        }

        .banner-right {
          display: flex;
          align-items: center;

          .count {
            font-weight: 700;
            font-size: 32px;
            font-family: DINAlternate-Bold;
          }

          .unit {
            margin-left: 10px;
            font-weight: 400;
            font-size: 14px;
            font-family: PingFangSC-Regular;
          }
        }
      }
    }

    .left-list {
      flex: 1;

      .list-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 40px;
        margin-top: 16px;
        padding: 0 5px 0 16px;
        border: 1px dashed rgba(0, 101, 213, 0.5);
        border-radius: 2px;

        &.active {
          background: #e1efff;
        }

        .item-left {
          display: flex;
          align-items: center;
          cursor: pointer;

          .item-title {
            color: #181818;
            font-weight: 400;
            font-size: 14px;
            font-family: PingFangSC-Regular;
          }

          .item-state {
            width: 52px;
            height: 18px;
            margin-left: 5px;
            color: #ffffff;
            font-weight: 500;
            font-size: 12px;
            font-family: PingFangSC-Medium;
            line-height: 18px;
            text-align: center;
            background-image: linear-gradient(216deg, #97ca37 0%, #00afff 100%);
            border-radius: 9px;
            box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.2);
          }
        }

        .item-right {
          display: flex;
          align-items: center;

          .anticon {
            margin-left: 12px;
            color: #3164f6;
            font-size: 12px;
            cursor: pointer;
          }

          .ant-switch {
            margin-left: 6px;
            scale: 0.6;
          }
        }
      }
    }

    .left-foot {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 55px;
      margin: 0 -20px 0 -20px;
      border-top: 1px #f0f2f5 solid;
    }
  }

  .cardList {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 9999;
    width: 120px;
    padding: 10px 15px;
    background: #fff;
    border: 1px #eee solid;
    border-radius: 8px;
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.2);

    .ant-checkbox-wrapper {
      margin: 3px 0;
    }

    .btns {
      margin-top: 10px;
      text-align: center;
    }
  }

  .right {
    width: 100%;
    height: 100%;
    overflow-y: auto;

    .topImg {
      width: 100%;
    }
  }
}
