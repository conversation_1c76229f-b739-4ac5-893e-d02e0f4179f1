import { getColumnList, listModuleInfo } from '@/services/systemMaintenance/module';
import { listLayout, saveTemplate } from '@/services/systemMaintenance/portalLayout';
import { downloadFile } from '@/utils/commonFileFun';
import { Button, Checkbox, message } from 'antd';
import React, { useEffect, useState } from 'react';
import GridLayout from 'react-grid-layout';
import './index.less';
import './reactGridLayout.less';

const PortalLayout: React.FC<any> = props => {
  const [layout, setLayout] = useState<any[]>([]);
  const [moduleList, setModuleList] = useState<any[]>([]);
  // const isTemplate = true;
  // const [isTemplate, setIsTemplate] = useState<boolean>(true);
  const [curTemplate, setCurTemplate] = useState<any>(null);
  const [columnInfo, setColumnInfo] = useState<any>({});
  const getLayoutList = async () => {
    const res = await listLayout();
    // const userRes = await getUserTemplateList();
    const columnListRes = await getColumnList({
      pageSize: 9999,
      pageIndex: 1,
      orgId: userInfo?.orgId,
    });
    const resList = await Promise.all([res, columnListRes]);
    let tempLayout = [];
    let defaultLayout = [];
    // let userLayout = [];
    let curentInfo = [];

    // 默认模板
    if (resList[0].code === '200') {
      const item = resList[0].data || [];
      if (item.length > 0) {
        defaultLayout = JSON.parse(item?.[0]?.layoutJson || '[]');
        curentInfo = item;
      }
    }
    // 用户模板
    // if (resList[1].code === '200') {
    //   const item = resList[1].data || [];
    //   if (item?.length > 0) {
    //     userLayout = JSON.parse(item?.[0]?.layoutJson || '[]');
    //     curentInfo = item;
    //     setLayout(userLayout);
    //     setCurTemplate(item?.[0] || {});
    //   }
    // }
    if (resList?.[1].code === '200') {
      const list = resList?.[1]?.data?.data || [];
      const styleIds = list?.map((item: any) => item.styleId)?.join(',');
      getStyleInfos(styleIds, list, defaultLayout);
    }
    tempLayout = defaultLayout;
    if (tempLayout?.length <= 0) {
      saveTemplate({
        tempName: `模块_${1}`,
        layoutJson: JSON.stringify([
          {
            w: 6,
            h: 30,
            x: 0,
            y: 9999, // puts it at the bottom
            i: `${resList[1]?.data?.data?.[0]?.columnId}`,
            // columnId: resList[2]?.data?.data?.[0]?.columnId,

            minW: 6,
            maxW: 12,
            static: false,
          },
        ]),
        columnId: `${resList[1]?.data?.data?.[0]?.columnId}`,
      }).then(res => {
        if (res.code === '200') {
          // saveUserTemplate(
          //   {
          //     tempId: res?.data?.[0],
          //     tempName: `模块_${1}`,
          //     layoutJson: JSON.stringify([
          //       {
          //         w: 6,
          //         h: 30,
          //         x: 0,
          //         y: 9999, // puts it at the bottom
          //         i: resList[2]?.data?.data?.[0]?.columnId,
          //         // columnId: resList[2]?.data?.data?.[0]?.columnId,
          //         minW: 6,
          //         maxW: 12,
          //         static: false,
          //       },
          //     ]),
          //     userName: isTemplate ? '' : userInfo?.username,
          //     columnId: resList[2]?.data?.data?.[0]?.columnId,
          //     // moduleList: moduleList.filter((item) => item.checked),
          //   },
          //   { headers: { isDebug: true } },
          // ).then((res) => {
          //   if (res.code === '200') {
          //     getUserLayoutList();
          //   } else {
          //     message.error(res.msg);
          //   }
          // });
          listLayout();
        } else {
          message.error(res.msg);
        }
      });
    } else {
      setLayout(tempLayout);
      setCurTemplate(curentInfo?.[0] || {});
    }
  };
  // const getUserLayoutList = async () => {
  //   const userRes = await getUserTemplateList();

  //   // 用户模板
  //   if (userRes.code === '200') {
  //     const item = userRes.data || [];
  //     const userLayout = JSON.parse(item?.[0]?.layoutJson || '[]');
  //     setLayout(userLayout);
  //     setCurTemplate(item?.[0] || {});
  //   }
  // };

  const getStyleInfos = async (styleIds: any[], list: any[], userLayout: any[]) => {
    const res = await listModuleInfo({ pageIndex: 1, pageSize: 9999, styleIds });
    if (res.code === '200') {
      const temp: any = {};
      // const tempColumnMap: any = {};
      const tempColumnInfoMap: any = {};

      res.data?.forEach((item: any) => {
        if (item.styleId) {
          temp[item.styleId] = item.styleCode;
        }
      });

      const layoutModules = userLayout.map(item => item.i);
      list.forEach((item: any) => {
        item.checked = false;
        item.styleId = `${item.styleId}`;
        item.columnId = `${item.columnId}`;
        // tempColumnMap[item.columnId] = temp[item.styleId];
        tempColumnInfoMap[item.columnId] = item;
        if (layoutModules.includes(item.columnId)) {
          item.checked = true;
        }
      });
      setModuleList(list);
      setColumnInfo(tempColumnInfoMap);
    }
  };

  useEffect(() => {
    getLayoutList();
  }, []);

  const onLayoutChange = (layer: any) => {
    console.log('onLayoutChange==', layer);
    setLayout(layer);
  };

  const geneDom = () => {
    return layout.map(lay => {
      return (
        <div data-grid={{ ...lay, minW: lay.minW, maxW: lay.maxW }} key={lay.i}>
          <img src={downloadFile(columnInfo[lay.i]?.tgpUrl)} alt="" />
        </div>
      );
    });
  };

  const onCheckboxChange = (e: any, item: any) => {
    const { checked } = e.target;
    console.log('onCheckboxChange==', checked, item);
    const _moduleList = JSON.parse(JSON.stringify(moduleList));
    _moduleList.forEach((i: any) => {
      if (Number(i.columnId) === Number(item.columnId)) {
        i.checked = checked;
      }
    });
    setModuleList(_moduleList);

    const layConfig = JSON.parse(JSON.stringify(layout));
    if (checked) {
      layConfig.push({
        w: 6,
        h: 30,
        x: 0,
        y: 9999, // puts it at the bottom
        i: `${item.columnId}`,
        // columnId: item.columnId,
        minW: 6,
        maxW: 12,
        static: false,
      });
    } else {
      const index = layConfig.findIndex((i: any) => Number(i.i) === Number(item.columnId));
      if (index !== -1) {
        layConfig.splice(index, 1);
      }
    }
    setLayout(layConfig);
  };

  const save = () => {
    // if (isTemplate) {
    saveTemplate({
      ...curTemplate,
      tempName: `模块_${1}`,
      layoutJson: JSON.stringify(layout),
      columnId: moduleList
        .filter(item => item.checked)
        ?.map(item => item.columnId)
        ?.join(','),
      // moduleList: moduleList.filter((item) => item.checked),
    }).then(res => {
      if (res.code === '200') {
        message.success('保存成功');
        // getUserLayoutList();
      } else {
        message.error(res.msg);
      }
    });
    // }
    // saveUserTemplate(
    //   {
    //     ...curTemplate,
    //     tempName: `模块_${1}`,
    //     // layoutJson:
    //     //   '[{"w":10,"h":52,"x":0,"y":0,"i":"7","minW":6,"maxW":12,"moved":false,"static":false},{"w":6,"h":30,"x":0,"y":52,"i":"8","minW":6,"maxW":12,"moved":false,"static":false}]',
    //     layoutJson: JSON.stringify(layout),
    //     userName: isTemplate ? '' : userInfo?.username,
    //     columnId: moduleList
    //       .filter((item) => item.checked)
    //       ?.map((item) => item.columnId)
    //       ?.join(','),
    //     // moduleList: moduleList.filter((item) => item.checked),
    //   },
    //   { headers: { isDebug: true } },
    // ).then((res) => {
    //   if (res.code === '200') {
    //     message.success('保存成功');
    //     getUserLayoutList();
    //   } else {
    //     message.error(res.msg);
    //   }
    // });
  };

  return (
    <div className="portalLayout-wrapper">
      <div className="cardList">
        {moduleList.map(item => {
          return (
            <Checkbox
              key={`${item.columnId}`}
              checked={item.checked}
              onChange={e => onCheckboxChange(e, item)}
            >
              {item.columnName}
            </Checkbox>
          );
        })}
        {/*
        <Checkbox checked={isTemplate} onChange={(e) => setIsTemplate(e.target.checked)}>
          是否保存默认模板
        </Checkbox> */}
        <div className="btns">
          <Button type="primary" onClick={() => save()}>
            保存
          </Button>
        </div>
      </div>

      <div className="right">
        {/* <img className="topImg" src={homeTop} alt="" /> */}
        <div className="grid-layout" id="grid-layout">
          <GridLayout
            className="layout"
            cols={12}
            rowHeight={1}
            margin={[10, 10]}
            isBounded={false}
            isDraggable
            isResizable
            onLayoutChange={onLayoutChange}
            layout={layout}
            width={1340}
          >
            {geneDom()}
          </GridLayout>
        </div>
      </div>
    </div>
  );
};

export default PortalLayout;
