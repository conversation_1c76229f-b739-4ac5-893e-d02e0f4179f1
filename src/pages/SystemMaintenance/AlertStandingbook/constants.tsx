import SearchInput from '@/components/BaseFormComponents/SearchInput';
import { DatePicker, Select } from 'antd';
import React from 'react';

const { RangePicker } = DatePicker;

export const HomeTitle = '预警台账';

export const EditPageTitle = '预警详情';

export const filterItems = [
  {
    name: 'name',
    label: '预警名称',
    component: <SearchInput placeholder="请输入预警名称" allowClear />,
  },
  {
    name: 'timeRange',
    label: '预警时间',
    component: <RangePicker placeholder={['请选择开始时间', '请选择结束时间']} />,
  },
  {
    name: 'type',
    label: '预警类别',
    component: <Select placeholder="请选择预警类别" allowClear />,
  },
  {
    name: 'handleState',
    label: '督办状态',
    component: <Select placeholder="请选择督办状态" allowClear />,
  },
];

export const tableColumns: any[] = [
  { title: '序号', dataIndex: 'rowId', width: 80 },
  { title: '预警名称', dataIndex: 'name', ellipsis: true, width: 200 },
  { title: '预警类型', dataIndex: 'type', width: 120, ellipsis: true },
  { title: '预警级别', dataIndex: 'level', width: 100 },
  { title: '预警时间', dataIndex: 'startDate', width: 150 },
  { title: '预警事项', dataIndex: 'item', ellipsis: true, width: 200 },
  { title: '预警来源', dataIndex: 'source', width: 120 },
  { title: '处置状态', dataIndex: 'warningState', width: 100 },
  { title: '督办状态', dataIndex: 'handleState', width: 100 },
];

// 统计卡片配置
export const totalCard = {
  key: 'total',
  title: '预警总数',
  color: '#1890ff',
  bgColor: '#e6f7ff',
};

export const levelCards = [
  {
    key: 'red',
    title: '红色预警',
    color: '#ff4d4f',
    bgColor: '#fff2f0',
    level: 'red',
    icon: '🔴',
  },
  {
    key: 'orange',
    title: '橙色预警',
    color: '#fa8c16',
    bgColor: '#fff7e6',
    level: 'orange',
    icon: '🟠',
  },
  {
    key: 'yellow',
    title: '黄色预警',
    color: '#fadb14',
    bgColor: '#feffe6',
    level: 'yellow',
    icon: '🟡',
  },
  {
    key: 'blue',
    title: '蓝色预警',
    color: '#1890ff',
    bgColor: '#e6f7ff',
    level: 'blue',
    icon: '🔵',
  },
];

export const statusCards = [
  {
    key: 'untreated',
    title: '未处置预警',
    color: '#722ed1',
    bgColor: '#f9f0ff',
    status: 'untreated',
    icon: '⏳',
  },
  {
    key: 'treated',
    title: '已处置预警',
    color: '#52c41a',
    bgColor: '#f6ffed',
    status: 'treated',
    icon: '✅',
  },
  {
    key: 'ignored',
    title: '已忽略预警',
    color: '#8c8c8c',
    bgColor: '#f5f5f5',
    status: 'ignored',
    icon: '⏸️',
  },
];

// 所有卡片的合集，用于查找
export const allCards = [totalCard, ...levelCards, ...statusCards];
