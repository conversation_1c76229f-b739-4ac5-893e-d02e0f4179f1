import { Divider, Typography } from 'antd';
import React from 'react';
import SupervisionDetail from './SupervisionDetail';

interface SupervisionInfoProps {
  handles: any[];
  stateOptions: any[];
}

const SupervisionInfo: React.FC<SupervisionInfoProps> = ({ handles, stateOptions }) => {
  console.log('handles: ', handles);
  return (
    <div style={{ padding: '16px' }}>
      <Typography.Title level={5}>预警流程信息</Typography.Title>
      <Divider style={{ margin: '12px 0', borderColor: 'gray' }} />
      {handles.map(handle => (
        <SupervisionDetail key={handle.id} stateOptions={stateOptions} data={handle} />
      ))}
    </div>
  );
};

export default SupervisionInfo;
