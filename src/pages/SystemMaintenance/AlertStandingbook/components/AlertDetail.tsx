import React, { useEffect, useState } from 'react';
import { Card, Descriptions, Timeline, Tag, Spin, message } from 'antd';
import { getReportDetail } from '@/services/systemMaintenance/alert';
import moment from 'moment';
import './AlertDetail.less';

interface AlertDetailProps {
  reportId: string;
}

const AlertDetail: React.FC<AlertDetailProps> = ({ reportId }) => {
  const [loading, setLoading] = useState(false);
  const [alertData, setAlertData] = useState<any>({});

  const fetchAlertDetail = async () => {
    if (!reportId) return;
    
    setLoading(true);
    try {
      const res = await getReportDetail(reportId);
      if (res.code === '200') {
        const { data: [data] = [{}] } = res;
        setAlertData(data || {});
      } else {
        message.error(res.msg || '获取详情失败！');
      }
    } catch (error) {
      message.error('获取详情失败！');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAlertDetail();
  }, [reportId]);

  const renderWarningLevel = (level: string) => {
    const levelMap: { [key: string]: { color: string; text: string } } = {
      '1': { color: 'red', text: '红色预警' },
      '2': { color: 'orange', text: '橙色预警' },
      '3': { color: 'yellow', text: '黄色预警' },
      '4': { color: 'blue', text: '蓝色预警' },
    };
    const levelInfo = levelMap[level] || { color: 'default', text: level };
    return <Tag color={levelInfo.color}>{levelInfo.text}</Tag>;
  };

  const renderWarningSource = (source: string) => {
    const sourceMap: { [key: string]: string } = {
      '0': '人工上报',
      '1': '系统监测',
      '2': '第三方接入',
      '3': '其他',
    };
    return sourceMap[source] || source;
  };

  const renderPublishStatus = (status: string) => {
    return status === '1' ? (
      <Tag color="green">已发布</Tag>
    ) : (
      <Tag color="orange">未发布</Tag>
    );
  };

  const renderTrackingTimeline = () => {
    const timelineItems = [];

    // 预警发布
    if (alertData.startDate) {
      timelineItems.push({
        color: 'blue',
        children: (
          <div>
            <div className="timeline-title">预警发布</div>
            <div className="timeline-content">
              <p><strong>发布时间：</strong>{moment(alertData.startDate).format('YYYY-MM-DD HH:mm:ss')}</p>
              <p><strong>发布单位：</strong>{alertData.issuingAuthority || '-'}</p>
            </div>
          </div>
        ),
      });
    }

    // 预警督办（如果有督办信息）
    if (alertData.handleState && alertData.handleState !== '0') {
      timelineItems.push({
        color: 'orange',
        children: (
          <div>
            <div className="timeline-title">预警督办</div>
            <div className="timeline-content">
              <p><strong>督办状态：</strong>{alertData.handleState}</p>
              <p><strong>督办时间：</strong>{alertData.handleTime ? moment(alertData.handleTime).format('YYYY-MM-DD HH:mm:ss') : '-'}</p>
              <p><strong>督办人：</strong>{alertData.handlePerson || '-'}</p>
              <p><strong>处置时限：</strong>{alertData.handleDeadline || '-'}</p>
            </div>
          </div>
        ),
      });
    }

    // 预警处置
    if (alertData.disposerName || alertData.ignoreReason) {
      const isIgnored = !!alertData.ignoreReason;
      timelineItems.push({
        color: isIgnored ? 'red' : 'green',
        children: (
          <div>
            <div className="timeline-title">预警处置</div>
            <div className="timeline-content">
              <p><strong>处置状态：</strong>{isIgnored ? '已忽略' : '已处置'}</p>
              <p><strong>处置时间：</strong>{alertData.disposerDate ? moment(alertData.disposerDate).format('YYYY-MM-DD HH:mm:ss') : '-'}</p>
              <p><strong>{isIgnored ? '忽略人' : '处置人'}：</strong>{isIgnored ? alertData.ignoreOperateName : alertData.disposerName}</p>
              <p><strong>{isIgnored ? '忽略内容' : '处置内容'}：</strong>{isIgnored ? alertData.ignoreReasonDescribe : alertData.disposerContent}</p>
            </div>
          </div>
        ),
      });
    }

    return <Timeline items={timelineItems} />;
  };

  if (loading) {
    return <Spin spinning={loading} style={{ width: '100%', minHeight: '200px' }} />;
  }

  return (
    <div className="alert-detail">
      {/* 预警信息 */}
      <Card title="预警信息" style={{ marginBottom: 24 }}>
        <Descriptions column={2} bordered>
          <Descriptions.Item label="预警名称">{alertData.name || '-'}</Descriptions.Item>
          <Descriptions.Item label="预警类型">{alertData.type || '-'}</Descriptions.Item>
          <Descriptions.Item label="预警级别">
            {renderWarningLevel(alertData.level)}
          </Descriptions.Item>
          <Descriptions.Item label="预警时间">
            {alertData.startDate ? moment(alertData.startDate).format('YYYY-MM-DD HH:mm:ss') : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="所属区域">{alertData.owningRegion || '-'}</Descriptions.Item>
          <Descriptions.Item label="填报单位">{alertData.reportingUnit || '-'}</Descriptions.Item>
          <Descriptions.Item label="是否发布">
            {renderPublishStatus(alertData.warningState)}
          </Descriptions.Item>
          <Descriptions.Item label="发布单位">{alertData.issuingAuthority || '-'}</Descriptions.Item>
          <Descriptions.Item label="预警来源">
            {renderWarningSource(alertData.source)}
          </Descriptions.Item>
          <Descriptions.Item label="预警事项" span={2}>
            {alertData.item || '-'}
          </Descriptions.Item>
          <Descriptions.Item label="可能影响范围" span={2}>
            {alertData.influenceRadius || '-'}
          </Descriptions.Item>
          <Descriptions.Item label="应采取防范措施" span={2}>
            {alertData.measure || '-'}
          </Descriptions.Item>
          <Descriptions.Item label="附件" span={2}>
            {alertData.influenceRadiusFile ? '有附件' : '无附件'}
          </Descriptions.Item>
        </Descriptions>
      </Card>

      {/* 预警跟踪 */}
      <Card title="预警跟踪">
        {renderTrackingTimeline()}
      </Card>
    </div>
  );
};

export default AlertDetail;
