import AlertForm from '@/components/AlertForm';
import CompanyForm from '@/components/AlertForm/components/CompanyForm';
import { getSupervisionDetail } from '@/services/systemMaintenance/alert';
import { getOptionsFromDict } from '@/utils/commonFunction';
import { FormInstance, Row, message } from 'antd';
import React, { useEffect, useState } from 'react';
import '../index.less';
import SupervisionInfo from './SupervisionInfo';

interface ModalFormProps {
  form?: FormInstance<any>;
  openType?: 'edit' | 'view';
  reportId: string;
  handleState: any[];
}

const HandleForm: React.FC<ModalFormProps> = ({ form, openType, reportId, handleState }) => {
  const [alertType, setAlertType] = useState('');
  const [handles, setHandles] = useState<any[]>([]);
  const [stateOptions, setStateOptions] = useState<any[]>([]);
  const [rectInfo, setRectInfo] = useState<any[]>([]);

  const queryFormDetail = async () => {
    const res = await getSupervisionDetail(reportId);
    if (res.code === '200') {
      const { data: [data] = [{}] } = res;
      if (data) {
        form?.setFieldsValue(data);
        const { type, handles, records } = data;
        setAlertType(type);
        setHandles(handles || []);
        setRectInfo(records || []);
      }
    } else message.error(res.msg || '获取详情失败！');
  };

  useEffect(() => {
    if (openType === 'view' && reportId !== '') {
      queryFormDetail();
    }
  }, [reportId, openType]);

  useEffect(() => {
    if (Array.isArray(handleState) && handleState.length > 0) {
      setStateOptions(getOptionsFromDict(handleState));
    }
  }, [handleState]);

  return (
    <>
      <div
        style={{
          flex: handles && handles.length > 0 ? 2.3 : undefined,
          paddingRight: handles && handles.length > 0 ? '12px' : '0',
        }}
        className="supervision-handle-form"
      >
        <Row gutter={[16, 0]}>
          <AlertForm type={alertType} form={form} />
          {rectInfo?.length > 0 && <CompanyForm rectificationInfo={rectInfo} />}
        </Row>
      </div>
      {handles && handles.length > 0 && (
        <div style={{ flex: 0.7 }}>
          <SupervisionInfo stateOptions={stateOptions} handles={handles} />
        </div>
      )}
    </>
  );
};
export default HandleForm;
