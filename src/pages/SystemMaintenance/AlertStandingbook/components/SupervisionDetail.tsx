import { Tag, Typography } from 'antd';
import moment from 'moment';
import React from 'react';
import '../index.less';

interface SupervisionDetailProps {
  data: any; // 数据对象
  stateOptions?: { label: string; value: string }[]; // 状态选项（督办阶段使用）
}
const SupervisionDetail: React.FC<SupervisionDetailProps> = ({ data, stateOptions }) => {
  // 阶段配置映射
  const stageConfig: any = {
    publish: {
      getStatusTag: () => <Tag color="blue">预警发布</Tag>,
      getTimeField: () => data?.startDate || data?.publishTime,
      renderFields: () => (
        <>
          <div className="item">
            <div className="label">发布时间:</div>
            <div>
              {data?.startDate ? moment(data.startDate).format('YYYY-MM-DD HH:mm:ss') : '-'}
            </div>
          </div>
          <div className="item">
            <div className="label">发布单位:</div>
            <div>{data?.issuingAuthority || data?.publishUnit || '-'}</div>
          </div>
        </>
      ),
    },
    supervision: {
      getStatusTag: () => {
        if (stateOptions && data?.handleState) {
          const matchedOption = stateOptions
            .slice(1)
            .find(option => option.value === data.handleState);
          const tagLabel = matchedOption ? matchedOption.label : '督办中';
          const index = stateOptions
            .slice(1)
            .findIndex(option => option.value === data.handleState);
          const colors = ['orange', '#D58D16', '#D53416'];
          const color = colors[index] || 'orange';
          return <Tag color={color}>{tagLabel}</Tag>;
        }
        return <Tag color="orange">督办中</Tag>;
      },
      getTimeField: () => data?.handleTime || data?.createTime,
      renderFields: () => (
        <>
          <div className="item">
            <div className="label">督办时间:</div>
            <div>
              {data?.handleTime ? moment(data.handleTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
            </div>
          </div>
          <div className="item">
            <div className="label">督办人:</div>
            <div>{data?.handlePerson || data?.createBy || '-'}</div>
          </div>
          <div className="item">
            <div className="label">处置时限:</div>
            <div>{data?.handleDeadline || data?.deadline || '-'}</div>
          </div>
        </>
      ),
    },
    disposal: {
      getStatusTag: () => {
        const isIgnored = !!data?.ignoreReason || !!data?.ignoreReasonDescribe;
        const isDisposed = !!data?.disposerName || !!data?.disposerContent;
        if (isIgnored) {
          return <Tag color="red">已忽略</Tag>;
        } else if (isDisposed) {
          return <Tag color="green">已处置</Tag>;
        }
        return <Tag color="blue">预警处置</Tag>;
      },
      getTimeField: () => data?.disposerDate || data?.ignoreDate || data?.disposeTime,
      renderFields: () => {
        const isIgnored = !!data?.ignoreReason || !!data?.ignoreReasonDescribe;
        const personLabel = isIgnored ? '忽略人' : '处置人';
        const contentLabel = isIgnored ? '忽略内容' : '处置内容';
        const personValue = isIgnored
          ? data?.ignoreOperateName || data?.ignorePerson
          : data?.disposerName;
        const contentValue = isIgnored
          ? data?.ignoreReasonDescribe || data?.ignoreReason
          : data?.disposerContent;

        return (
          <>
            <div className="item">
              <div className="label">处置时间:</div>
              <div>
                {data?.disposerDate
                  ? moment(data.disposerDate).format('YYYY-MM-DD HH:mm:ss')
                  : data?.ignoreDate
                  ? moment(data.ignoreDate).format('YYYY-MM-DD HH:mm:ss')
                  : '-'}
              </div>
            </div>
            <div className="item">
              <div className="label">{personLabel}:</div>
              <div>{personValue || '-'}</div>
            </div>
            <div className="item">
              <div className="label">{contentLabel}:</div>
              <div>{contentValue || '-'}</div>
            </div>
          </>
        );
      },
    },
  };

  // 获取当前阶段配置
  const currentStage = stageConfig?.[data?.stageType || 'supervision'] || {
    title: '未知阶段',
    getStatusTag: () => <Tag>未知状态</Tag>,
    getTimeField: () => null,
    renderFields: () => null,
  };

  return (
    <div className="supervision-detail">
      <div className="header">
        <div className="circle"></div>
        <Typography.Text>
          {currentStage.getTimeField()
            ? moment(currentStage.getTimeField()).format('YYYY-MM-DD HH:mm:ss')
            : '-'}
        </Typography.Text>
      </div>
      <div className="content">
        <div className="line"></div>
        <div>
          <div className="stage-header">
            {/* <span className="stage-title">{currentStage.title}</span> */}
            {currentStage.getStatusTag()}
          </div>
          {currentStage.renderFields()}
        </div>
      </div>
    </div>
  );
};
export default SupervisionDetail;
