.statistics-cards {
  margin-bottom: 12px;

  .empty-state {
    padding: 40px 0;
    text-align: center;
    background: #fafafa;
    border-radius: 8px;
    border: 1px dashed #d9d9d9;
  }

  .main-row {
    align-items: stretch;
  }

  .level-row {
    position: relative;

    &::after {
      position: absolute;
      right: 0;
      bottom: -8px;
      left: 0;
      height: 1px;
      background-color: #e8e8e8;
      content: '';
    }
  }

  .total-card-wrapper {
    height: 100%;

    .statistics-card {
      height: 100%;

      .ant-card-body {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
      }

      .card-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        min-height: 60px;
        padding: 20px;
        color: white !important;

        background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
        border-radius: 8px;

        .card-title {
          margin-bottom: 8px;
          color: white !important;
          font-weight: 500;
          font-size: 16px;
        }

        .card-count {
          color: white !important;
          font-weight: bold;
          font-size: 36px;
        }
      }

      &.selected {
        .card-content {
          width: 100%;
          background: linear-gradient(135deg, #40a9ff 0%, #69c0ff 100%);
        }
      }
    }
  }

  .statistics-card {
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transform: translateY(-2px);
    }

    &.selected {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transform: translateY(-2px);
    }

    .ant-card-body {
      padding: 8px;
    }

    .card-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 30px;
      text-align: center;

      .card-icon {
        margin-bottom: 4px;
        font-size: 20px;
      }

      .card-info {
        .card-title {
          margin-bottom: 2px;
          font-weight: 500;
          font-size: 13px;
          white-space: nowrap;
        }

        .card-count {
          font-weight: bold;
          font-size: 18px;
        }
      }
    }
  }
}

// 响应式调整
@media (max-width: 768px) {
  .statistics-cards {
    .statistics-card {
      .card-content {
        min-height: 60px;

        .card-icon {
          margin-bottom: 4px;
          font-size: 20px;
        }

        .card-info {
          .card-title {
            font-size: 12px;
          }

          .card-count {
            font-size: 16px;
          }
        }
      }
    }
  }
}
