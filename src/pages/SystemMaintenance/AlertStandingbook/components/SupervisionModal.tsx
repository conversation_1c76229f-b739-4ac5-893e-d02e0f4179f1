import React from 'react';
import { Modal, Input, Form, DatePicker } from 'antd';
import moment from 'moment';

interface SupervisionModalProps {
  visible: boolean;
  title: string;
  onOk: () => void;
  onCancel: () => void;
  deadline: string;
  setDeadline: (value: string) => void;
  content: string;
  setContent: (value: string) => void;
  company: string;
  setCompany: (value: string) => void;
}
const SupervisionModal: React.FC<SupervisionModalProps> = ({
  visible,
  title,
  onOk,
  onCancel,
  deadline,
  setDeadline,
  content,
  setContent,
  company,
  setCompany,
}) => {
  const [form] = Form.useForm();
  const handleSubmit = () => {
    form
      .validateFields()
      .then(() => {
        onOk();
      })
      .catch((info) => {
        console.log('Validate Failed:', info);
      });
  };
  return (
    <Modal
      title={title}
      open={visible}
      onOk={handleSubmit}
      onCancel={onCancel}
      okText="提交"
      cancelText="取消"
    >
      <Form form={form} layout="horizontal" labelCol={{ span: 6 }}>
        <Form.Item
          label="整改期限"
          name="deadline"
          rules={[{ required: true, message: '整改期限不能为空' }]}
        >
          <DatePicker
            showTime
            format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择整改期限"
            value={deadline ? moment(deadline) : null}
            onChange={(value: any) => setDeadline(value ? value.format('YYYY-MM-DD HH:mm:ss') : '')}
          />
        </Form.Item>
        <Form.Item
          label="整改内容"
          name="content"
          rules={[{ required: true, message: '整改内容不能为空' }]}
        >
          <Input.TextArea
            placeholder="请输入整改内容"
            value={content}
            onChange={(e) => setContent(e.target.value)}
          />
        </Form.Item>
        <Form.Item
          label="被督办企业"
          name="company"
          rules={[{ required: true, message: '被督办企业不能为空' }]}
        >
          <Input
            placeholder="请输入被督办企业"
            value={company}
            onChange={(e) => setCompany(e.target.value)}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default SupervisionModal;
