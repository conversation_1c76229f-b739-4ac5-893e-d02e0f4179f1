import { Card, Col, Row, Empty } from 'antd';
import React from 'react';
import { levelCards, statusCards, totalCard } from '../constants';
import './StatisticsCards.less';

interface StatisticsCardsProps {
  statistics: {
    total: number;
    red: number;
    orange: number;
    yellow: number;
    blue: number;
    untreated: number;
    treated: number;
    ignored: number;
  };
  selectedCard: string;
  onCardSelect: (key: string) => void;
}

const StatisticsCards: React.FC<StatisticsCardsProps> = ({
  statistics,
  selectedCard,
  onCardSelect,
}) => {
  // 检查是否有数据
  const hasData = statistics && Object.values(statistics).some(value => value > 0);

  // 如果没有数据，显示空状态
  if (!hasData) {
    return (
      <div className="statistics-cards">
        <div className="empty-state">
          <Empty
            description="暂无数据"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        </div>
      </div>
    );
  }
  const renderCard = (card: any, size: 'large' | 'small' = 'small') => {
    const isSelected = selectedCard === card.key;
    const count = statistics[card.key as keyof typeof statistics] || 0;
    const isTotal = card.key === 'total';

    return (
      <Card
        className={`statistics-card ${size} ${isSelected ? 'selected' : ''}`}
        onClick={() => onCardSelect(card.key)}
        hoverable
        style={{
          borderColor: isSelected ? card.color : '#d9d9d9',
          backgroundColor: isSelected ? card.bgColor : '#fff',
        }}
      >
        <div className="card-content">
          {isTotal ? (
            // 预警总数卡片的特殊布局
            <>
              <div className="card-count">{count}</div>
              <div className="card-title">{card.title}</div>
            </>
          ) : (
            // 其他卡片的常规布局
            <div className="card-info">
              {card.icon && (
                <div className="card-icon" style={{ color: card.color }}>
                  {card.icon}
                </div>
              )}
              <div className="card-title" style={{ color: card.color }}>
                {card.title}
              </div>
              <div className="card-count" style={{ color: card.color }}>
                {count}个
              </div>
            </div>
          )}
        </div>
      </Card>
    );
  };

  return (
    <div className="statistics-cards">
      <Row gutter={[16, 16]} className="main-row">
        {/* 左侧：预警总数 */}
        <Col span={6}>
          <div className="total-card-wrapper">{renderCard(totalCard)}</div>
        </Col>

        {/* 右侧：颜色预警和处置状态 */}
        <Col span={18}>
          {/* 颜色统计 - 上方一行 */}
          <Row gutter={16} className="level-row" style={{ marginBottom: 8 }}>
            {levelCards.map(card => (
              <Col key={card.key} span={6}>
                {renderCard(card)}
              </Col>
            ))}
          </Row>

          {/* 处置状态 - 下方一行 */}
          <Row gutter={[16, 16]} className="status-row" style={{ paddingTop: 16 }}>
            {statusCards.map(card => (
              <Col key={card.key} span={8}>
                {renderCard(card)}
              </Col>
            ))}
          </Row>
        </Col>
      </Row>
    </div>
  );
};

export default StatisticsCards;
