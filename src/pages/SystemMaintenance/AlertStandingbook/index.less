.alert-standingbook {
  .common-page-body {
    padding: 24px;
  }

  // 确保统计卡片和列表之间有合适的间距
  .ant-card {
    margin-bottom: 8px;
  }
  .status-row {
    .ant-card {
      margin-bottom: 0;
    }
  }

  // 表格样式优化
  .ant-table {
    .ant-table-thead > tr > th {
      font-weight: 600;
      background-color: #fafafa;
    }

    .ant-table-tbody > tr:hover > td {
      background-color: #f5f5f5;
    }
  }

  // 操作按钮样式
  .ant-btn-link {
    padding: 0 8px;

    &:first-child {
      padding-left: 0;
    }
  }
}

// 详情弹窗样式
.ant-modal {
  .ant-modal-header {
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;
  }

  .ant-modal-body {
    max-height: 70vh;
    padding: 24px;
    overflow-y: auto;
  }
}

// 督办详情组件样式
.supervision-detail {
  .stage-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    gap: 12px;

    .stage-title {
      font-size: 16px;
      font-weight: 600;
      color: #262626;
    }
  }

  .item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 8px;
    line-height: 1.5;

    .label {
      font-weight: 500;
      color: #262626;
      min-width: 80px;
      margin-right: 12px;
      flex-shrink: 0;
    }

    > div:last-child {
      color: #595959;
      flex: 1;
      word-break: break-all;
    }
  }
}
