import CommonLinkButton from '@/components/BaseFormComponents/CommonLinkButton';
import FixedWidthSelect from '@/components/BaseFormComponents/FixedWidthSelect';
import ListRequest from '@/components/ListRequest';
import ModalForm from '@/components/ModalForm';
import { useAlertConfig } from '@/hook/useAlertConfig';
import { createCodeLabelMap, getOptionsFromDict } from '@/utils/commonFunction';
import useDict from '@/utils/useDict';
import { message } from 'antd';
import React, { useEffect, useState } from 'react';
import StatisticsCards from './components/StatisticsCards';
import { allCards, EditPageTitle, filterItems, HomeTitle, tableColumns } from './constants';

import HandleForm from './components/HandleForm';
import SupervisionButtons from './components/SupervisionButtons';

import PageHeader from '@/components/PageHeader';
import './index.less';

const AlertStandingbook: React.FC = () => {
  const { value: handleState } = useDict('warn_handle_state');
  const { value: warningState } = useDict('warn_warning_state');

  const [formTitle, setFormTitle] = useState(HomeTitle);
  const [detailVisible, setDetailVisible] = useState(false);
  const [columns, setColumns] = useState([...tableColumns]);
  const [formFilters, setFormFilters] = useState([...filterItems]);
  const [reportId, setReportId] = useState('');
  const [selectedCard, setSelectedCard] = useState('total');
  const [extraParams, setExtraParams] = useState<any>({});
  const [updateTrigger, setUpdateTrigger] = useState(false);
  const [openType, setOpenType] = useState<'view' | 'edit'>('view');
  const [levelId, setLevelId] = useState('');
  const [handleTitle, setHandleTitle] = useState('');
  const [handleValue, setHandleValue] = useState('');
  const [statistics, setStatistics] = useState({
    total: 860,
    red: 100,
    orange: 115,
    yellow: 215,
    blue: 330,
    untreated: 100,
    treated: 330,
    ignored: 430,
  });

  const { setAlertTypeOptions, setAlertLevelOptions } = useAlertConfig(
    formFilters,
    columns,
    setColumns,
    { typeIndex: 2, levelIndex: 3 },
  );

  const viewAlertDetail = (record: any) => {
    setLevelId(record.id);
    setOpenType('view');
    setFormTitle(EditPageTitle);
    setDetailVisible(true);
  };

  const handleCardSelect = (cardKey: string) => {
    setSelectedCard(cardKey);

    // 根据选中的卡片设置筛选参数
    const params: any = {};

    const card = allCards.find((c: any) => c.key === cardKey) as any;
    if (card) {
      if (card.level) {
        params.level = card.level;
      }
      if (card.status) {
        params.warningState = card.status;
      }
    }

    setExtraParams(params);
    setUpdateTrigger(!updateTrigger);
  };

  const viewSupervisionFlow = () => {
    // 督办流程功能，暂时用消息提示
    message.info('督办流程功能开发中...');
  };

  const setOperateColumns = () => {
    const actionColumn = {
      title: '操作',
      width: 200,
      noTooltip: true,
      render: (_text: any, record: any) => {
        return (
          <div>
            <CommonLinkButton type="link" onClick={() => viewAlertDetail(record)}>
              详情
            </CommonLinkButton>
            <CommonLinkButton type="link" onClick={() => viewSupervisionFlow()}>
              督办流程
            </CommonLinkButton>
          </div>
        );
      },
    };
    setColumns([...tableColumns, actionColumn]);
  };

  // 获取统计数据
  const fetchStatistics = async () => {
    try {
      // 这里应该调用实际的统计API
      // const res = await getAlertStatistics();
      // if (res.code === '200') {
      //   setStatistics(res.data);
      // }

      // 暂时使用模拟数据
      console.log('获取统计数据...');
    } catch (error) {
      message.error('获取统计数据失败');
    }
  };

  const updateColumnRender = (options: any[], dataIndex: string) => {
    if (options.length > 0) {
      const newColumns = [...columns];
      const column = newColumns.find(col => col.dataIndex === dataIndex);
      if (column) {
        const map = createCodeLabelMap(options);
        column.render = (code: string) => <span>{map[code] || '-'}</span>;
        setColumns(newColumns);
      }
    }
  };

  const setHandleTypeOptions = (handleOptions: any[]) => {
    formFilters[3].component = (
      <FixedWidthSelect placeholder="请选择督办状态" allowClear options={handleOptions} />
    );
    setFormFilters([...formFilters]);
  };

  const turnToListPage = () => {
    setDetailVisible(false);
    setFormTitle(HomeTitle);
  };

  const saveSupervisionInfo = () => {
    return true;
  };

  useEffect(() => {
    setFormFilters([...formFilters]);
    setOperateColumns();
    setAlertTypeOptions();
    setAlertLevelOptions();
    fetchStatistics();
  }, []);

  useEffect(() => {
    if (handleState.length > 0) {
      const handleOptions = getOptionsFromDict(handleState);
      updateColumnRender(handleOptions, 'handleState');
      setHandleTypeOptions(handleOptions);
    }
  }, [handleState]);

  useEffect(() => {
    if (warningState.length > 0) {
      const warningOptions = getOptionsFromDict(warningState);
      updateColumnRender(warningOptions, 'warningState');
    }
  }, [warningState]);

  return (
    <div className="common-page alert-standingbook">
      {detailVisible && (
        <PageHeader showBackIcon={detailVisible} title={formTitle} onClick={turnToListPage} />
      )}
      {!detailVisible && (
        <div className="common-page-body">
          {/* 统计卡片 */}
          <StatisticsCards
            statistics={statistics}
            selectedCard={selectedCard}
            onCardSelect={handleCardSelect}
          />

          {/* 数据列表 */}
          <div style={{ height: 'calc(100% - 200px)' }}>
            <ListRequest
              apiUrl={`${ALARMPREFIX}/warning/safeproduction/report/reportList`}
              pageSize={10}
              filterItems={formFilters}
              columns={columns}
              extraParams={extraParams}
              updateTrigger={updateTrigger}
              inlineButtons
              method="GET"
              buttonCol={1}
              labelCol={{ span: 6 }}
              searchButtonStyle={{ marginLeft: 8 }}
              scrollYDelta={248}
            />
          </div>
        </div>
      )}

      <ModalForm
        openType="view"
        visible={detailVisible}
        onSubmit={saveSupervisionInfo}
        formComp={<HandleForm handleState={handleState} reportId={levelId} openType="view" />}
        onClose={turnToListPage}
        showDefaultButtons={false}
        customButtons={
          <SupervisionButtons
            handleValue={handleValue}
            handleTitle={handleTitle}
            reportId={levelId}
            openType={openType}
            onCancel={turnToListPage}
          />
        }
      />
    </div>
  );
};

export default AlertStandingbook;
