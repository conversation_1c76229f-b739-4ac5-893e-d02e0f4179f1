import React, { useState, useEffect } from 'react';
import useDict from '@/utils/useDict';
import ModalForm from '@/components/ModalForm';
import { CustomButton } from '@/components/FilterForm';
import {
  getRecordInitValue,
  getOptionsFromDict,
  setFormDataFromFileUrl,
} from '@/utils/commonFunction';
import { tableColumns, filterItems } from './constant';
import RuleForm, { formItems as ruleFormItems } from './components/RuleForm';
import AuditForm, { formItems as auditFormItems } from './components/AuditForm';
import { Button, message, Select, Popconfirm, TreeSelect } from 'antd';
import FilePreview from '@/components/FilePreview';
import { saveInstitution, deleteInstitution } from '@/services/systemMaintenance/rules';
import PageHeader from '@/components/PageHeader';
import ListRequest from '@/components/ListRequest';
import { queryOrgTree } from '@/services/systemMaintenance/job';
import { TreeNode } from '@/components/CommonTissueList';

const customButtons: CustomButton[] = [
  { text: '新增', color: '#ffffff', bgColor: '#3164F6', onClick: () => {} },
];

const updateFormFilterComponent = (filterIndex: number, data: any, formFilters: any) => {
  if (data) {
    formFilters[filterIndex].component = <Select options={getOptionsFromDict(data)} />;
  }
};

let currentId = '';
let fileUrl = '';
let fileName = '';
let ifRelease = false;

const formItems = [...auditFormItems, ...ruleFormItems];

const RuleConfig: React.FC = () => {
  const { value: auditStatus } = useDict('portal_audit_type');
  const { value: releaseType } = useDict('portal_release_type');
  const { value: auditActions } = useDict('portal_audit_actions');

  const [columns, setColumns] = useState([...tableColumns]);
  const [formFilters, setFormFilters] = useState([...filterItems]);
  const [openType, setOpenType] = useState<'add' | 'edit' | 'view'>('add');
  const [updateTrigger, setUpdateTrigger] = useState(false);
  const [formTitle, setFormTitle] = useState('管理制度管理');
  const [formVisible, setFormVisible] = useState(false);
  const [initValue, setInitValue] = useState<Record<string, any>>();
  const [currentForm, setCurrentForm] = useState<any>(<></>);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [treeData, setTreeData] = useState<TreeNode[]>([]);

  const onAuditStatusChange = (_v: any, item: any) => {
    ifRelease = item.label === '审核已通过';
  };

  const getAuditForm = () => {
    return (
      <AuditForm
        onAuditStatus={onAuditStatusChange}
        auditOptions={getOptionsFromDict(auditActions)}
      />
    );
  };

  const getRuleForm = (openType: string) => {
    return (
      <RuleForm
        // @ts-ignore
        openType={openType}
        fileName={fileName}
        fileUrl={fileUrl}
        scopTreeData={treeData}
      />
    );
  };

  customButtons[0].onClick = () => {
    setOpenType('add');
    setFormVisible(true);
    setFormTitle('新增制度');
    setCurrentForm(getRuleForm('add'));
  };

  const setFileUrl = (formData: any) => {
    let url = formData.fileUrl;
    if (typeof url === 'object') {
      url = formData.fileUrl?.[0]?.response?.data?.downloadUrl || '';
    } else if (openType === 'edit') {
      url = fileUrl;
    }
    return url;
  };

  const saveRuleInfo = async (formData: any) => {
    console.log('formData: ', formData);
    const newFormData = {
      ...initValue,
      ...formData,
    };
    if (formTitle === '编辑制度' || formTitle === '新增制度') {
      newFormData.releaseScope = formData.releaseScope.join(',');
      newFormData.fileUrl = setFileUrl(formData);
    }
    if (openType === 'edit') {
      newFormData.id = currentId;
      const releaseOptions = getOptionsFromDict(releaseType) || [];
      if (ifRelease) {
        const value = releaseOptions.find(item => item.label === '已发布')?.value;
        newFormData.releaseStatus = value;
      }
    }
    const res = await saveInstitution(newFormData);
    if (res.code === '200') {
      message.success('保存成功');
      setUpdateTrigger(!updateTrigger);
      return true;
    }
    message.error(res.msg);
    return false;
  };

  const editRule = (record: any) => {
    const initValue = getRecordInitValue(record, formItems);
    const fileInfo = setFormDataFromFileUrl(record.fileUrl);
    fileUrl = fileInfo.downloadUrl || '';
    fileName = fileInfo.name || '';
    initValue.releaseScope = initValue.releaseScope.split(',');
    Reflect.deleteProperty(initValue, 'fileUrl');
    setInitValue(initValue);
    currentId = record.id;
    setOpenType('edit');
    setFormVisible(true);
    setFormTitle('编辑制度');
    setCurrentForm(getRuleForm('edit'));
  };

  const delRule = async (record: any) => {
    const res = await deleteInstitution(record.id);
    if (res.code === '200') message.success('删除成功');
    else message.error(res.msg);
    setUpdateTrigger(!updateTrigger);
  };

  const auditRule = (record: any) => {
    const initValue = getRecordInitValue(record, formItems);
    setInitValue(initValue);
    const fileInfo = setFormDataFromFileUrl(record.fileUrl);
    fileUrl = fileInfo.downloadUrl || '';
    Reflect.deleteProperty(initValue, 'fileUrl');
    Reflect.deleteProperty(initValue, 'auditStatus');
    currentId = record.id;
    setOpenType('edit');
    setFormVisible(true);
    setFormTitle('审核');
    setCurrentForm(getAuditForm());
  };

  const turnToListPage = () => {
    setFormVisible(false);
    setFormTitle('管理制度管理');
  };

  const onWithdraw = async (record: any) => {
    const initValue = getRecordInitValue(record, formItems);
    const releaseOptions = getOptionsFromDict(releaseType) || [];
    const auditOptions = getOptionsFromDict(auditStatus) || [];
    const unReleaseValue = releaseOptions.find(item => item.label === '未发布')?.value;
    const unAuditValue = auditOptions.find(item => item.label === '未审核')?.value;
    initValue.releaseStatus = unReleaseValue;
    initValue.auditStatus = unAuditValue;
    initValue.id = record.id;
    const res = await saveInstitution(initValue);
    if (res.code === '200') message.success('撤回成功');
    else message.error(res.msg);
    setUpdateTrigger(!updateTrigger);
  };

  const onPreview = (record: any) => {
    const { fileUrl: url } = record;
    fileUrl = url;
    setPreviewOpen(true);
  };

  const setOperateColumns = () => {
    const actionColumn = {
      title: '操作',
      render: (_text: any, record: any) => (
        <div>
          {record?.releaseStatus === '已发布' ? (
            <Popconfirm
              title=" "
              description="你确定要撤回该管理制度吗？"
              onConfirm={() => onWithdraw(record)}
              okText="确认"
              cancelText="取消"
            >
              <Button type="link">撤回</Button>
            </Popconfirm>
          ) : (
            <>
              <Button type="link" onClick={() => editRule(record)}>
                修改
              </Button>
              <Popconfirm
                title=" "
                description="你确定要删除该管理制度吗？"
                onConfirm={() => delRule(record)}
                okText="确认"
                cancelText="取消"
              >
                <Button type="link">删除</Button>
              </Popconfirm>
              <Button type="link" onClick={() => auditRule(record)}>
                审核
              </Button>
            </>
          )}
          <Button type="link" onClick={() => onPreview(record)}>
            预览
          </Button>
          <a href={`/storage${record.fileUrl}`} download>
            <Button type="link">下载</Button>
          </a>
        </div>
      ),
    };
    setColumns([...tableColumns, actionColumn]);
  };

  const setQueryParams = (params: any) => {
    params.releaseScope = params.releaseScope?.join?.(',') || '';
    return params;
  };

  useEffect(() => {
    updateFormFilterComponent(1, auditStatus, formFilters);
    updateFormFilterComponent(2, releaseType, formFilters);
    filterItems[3].component = (
      <TreeSelect
        showCheckedStrategy={TreeSelect.SHOW_ALL}
        treeCheckable
        allowClear
        multiple
        treeData={treeData}
      />
    );
    setFormFilters(formFilters);
    setOperateColumns();
  }, [auditStatus, releaseType, treeData]);

  useEffect(() => {
    const fetchTreeData = async () => {
      try {
        const res = await queryOrgTree();
        const transformData = (data: any[]): TreeNode[] => {
          return data.map(item => ({
            title: item.orgName,
            label: item.orgName,
            key: item.orgId,
            value: item.orgId,
            children: item.children.length ? transformData(item.children) : [],
          }));
        };
        const formattedData = transformData(res.data);
        setTreeData(formattedData);
      } catch (error) {
        message.error('树状图数据获取或者格式化失败');
      }
    };
    fetchTreeData();
  }, []);

  return (
    <div className="common-page">
      <PageHeader showBackIcon={formVisible} title={formTitle} onClick={turnToListPage} />
      {!formVisible && (
        <div className="common-page-body">
          <ListRequest
            apiUrl={`${PREFIX}/portal/backend/institutionMgr/institutionList`}
            pageSize={5}
            filterItems={formFilters}
            columns={columns}
            inlineButtons
            customButtons={customButtons}
            updateTrigger={updateTrigger}
            method="GET"
            buttonCol={3}
            labelCol={{ span: 6 }}
            searchParamsHandle={setQueryParams}
            searchButtonStyle={{ paddingRight: 0, marginRight: '-16px' }}
          />
        </div>
      )}
      <ModalForm
        openType={openType}
        visible={formVisible}
        initialValues={initValue}
        onSubmit={saveRuleInfo}
        formComp={currentForm}
        onClose={turnToListPage}
      />
      <FilePreview path={fileUrl} open={previewOpen} onClose={() => setPreviewOpen(false)} />
    </div>
  );
};

export default RuleConfig;
