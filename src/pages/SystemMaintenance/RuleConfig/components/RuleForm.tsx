import React, { useState } from 'react';
import { Form, Input, Col, TreeSelect } from 'antd';
import UploadCustom from '@/components/UploadCustom';
import OnlyReadUpload from '@/components/OnlyReadUpload';
import { TreeNode } from '@/components/CommonTissueList';

interface ModalFormProps {
  scopTreeData?: TreeNode[];
  openType?: 'edit' | 'add';
  fileName?: string;
  fileUrl?: string;
}

export const formItems = ['fileUrl', 'name', 'releaseScope'];

const RuleForm: React.FC<ModalFormProps> = ({ openType, fileName, fileUrl, scopTreeData }) => {
  const [showOnlyRead, setShowOnlyRead] = useState(openType === 'edit');

  const handleDelete = () => {
    setShowOnlyRead(false);
  };

  return (
    <>
      <Col span={8} />
      <Col span={8}>
        <Form.Item
          label="制度名称"
          labelCol={{ span: 6 }}
          name="name"
          rules={[{ required: true, message: '请输入制度名称' }]}
        >
          <Input />
        </Form.Item>
      </Col>
      <Col span={8} />
      <Col span={8} />
      <Col span={8}>
        <Form.Item
          label="发布范围"
          name="releaseScope"
          labelCol={{ span: 6 }}
          rules={[{ required: true, message: '请输入发布范围' }]}
        >
          <TreeSelect
            treeData={scopTreeData}
            showCheckedStrategy={TreeSelect.SHOW_ALL}
            treeCheckable
            allowClear
            multiple
          />
        </Form.Item>
      </Col>
      <Col span={8} />
      <Col span={8} />
      <Col span={8}>
        <Form.Item
          labelCol={{ span: 6 }}
          label="附件"
          name="fileUrl"
          rules={[{ required: true, message: '请上传附件' }]}
        >
          {showOnlyRead ? (
            <OnlyReadUpload
              fileName={fileName || ''}
              downloadUrl={fileUrl || ''}
              onDelete={handleDelete}
            />
          ) : (
            <UploadCustom type="doc" buttonTitle="上传附件" maxNum={1} />
          )}
        </Form.Item>
      </Col>
    </>
  );
};
export default RuleForm;
