import React from 'react';
import { Form, Input, Col, Select } from 'antd';

interface ModalFormProps {
  auditOptions?: any[];
  onAuditStatus?: (value: any, item: any) => void;
}

export const formItems = ['auditStatus', 'auditOpinion'];

const AuditForm: React.FC<ModalFormProps> = ({ auditOptions, onAuditStatus }) => {
  return (
    <>
      <Col span={8} />
      <Col span={8}>
        <Form.Item
          label="审核结果"
          labelCol={{ span: 6 }}
          name="auditStatus"
          rules={[{ required: true, message: '请选择审核结果' }]}
        >
          <Select onChange={onAuditStatus} placeholder="请选择审核结果" options={auditOptions} />
        </Form.Item>
      </Col>
      <Col span={8} />
      <Col span={8} />
      <Col span={8}>
        <Form.Item label="审核意见" labelCol={{ span: 6 }} name="auditOpinion">
          <Input.TextArea />
        </Form.Item>
      </Col>
    </>
  );
};
export default AuditForm;
