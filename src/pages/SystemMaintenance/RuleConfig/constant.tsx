import React from 'react';
import { Input } from 'antd';

export const filterItems = [
  {
    name: 'name',
    label: '制度名称',
    component: <Input placeholder="请输入" />,
    span: 5,
  },
  {
    name: 'auditStatus',
    label: '审核状态',
    component: <Input placeholder="请输入" />,
    span: 5,
  },
  {
    name: 'releaseStatus',
    label: '状态',
    component: <Input placeholder="请输入" />,
    span: 5,
  },
  {
    name: 'releaseScope',
    label: '发布范围',
    component: <Input placeholder="请输入" />,
    span: 5,
    labelCol: { span: 8 },
  },
];

export const tableColumns: any[] = [
  { title: '序号', dataIndex: 'rowId', width: 80 },
  { title: '制度名称', dataIndex: 'name', ellipsis: true },
  { title: '发布范围', dataIndex: 'releaseScopeName', width: 150, ellipsis: true },
  { title: '审核状态', dataIndex: 'auditStatus', width: 150 },
  { title: '发布状态', dataIndex: 'releaseStatus', width: 120 },
  { title: '发布时间', dataIndex: 'updateTime' },
];
