import { useDeepCompareEffect } from '@ant-design/pro-components';
import { history } from '@umijs/max';
import { getRuleList } from '@/services/systemMaintenance/rules';
import { Button, ConfigProvider, Empty, message, Pagination } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import React, { useState } from 'react';
import styles from './index.less';

const FrontRuleConfig: React.FC<any> = (props) => {
  const { params } = props;
  const [searchParams, setSearchParams] = useState<{
    pageNo: number;
    pageSize: number;
  }>({ pageNo: 1, pageSize: 10 });
  const [list, setList] = useState<any[]>([]);
  const [total, setTotal] = useState<any>(0);

  const queryRuleList = async () => {
    // @ts-ignore
    const { orgId } = window.userInfo;
    if (orgId) {
      const res = await getRuleList(Object.assign({}, searchParams, { releaseScope: orgId }));
      setList(res.data.data || []);
      setTotal(res.data.totalRecords);
    } else message.warning('未查到当前用户所属组织');
  };

  useDeepCompareEffect(() => {
    queryRuleList();
  }, [params, searchParams]);

  return (
    <div className="homeColumn-articleList">
      <div className="list-box">
        {total > 0 ? (
          list.map((item) => {
            return (
              <div
                className={`list-item ${Number(item.readAlready || '0') > 0 && 'readed'}`}
                key={item.id}
              >
                <div
                  className="item-title"
                  onClick={() =>
                    history.push(
                      `/homePreviewPage?mid=${
                        params.columnId
                      }&type=rule&fileUrl=${encodeURIComponent(item.fileUrl)}`,
                    )
                  }
                >
                  <div className="ellipsis">{item.name}</div>
                </div>
                <div className="item-desc ellipsis-multiline" />
                <div className="item-bottom">
                  <div className="item-time">{item.updateTime}</div>
                  <div className={styles.btnContainer}>
                    <div
                      className="item-more"
                      onClick={() =>
                        history.push(
                          `/homePreviewPage?mid=${
                            params.columnId
                          }&type=rule&fileUrl=${encodeURIComponent(item.fileUrl)}`,
                        )
                      }
                    >
                      <span>查看</span>
                    </div>
                    <a
                      className="item-more"
                      href={`/storage${item.fileUrl}`}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <Button className="item-more" type="link" disabled={!item.fileUrl}>
                        下载
                      </Button>
                    </a>
                  </div>
                </div>
              </div>
            );
          })
        ) : (
          <Empty description="暂无内容" />
        )}
      </div>
      <div className="pagination-box">
        <ConfigProvider locale={zhCN}>
          <Pagination
            current={searchParams.pageNo}
            pageSize={searchParams.pageSize}
            total={total}
            showQuickJumper
            onChange={(page, pageSize) => {
              setSearchParams({
                pageNo: page,
                pageSize,
              });
              window.scrollTo({
                top: 0,
                behavior: 'smooth', // 为滚动添加动画效果
              });
            }}
          />
        </ConfigProvider>
      </div>
    </div>
  );
};

export default FrontRuleConfig;
