import React from 'react';

export const HomeTitle = '预警分级';

export const EditPageTitle = '预警分级编辑';

export const PolicyPageTitle = '预警策略配置';

export const tableColumns: any[] = [
  { title: '序号', dataIndex: 'rowId', width: 80 },
  { title: '等级名称', dataIndex: 'name', ellipsis: true, width: 250 },
  {
    title: '等级颜色',
    dataIndex: 'color',
    width: 150,
    render: (color: string) => (
      <div
        style={{
          width: '20px',
          height: '20px',
          backgroundColor: color,
          borderRadius: '3px',
        }}
      />
    ),
  },
  { title: '分级描述', dataIndex: 'description' },
];
