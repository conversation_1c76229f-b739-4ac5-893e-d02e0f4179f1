// 用户组输入框样式
.user-group-input {
  cursor: pointer;

  &:hover {
    border-color: #40a9ff;
  }
}

.user-group-icon {
  color: #1890ff;
}

// 弹窗样式
.modal-container {
  display: flex;
  height: 400px;
}

.tree-panel {
  flex: 1;
  padding: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;

  &.left-panel {
    margin-right: 8px;
  }

  &.right-panel {
    margin-left: 8px;
  }
}

.panel-header {
  margin-bottom: 8px;
  padding: 8px 12px;
  font-weight: bold;
  background: #f5f5f5;
  border-radius: 4px;
}

.panel-content {
  height: calc(100% - 40px);
  overflow-y: auto;
}

// 已选择树形样式
.selected-tree {
  :global(.ant-tree-node-content-wrapper) {
    display: flex !important;
    align-items: center !important;
    width: 100% !important;
  }

  :global(.ant-tree-title) {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    width: 100% !important;
  }

  :global(.ant-tree-treenode) {
    position: relative;
  }
}

.tree-node-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  min-height: 24px;
}

.tree-node-title {
  flex: 1;
  padding-right: 8px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.tree-node-actions {
  flex-shrink: 0;
  width: 50px;
  text-align: right;
}

.delete-button {
  min-width: 40px;
  height: 22px;
  padding: 0;
  color: #1890ff;
  font-size: 12px;
  line-height: 22px;
}

// 定时任务表达式输入框
.cron-input {
  width: 100%;
}
