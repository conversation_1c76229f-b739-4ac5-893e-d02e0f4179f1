import React, { useState } from 'react';
import { Form, Input, Col, ColorPicker } from 'antd';
import { Color } from 'antd/es/color-picker';
import { nameLengthRules, textAreaLengthRules } from '@/utils/form';

export const formItems = ['color', 'name', 'description'];

const AlertLevelForm: React.FC = () => {
  const [color, setColor] = useState<string>(''); // 保存选择的颜色

  // 处理颜色变化
  const handleColorChange = (value: Color) => {
    // 获取 hex 值，并更新颜色
    setColor(value.toHexString() || '');
  };

  return (
    <>
      <Col span={8}>
        <Form.Item
          label="等级名称"
          labelCol={{ span: 8 }}
          name="name"
          rules={[{ required: true, message: '请输入等级名称' }, nameLengthRules]}
        >
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          label="等级颜色"
          name="color"
          labelCol={{ span: 8 }}
          rules={[{ required: true, message: '请选择等级颜色' }]}
        >
          <ColorPicker value={color} onChange={v => handleColorChange(v)} />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          label="分级描述"
          name="description"
          labelCol={{ span: 8 }}
          rules={[{ required: true, message: '请输入分级描述' }, textAreaLengthRules]}
        >
          <Input.TextArea />
        </Form.Item>
      </Col>
    </>
  );
};
export default AlertLevelForm;
