import useOptionsFromDict from '@/hook/useOptionsFromDict';
import { queryAlertPolicyDetail } from '@/services/systemMaintenance/alert';
import { getRecordInitValue } from '@/utils/commonFunction';
import { TeamOutlined } from '@ant-design/icons';
import {
  Button,
  Col,
  Form,
  FormInstance,
  Input,
  Modal,
  Radio,
  Select,
  Spin,
  Tree,
  message,
} from 'antd';
import parser from 'cron-parser';
import React, { useEffect, useState } from 'react';
import styles from './PolicyConfig.less';

interface PolicyConfigFormProps {
  treeData: any[];
  levelId?: string;
  levelName?: string;
  form?: FormInstance<any>;
}

export const formItems = [
  'orgId',
  'ishandle',
  'notificationMode',
  'id',
  'orgName',
  'isEnabled',
  'pushFrequency',
  'cronExpression',
  'pushEmergencyPersonnel',
  'levelName',
];

const PolicyConfigForm: React.FC<PolicyConfigFormProps> = ({
  form,
  treeData,
  levelId,
  levelName,
}) => {
  const [renderData, setRenderData] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [id, setId] = useState('');
  const [pushFrequency, setPushFrequency] = useState('immediate');
  const [showUserGroupModal, setShowUserGroupModal] = useState(false);
  const [selectedUserGroups, setSelectedUserGroups] = useState<any[]>([]);
  // 当前选中确认的用户组节点的keys
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [checkedKeys, setCheckedKeys] = useState<string[]>([]);
  const methodOptions = useOptionsFromDict('warn_notify_method');

  // cron表达式验证函数
  const validateCronExpression = (_: any, value: string) => {
    if (!value) {
      return Promise.resolve();
    }
    try {
      parser.parseExpression(value);
      return Promise.resolve();
    } catch (error) {
      return Promise.reject(new Error('请输入有效的定时任务表达式'));
    }
  };

  // 处理推送频率变化
  const handlePushFrequencyChange = (value: string) => {
    setPushFrequency(value);
    if (value === 'immediate') {
      form?.setFieldsValue({ cronExpression: '' });
    }
  };

  // 从树形数据中获取选中的组织
  const getSelectedGroupsFromTree = (treeData: any[], keys: string[]): any[] => {
    const result: any[] = [];
    const traverse = (nodes: any[]) => {
      nodes.forEach(node => {
        if (keys.includes(node.key)) {
          result.push(node);
        }
        if (node.children && node.children.length > 0) {
          traverse(node.children);
        }
      });
    };
    traverse(treeData);
    return result;
  };

  // 检查子节点中是否有被选中的
  const hasAnySelectedInChildren = (children: any[], selectedKeys: string[]): boolean => {
    return children.some(child => {
      if (selectedKeys.includes(child.key)) {
        return true;
      }
      if (child.children && child.children.length > 0) {
        return hasAnySelectedInChildren(child.children, selectedKeys);
      }
      return false;
    });
  };

  // 构建已选择的树形数据
  const buildSelectedTreeData = (treeData: any[], selectedKeys: string[]): any[] => {
    const buildTree = (nodes: any[]): any[] => {
      return nodes
        .map(node => {
          const hasSelectedChildren =
            node.children &&
            node.children.length > 0 &&
            hasAnySelectedInChildren(node.children, selectedKeys);
          const isSelected = selectedKeys.includes(node.key);

          if (isSelected || hasSelectedChildren) {
            return {
              ...node,
              children: node.children && node.children.length > 0 ? buildTree(node.children) : [],
            };
          }
          return null;
        })
        .filter(Boolean);
    };

    return buildTree(treeData);
  };

  // 处理用户组选择,并初始化当前已选择的key
  const handleUserGroupSelect = () => {
    setCheckedKeys(selectedKeys);
    setShowUserGroupModal(true);
  };

  // 确认用户组选择
  const handleUserGroupConfirm = () => {
    const selectedGroups = getSelectedGroupsFromTree(renderData, checkedKeys);
    setSelectedKeys(checkedKeys);
    setSelectedUserGroups(selectedGroups);
    const orgIds = checkedKeys;
    const orgNames = selectedGroups.map(group => group.title).join(',');
    form?.setFieldsValue({
      orgId: orgIds,
      orgName: orgNames,
    });
    setShowUserGroupModal(false);
  };

  // 递归收集节点及其所有子节点的key
  const collectNodeAndChildrenKeys = (node: any, keyField: string = 'key'): string[] => {
    let keys: string[] = [];

    // 添加当前节点key
    if (node && node[keyField] !== undefined) {
      keys.push(node[keyField]);
    }

    // 递归添加所有子节点key
    if (node && node.children && node.children.length > 0) {
      node.children.forEach((child: any) => {
        keys = keys.concat(collectNodeAndChildrenKeys(child, keyField));
      });
    }

    return keys;
  };

  // 查找指定key的节点及其所有子节点
  const findNodeAndChildrenByKey = (treeData: any[], targetKey: string): any[] => {
    let result: any[] = [];

    const traverse = (nodes: any[]) => {
      nodes.forEach(node => {
        if (node.key === targetKey) {
          result = collectNodeAndChildrenKeys(node);
        }
        if (node.children && node.children.length > 0) {
          traverse(node.children);
        }
      });
    };

    traverse(treeData);
    return result;
  };

  // 查找节点的父节点
  const findParentNode = (treeData: any[], targetKey: string, parent: any = null): any => {
    for (const node of treeData) {
      if (node.key === targetKey) {
        return parent;
      }
      if (node.children && node.children.length > 0) {
        const found = findParentNode(node.children, targetKey, node);
        if (found !== null) {
          return found;
        }
      }
    }
    return null;
  };

  // 检查父节点是否应该被取消选中（当所有子节点都被删除时）
  const getParentsToUncheck = (
    treeData: any[],
    keysToDelete: string[],
    currentCheckedKeys: string[],
  ): string[] => {
    const parentsToUncheck: string[] = [];
    const processedParents = new Set<string>();

    // 获取删除后剩余的选中keys
    const remainingKeys = currentCheckedKeys.filter(k => !keysToDelete.includes(k));

    // 递归检查父节点是否应该被取消选中
    const checkParentNode = (nodeKey: string) => {
      const parent = findParentNode(treeData, nodeKey);
      if (parent && parent.children && !processedParents.has(parent.key)) {
        processedParents.add(parent.key);

        // 检查父节点的所有子节点是否都将被删除或取消选中
        const allChildrenKeys = parent.children.map((child: any) => child.key);

        // 如果父节点其中一个或者多个节点没有选中，它应该被取消选中
        const parentsUncheck = allChildrenKeys?.length === remainingKeys?.length - 1;

        // 如果父节点没有剩余的选中子节点，且父节点当前是选中状态，则应该取消选中
        if (!parentsUncheck && currentCheckedKeys.includes(parent.key)) {
          parentsToUncheck.push(parent.key);
          // 递归检查祖父节点
          checkParentNode(parent.key);
        }
      }
    };

    // 检查每个要删除的节点的父节点
    keysToDelete.forEach(deletedKey => {
      checkParentNode(deletedKey);
    });

    return parentsToUncheck;
  };

  // 删除已选择的用户组（包括子节点）
  const handleRemoveUserGroup = (key: string) => {
    // 查找要删除的节点及其所有子节点
    const keysToDelete = findNodeAndChildrenByKey(renderData, key);

    // 查找需要取消选中的父节点
    const parentsToUncheck = getParentsToUncheck(renderData, keysToDelete, checkedKeys);

    // 合并要删除的节点和需要取消选中的父节点
    const allKeysToRemove = [...keysToDelete, ...parentsToUncheck];

    // 从已选择的keys中过滤掉要删除的节点及其子节点和相关父节点
    const newCheckedKeys = checkedKeys.filter(k => !allKeysToRemove.includes(k));
    setCheckedKeys(newCheckedKeys);

    // 更新已选择的用户组
    const newSelectedGroups = getSelectedGroupsFromTree(renderData, newCheckedKeys);
    setSelectedUserGroups(newSelectedGroups);

    // const orgNames = newSelectedGroups.map(group => group.title).join(',');
    // form?.setFieldsValue({
    //   orgId: newCheckedKeys,
    //   orgName: orgNames,
    // });
  };

  useEffect(() => {
    const transformData = (data: any[]): any[] => {
      return data.map(item => ({
        title: item.orgName,
        key: item.orgId,
        value: item.orgId,
        children: item.children.length ? transformData(item.children) : [],
      }));
    };
    const formattedData = transformData(treeData);
    setRenderData(formattedData);
  }, [treeData]);

  const getPolicyDetail = async (id: string) => {
    const res = await queryAlertPolicyDetail(id);
    setTimeout(() => {
      setLoading(false);
    });
    if (res.code === '200') {
      const { data } = res;
      if (data !== null) {
        const initValues = getRecordInitValue(data, formItems);
        initValues.orgId = data.orgId ? data.orgId.split(',') : [];
        initValues.notificationMode = data.notificationMode ? data.notificationMode.split(',') : [];

        // 设置推送频率相关状态
        setPushFrequency(data.pushFrequency || 'immediate');

        // 设置用户组相关状态
        if (data.orgId) {
          const orgIds = data.orgId.split(',');
          setCheckedKeys(orgIds);
          const selectedGroups = getSelectedGroupsFromTree(renderData, orgIds);
          setSelectedUserGroups(selectedGroups);
        }

        setId(data.id);
        form?.setFieldsValue(initValues);
      }
    } else {
      message.error(res.msg);
    }
  };

  useEffect(() => {
    if (levelId) getPolicyDetail(levelId);
  }, [levelId]);

  return (
    <>
      {loading ? (
        <>
          <Col span={12} />
          <Spin tip="加载中..." />
          <Col span={8} />
        </>
      ) : (
        <>
          <Col span={8}>
            <Form.Item
              label="预警级别"
              name="name"
              labelCol={{ span: 6 }}
              initialValue={levelName}
            >
              <Input disabled />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="是否督办"
              name="ishandle"
              labelCol={{ span: 6 }}
              initialValue="0"
              rules={[{ required: true, message: '请选择是否督办' }]}
            >
              <Radio.Group>
                <Radio value="1">是</Radio>
                <Radio value="0">否</Radio>
              </Radio.Group>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="是否启用"
              name="isEnabled"
              labelCol={{ span: 6 }}
              initialValue="1"
              rules={[{ required: true, message: '请选择是否启用' }]}
            >
              <Radio.Group>
                <Radio value="1">是</Radio>
                <Radio value="0">否</Radio>
              </Radio.Group>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="推送频率"
              name="pushFrequency"
              labelCol={{ span: 6 }}
              initialValue="immediate"
              rules={[{ required: true, message: '请选择推送频率' }]}
            >
              <Select onChange={handlePushFrequencyChange}>
                <Select.Option value="immediate">立即推送</Select.Option>
                <Select.Option value="custom">自定义</Select.Option>
              </Select>
            </Form.Item>
          </Col>
          {pushFrequency === 'custom' && (
            <Col span={16}>
              <Form.Item
                label="定时任务表达式"
                name="cronExpression"
                labelCol={{ span: 3 }}
                initialValue="0 0 9 * * ?"
                rules={[
                  { required: true, message: '请输入定时任务表达式' },
                  { validator: validateCronExpression },
                ]}
                extra={
                  <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                    <div>格式：秒 分 时 日 月 周</div>
                    <div>示例：</div>
                    <div>• 0 0 9 * * ? (每天上午9点)</div>
                    <div>• 0 30 8 * * ? (每天上午8点30分)</div>
                    <div>• 0 0 9 * * 1-5 (周一到周五上午9点)</div>
                    <div>• 0 0 9,18 * * ? (每天上午9点和下午6点)</div>
                  </div>
                }
              >
                <Input
                  placeholder="格式：秒 分 时 日 月 周，如：0 0 9 * * ?"
                  className={styles['cron-input']}
                />
              </Form.Item>
            </Col>
          )}
          <Col span={24}>
            <Form.Item
              label="推送用户组"
              name="orgName"
              labelCol={{ span: 2 }}
              rules={[{ required: true, message: '请选择推送用户组' }]}
            >
              <Input
                placeholder="请选择推送用户组"
                value={selectedUserGroups.map(group => group.title).join(', ')}
                readOnly
                onClick={handleUserGroupSelect}
                className={styles['user-group-input']}
                suffix={<TeamOutlined className={styles['user-group-icon']} />}
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label={
                <div style={{ lineHeight: '1.2' }}>
                  是否推送
                  <br />
                  应急值守人员
                </div>
              }
              name="pushEmergencyPersonnel"
              labelCol={{ span: 6 }}
              initialValue="0"
              rules={[{ required: true, message: '请选择是否推送应急值守人员' }]}
            >
              <Radio.Group>
                <Radio value="1">是</Radio>
                <Radio value="0">否</Radio>
              </Radio.Group>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="推送方式"
              name="notificationMode"
              labelCol={{ span: 6 }}
              initialValue={['0']}
              rules={[{ required: true, message: '请选择推送方式' }]}
            >
              <Select options={methodOptions} allowClear mode="multiple" />
            </Form.Item>
          </Col>
          <Form.Item name="orgId" />
          {/* 修改状态下需要给后端传 ID */}
          {id !== '' && <Form.Item name="id" />}
        </>
      )}

      {/* 用户组选择弹窗 */}
      <Modal
        title="推送用户组"
        open={showUserGroupModal}
        onOk={handleUserGroupConfirm}
        onCancel={() => setShowUserGroupModal(false)}
        width={800}
        okText="确定"
        cancelText="取消"
      >
        <div className={styles['modal-container']}>
          {/* 左侧：组织机构树 */}
          <div className={`${styles['tree-panel']} ${styles['left-panel']}`}>
            <div className={styles['panel-header']}>组织机构</div>
            <div className={styles['panel-content']}>
              <Tree
                treeData={renderData}
                checkable
                checkedKeys={checkedKeys}
                onCheck={(checked: any, e: any) => {
                  let newCheckedKeys = Array.isArray(checked) ? checked : checked.checked;

                  // 如果是取消勾选某个节点
                  if (e.checked === false) {
                    // 获取当前节点的所有父节点
                    const getParentKeys = (nodeKey: string): string[] => {
                      const parents: string[] = [];
                      const findParent = (
                        nodes: any[],
                        childKey: string,
                        parentKey: string | null,
                      ) => {
                        nodes.forEach(node => {
                          if (node.key === childKey && parentKey) {
                            parents.push(parentKey);
                            // 继续向上查找父节点
                            findParent(renderData, parentKey, null);
                          } else if (node.children && node.children.length > 0) {
                            findParent(node.children, childKey, node.key);
                          }
                        });
                      };
                      findParent(renderData, nodeKey, null);
                      return parents;
                    };

                    // 获取所有需要取消勾选的父节点
                    const parentKeys = getParentKeys(e.node.key);
                    newCheckedKeys = newCheckedKeys.filter(
                      (key: string) => !parentKeys.includes(key),
                    );
                  } else if (e.checked === true) {
                    // 如果是勾选某个节点，检查其父节点是否应该被勾选
                    const checkParentNodes = (nodeKey: string) => {
                      const findParentAndCheck = (nodes: any[], childKey: string) => {
                        nodes.forEach(node => {
                          if (
                            node.children &&
                            node.children.some((child: any) => child.key === childKey)
                          ) {
                            // 检查是否所有兄弟节点都被选中
                            const allSiblingsChecked = node.children.every((child: any) =>
                              newCheckedKeys.includes(child.key),
                            );

                            if (allSiblingsChecked && !newCheckedKeys.includes(node.key)) {
                              newCheckedKeys.push(node.key);
                              // 递归检查上层父节点
                              checkParentNodes(node.key);
                            }
                          } else if (node.children && node.children.length > 0) {
                            findParentAndCheck(node.children, childKey);
                          }
                        });
                      };
                      findParentAndCheck(renderData, nodeKey);
                    };

                    checkParentNodes(e.node.key);
                  }

                  setCheckedKeys(newCheckedKeys);
                }}
                defaultExpandAll
                selectable={false}
              />
            </div>
          </div>

          {/* 右侧：已选择 */}
          <div className={`${styles['tree-panel']} ${styles['right-panel']}`}>
            <div className={styles['panel-header']}>已选择</div>
            <div className={styles['panel-content']}>
              <Tree
                className={styles['selected-tree']}
                treeData={buildSelectedTreeData(renderData, checkedKeys)}
                defaultExpandAll
                blockNode
                titleRender={nodeData => (
                  <div className={styles['tree-node-content']}>
                    <span className={styles['tree-node-title']}>{nodeData.title}</span>
                    <div className={styles['tree-node-actions']}>
                      {checkedKeys.includes(nodeData.key) && (
                        <Button
                          type="link"
                          size="small"
                          onClick={e => {
                            e.stopPropagation();
                            handleRemoveUserGroup(nodeData.key);
                          }}
                          className={styles['delete-button']}
                        >
                          删除
                        </Button>
                      )}
                    </div>
                  </div>
                )}
              />
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
};
export default PolicyConfigForm;
