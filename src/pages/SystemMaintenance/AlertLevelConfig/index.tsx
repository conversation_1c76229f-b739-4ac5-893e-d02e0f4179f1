import React, { useState, useEffect } from 'react';
import PageHeader from '@/components/PageHeader';
import ListRequest from '@/components/ListRequest';
import { EditPageTitle, HomeTitle, tableColumns, PolicyPageTitle } from './constants';
import { CustomButton } from '@/components/FilterForm';
import { message, Popconfirm } from 'antd';
import ModalForm from '@/components/ModalForm';
import { queryOrgTree } from '@/services/systemMaintenance/job';
import AlertLevelForm, { formItems } from './components/AlertLevelForm';
import PolicyConfigForm from './components/PolicyConfig';
import { ColumnType } from '@/components/RequestTable';
import { getRecordInitValue } from '@/utils/commonFunction';
import {
  deleteAlertLevelInfo,
  saveAlertLevelInfo,
  saveAlertPolicyDetail,
} from '@/services/systemMaintenance/alert';
import CommonLinkButton from '@/components/BaseFormComponents/CommonLinkButton';

const customButtons: CustomButton[] = [
  { text: '新增', color: '#ffffff', bgColor: '#3164F6', onClick: () => {} },
];

const AlertLevelConfig: React.FC = () => {
  const [formTitle, setFormTitle] = useState(HomeTitle);
  const [formVisible, setFormVisible] = useState(false);
  const [columns, setColumns] = useState<ColumnType<any>[]>([...tableColumns]);
  const [openType, setOpenType] = useState<'add' | 'edit'>('add');
  const [initValue, setInitValue] = useState<Record<string, any>>();
  const [currentForm, setCurrentForm] = useState<any>(<></>);
  const [treeData, setTreeData] = useState<any[]>([]);
  const [levelId, setLevelId] = useState('');
  const [updateTrigger, setUpdateTrigger] = useState(false);

  customButtons[0].onClick = () => {
    setOpenType('add');
    setFormVisible(true);
    setFormTitle(EditPageTitle);
    setCurrentForm(<AlertLevelForm />);
  };

  const editAlterType = (record: any) => {
    const initValue = getRecordInitValue(record, formItems);
    setInitValue(initValue);
    setLevelId(record.id);
    setOpenType('edit');
    setFormTitle(EditPageTitle);
    setFormVisible(true);
    setCurrentForm(<AlertLevelForm />);
  };

  const delAlterType = async (record: any) => {
    const res = await deleteAlertLevelInfo(record.id);
    if (res.code === '200') {
      message.success('删除成功！');
      setUpdateTrigger(!updateTrigger);
    } else {
      message.error(res.msg);
    }
  };

  const savePolicyInfo = async (formData: any) => {
    const newData = { ...formData, levelId };
    newData.orgId = newData.orgId.join(',');
    newData.notificationMode = newData.notificationMode.join(',');
    const res = await saveAlertPolicyDetail(newData);
    if (res.code === '200') {
      message.success('保存策略成功！');
      return true;
    }
    message.error(res.msg);
    return false;
  };

  const saveAlertType = async (formData: any) => {
    if (formTitle === PolicyPageTitle) {
      const checkRes = await savePolicyInfo(formData);
      return checkRes;
    }

    const newData = { ...formData };
    const extraInfo: Record<string, string> = {};
    if (openType === 'edit') extraInfo.id = levelId;
    if (typeof formData.color === 'object') {
      newData.color = formData.color.toHexString();
    }
    const res = await saveAlertLevelInfo(Object.assign(newData, extraInfo));
    if (res.code === '200') {
      message.success('保存成功！');
      setUpdateTrigger(!updateTrigger);
      return true;
    }
    message.error(res.msg);

    return false;
  };

  const policyConfigSet = (record: any) => {
    setLevelId(record.id);
    setFormTitle(PolicyPageTitle);
    setFormVisible(true);
    setCurrentForm(<PolicyConfigForm levelId={record.id} levelName={record.name} treeData={treeData} />);
  };

  const setOperateColumns = () => {
    const actionColumn: ColumnType<any> = {
      title: '操作',
      width: 280,
      noTooltip: true,
      render: (_text: any, record: any) => (
        <div>
          <CommonLinkButton onClick={() => editAlterType(record)}>修改</CommonLinkButton>
          <Popconfirm
            title=" "
            description="你确定要删除该预警分级吗？"
            onConfirm={() => delAlterType(record)}
            okText="确认"
            cancelText="取消"
          >
            <CommonLinkButton>删除</CommonLinkButton>
          </Popconfirm>
          <CommonLinkButton onClick={() => policyConfigSet(record)}>策略配置</CommonLinkButton>
        </div>
      ),
    };
    setColumns([...tableColumns, actionColumn]);
  };

  const turnToListPage = () => {
    setFormVisible(false);
    setFormTitle(HomeTitle);
  };

  useEffect(() => {
    setOperateColumns();
  }, [treeData, levelId, updateTrigger]);

  useEffect(() => {
    const fetchTreeData = async () => {
      try {
        const res = await queryOrgTree();
        setTreeData(res.data);
      } catch (error) {
        message.error('组织数据获取或者格式化失败');
      }
    };
    fetchTreeData();
  }, []);

  return (
    <div className="common-page">
      <PageHeader showBackIcon={formVisible} title={formTitle} onClick={turnToListPage} />
      {!formVisible && (
        <div className="common-page-body">
          <ListRequest
            apiUrl={`${ALARMPREFIX}/warning/safeproduction/scale/scaleList`}
            pageSize={10}
            filterItems={[]}
            hideActionButtons
            updateTrigger={updateTrigger}
            columns={columns}
            customButtons={customButtons}
            inlineButtons
            method="GET"
            labelCol={{ span: 8 }}
            scrollYDelta={48}
          />
        </div>
      )}
      <ModalForm
        openType={openType}
        visible={formVisible}
        initialValues={initValue}
        onSubmit={saveAlertType}
        formComp={currentForm}
        onClose={turnToListPage}
      />
    </div>
  );
};

export default AlertLevelConfig;
