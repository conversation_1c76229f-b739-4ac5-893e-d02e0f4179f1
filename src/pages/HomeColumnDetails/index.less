.column-details {
  position: relative;

  .column-header {
    width: 100%;
    height: 180px;
    padding-right: 8.3%;
    padding-left: 8.3%;
    background: url('../../assets/images/home/<USER>') no-repeat center;
    background-size: 100% 100%;
  }

  .column-banner {
    height: 250px;
    background: url('../../assets/images/home/<USER>') no-repeat center;
    background-size: 100% 100%;
  }

  .column-wrapper {
    background: #f0f2f5;

    .column-inner {
      width: 1340px;
      margin: 0 auto;
      padding-bottom: 40px;

      .column-breadcrumb {
        display: flex;
        align-items: center;
        height: 68px;
        color: #666666;
        font-weight: 400;
        font-size: 20px;
        font-family: PingFangSC-Regular;

        img {
          margin-right: 5px;
        }

        .breadcrumb-text {
          padding: 0 5px;

          &:not(:last-child) {
            cursor: pointer;

            &:hover {
              color: #407add;
            }
          }
        }
      }

      .column-main {
        background: #fff;

        .top-line {
          width: 100%;
          height: 4px;
          background: #0076d7;
          border-radius: 4px;
        }

        .detail-box {
          width: 1000px;
          margin: 0 auto;
          padding: 60px 0 200px 0;

          .detail-title {
            color: #333333;
            font-weight: 500;
            font-size: 32px;
            font-family: PingFangSC-Medium;
            text-align: center;
          }

          .detail-time {
            margin-top: 40px;
            padding-bottom: 12px;
            color: #999999;
            font-weight: 500;
            font-size: 20px;
            font-family: PingFangSC-Medium;
            text-align: center;
            border-bottom: 1px #f0f2f5 solid;
          }

          .detail-content {
            margin-top: 40px;
            padding: 0 40px;
            color: #666666;
            font-weight: 400;
            font-size: 20px;
            font-family: PingFangSC-Regular;
            line-height: 40px;
          }
        }
      }
    }
  }
}
