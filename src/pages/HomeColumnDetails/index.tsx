import { infoPublishDetail, saveInfoPublishRead } from '@/services/home/<USER>';
import { getColumnList } from '@/services/systemMaintenance/module';
import { RightOutlined } from '@ant-design/icons';
import { history } from '@umijs/max';
import { IDomEditor } from '@wangeditor/editor';
import { Editor } from '@wangeditor/editor-for-react';
import '@wangeditor/editor/dist/css/style.css';
import React, { useEffect, useState } from 'react';
import { useLocation } from 'umi';
import weizhi from '../../assets/images/home/<USER>';
import './index.less';

const ColumnDetails: React.FC<any> = props => {
  const location = useLocation();
  const query = new URLSearchParams(location.search);
  const mid = query.get('mid') || ''; // 模块id
  const tid = query.get('tid') || ''; // 文章id
  const [moduleTypeMap, setModuleTypeMap] = useState<any>({});
  const [details, setDetails] = useState<any>({});

  const getModuleList = async () => {
    const res = await getColumnList({ pageSize: 9999, pageIndex: 1 });
    if (res.code === '200') {
      const list = res.data?.data || [];
      const obj: any = {};
      list.forEach((item: any) => {
        obj[item.columnId] = item;
      });
      setModuleTypeMap(obj);
    }
  };

  async function readText() {
    saveInfoPublishRead({ textId: tid });
  }

  const getDetail = () => {
    infoPublishDetail({ textId: tid, orgId: userInfo.orgId }).then(res => {
      if (res.code === '200') {
        if (Number(res?.data?.readAlready || '0') === 0) {
          readText();
        }
        setDetails(res.data || {});
      }
    });
  };

  useEffect(() => {
    getModuleList();
  }, []);

  useEffect(() => {
    tid && getDetail();
  }, [tid]);

  console.log('moduleTypeMap', moduleTypeMap);

  return (
    <div className="column-details">
      {/* <div className="column-header">
        <HomeHeader from="column" />
      </div>

      <div className="column-banner"> </div> */}

      <div className="column-wrapper">
        <div className="column-inner">
          <div className="column-breadcrumb">
            <img src={weizhi} alt="" />
            <span
              className="breadcrumb-text"
              style={{ cursor: 'pointer' }}
              onClick={() => history.push('/home-index')}
            >
              首页
            </span>
            <RightOutlined />
            <span
              className="breadcrumb-text"
              style={{ cursor: 'pointer' }}
              onClick={() => history.push(`/column?id=${mid}`)}
            >
              {moduleTypeMap[mid]?.columnName}
            </span>
            <RightOutlined />
            <span className="breadcrumb-text">详情</span>
          </div>

          <div className="column-main">
            <div className="top-line"> </div>
            <div className="detail-box">
              <div className="detail-title">{details.textTitle}</div>
              <div className="detail-time">{details.textModifyTime}</div>
              <div className="detail-content">
                <Editor
                  defaultConfig={{}}
                  value={details.textContent}
                  onCreated={(editor: IDomEditor) => {
                    editor.disable();
                  }}
                  mode="default"
                  // style={{ height: 300 }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* <HomeFooter /> */}
    </div>
  );
};

export default ColumnDetails;
