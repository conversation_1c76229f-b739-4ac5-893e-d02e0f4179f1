import logo from '@/assets/images/login/logo_da.png';
import logoTitle from '@/assets/images/login/logo_xiao.png';
import passwd from '@/assets/images/login/passwd.png';
import boxImg from '@/assets/images/login/tupian.png';
import user from '@/assets/images/login/user.png';
import { loginByPasswod } from '@/services/login';
import { history } from '@umijs/max';
import { Button, Form, Input, message } from 'antd';
import queryString from 'query-string';
import React, { useEffect, useState } from 'react';
import './index.less';

const FormItem = Form.Item;
const Login: React.FC = () => {
  const [form] = Form.useForm();
  const [loginLoading, setLoginLoading] = useState(false);

  const handleSubmit = () => {
    form.validateFields().then(async (values: any) => {
      setLoginLoading(true);
      if (values.remember) {
        localStorage.setItem('zhyqUsername', values.username);
        localStorage.setItem('zhyqPassword', values.password);
      }

      const msg = await loginByPasswod({ ...values });
      if (msg?.message === 'success') {
        const data = queryString.parse(msg.data);
        const accessToken = data?.access_token || '';
        localStorage.setItem('jishan_token', accessToken as string);
        if (
          (history.location.state as any)?.from &&
          (history.location.state as any).from.indexOf('login') < 0
        ) {
          window.location.href = (history.location.state as any).from;
        } else {
          window.location.href = '/jsalarm/';
        }
      } else {
        message.error('登录失败，请检查用户名或者密码是否正确!');
        localStorage.removeItem('jishan_token');
      }
      setLoginLoading(false);
    });
  };
  function keydown(e: any) {
    if (e.keyCode === 13) {
      handleSubmit();
    }
  }

  useEffect(() => {
    window.addEventListener('keydown', keydown);
    const username = localStorage.getItem('zhyqUsername');
    const password = localStorage.getItem('zhyqPassword');

    if (username && password) {
      form.setFieldsValue({
        username,
        password,
        remember: true,
      });
    }
  }, []);

  return (
    <div className="login-page">
      <div className="login-header">
        <img className="logo" alt="logo" src={logo} />
        <img className="logoTitle" alt="logotitle" src={logoTitle} />
      </div>
      <div className="login-box">
        <img className="login-box-img" alt="boxImg" src={boxImg} />
        <div className="login-main">
          <div className="divider-box">
            <span className="span1">登录</span>
            <span className="span2">/</span>
            <span className="span2">LOGIN</span>
          </div>
          <div className="login-form">
            <Form form={form}>
              <FormItem name="userName" rules={[{ required: true, message: '请输入用户名!' }]}>
                <Input
                  className="customInput"
                  placeholder="请输入用户名"
                  prefix={<img className="prefiximg" alt="user" src={user} />}
                />
              </FormItem>
              <FormItem name="password" rules={[{ required: true, message: '请输入密码!' }]}>
                <Input
                  className="customInput"
                  placeholder="请输入密码"
                  prefix={<img className="prefiximg" alt="passwd" src={passwd} />}
                  type="password"
                />
              </FormItem>
            </Form>

            <Button className="loginBtn" loading={loginLoading} onClick={handleSubmit}>
              登录
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
