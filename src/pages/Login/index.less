.login-page {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  overflow: hidden;
  // flex-direction: row-reverse;
  background: url('../../assets/images/login/bg.png') no-repeat;
  background-size: 100% 100%;

  .login-header {
    position: absolute;
    top: 0;
    left: 8%;
    display: flex;
    align-items: center;
    .logo {
      width: 142px;
      height: 168px;
      margin-top: -5px;
    }
    .logoTitle {
      width: 320px;
      height: 64px;
      margin-left: 20px;
    }
  }

  .login-box {
    display: flex;
    width: 1200px;
    height: 700px;
    padding: 10px;
    background: #fff;
    border-radius: 10px;
    // width: 40%;
    // min-width: 970px;
    // padding-right: 10%;
    // display: flex;
    // align-items: center;

    .login-box-img {
      width: 700px;
      height: 680px;
    }

    .login-main {
      width: calc(100% - 700px);
      padding: 90px 65px;

      .login-title {
        color: #000000;
        font-weight: 600;
        font-size: 96px;
        font-family: FZHZGBJW--GB1-0;
        letter-spacing: 0;
        text-align: center;
        text-shadow: 0 1px 2px rgba(49, 103, 213, 0.4);
        background-image: -webkit-linear-gradient(bottom, #3167d5, #5fa0ec);
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .divider-box {
        .span1 {
          position: relative;
          margin-right: 3px;
          color: #333;
          font-weight: 400;
          font-size: 42px;
          font-family: PingFangSC-Regular;
          line-height: 59px;

          &::after {
            position: absolute;
            bottom: -7px;
            left: 0;
            width: 80px;
            height: 6px;
            background: #f7b500;
            content: '';
          }
        }
        .span2 {
          margin: 0 3px;
          color: rgba(153, 153, 153, 0.4);
          font-weight: 600;
          font-size: 24px;
          font-family: PingFangSC-Semibold;
          line-height: 59px;
        }
      }

      .login-form {
        margin: 60px auto 0 auto;

        .ant-input-affix-wrapper,
        .ant-select-selector {
          padding: 18px 18px;
          font-size: 18px;
          background: rgba(49, 103, 213, 0.05);
          border: none;
          border-radius: 4px;

          .ant-input-prefix {
            margin-right: 14px;
          }

          .ant-input,
          .ant-select-selection-wrap {
            padding-left: 14px;
            background: transparent;
            border-left: 1px #3167d5 solid;
            border-radius: 0;
          }
        }

        .ant-checkbox-wrapper {
          color: #94a099;
        }

        .loginBtn {
          width: 100%;
          margin-top: 80px;
        }

        .ant-btn {
          height: 52px;
          padding: 0;
          color: #fff;
          font-weight: bold;
          font-size: 20px;
          line-height: 52px;
          background: #3164f6;
          border: 1px #3164f6 solid;
          border-radius: 4px;
        }

        .ctrl-wrapper {
          display: flex;
          align-items: center;
          justify-content: space-between;
          color: #94a099;
          line-height: 20px;
          letter-spacing: 0;
        }
      }

      .tips-container {
        display: flex;
        align-items: center;
        padding: 8px 4px;
        background: #f3f8fe;
        border-radius: 4px;

        .image {
          margin-right: 10px;
        }

        .tips-title {
          padding-bottom: 8px;
          color: #3167d5;
          letter-spacing: 0;
        }

        .tips-content {
          color: #999999;
          line-height: 24px;
          letter-spacing: 0;

          .highlight {
            color: #3167d5;
          }
        }
      }
    }
  }
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  transition: background-color 5000s ease-in-out 0s;
  -webkit-text-fill-color: #000 !important; /* 如果需要，还可以改变文字颜色 */
}
