import { listDigitalAppBaseInfo, listDigitalAppBaseInfoUn } from '@/services/home/<USER>';
import { DoubleRightOutlined } from '@ant-design/icons';
import { Carousel, message } from 'antd';
import React, { useEffect, useState } from 'react';
import yinying from '../../../assets/images/home/<USER>';
import './index.less';

const SystemList: React.FC<any> = (props) => {
  const [sysList, setSysList] = useState([]);

  const getList = () => {
    const token = localStorage.getItem('jishan_token');
    if (token) {
      listDigitalAppBaseInfo().then((res: any) => {
        if (res.code === '200') {
          const list = res.data || [];
          setSysList(list);
        }
      });
    } else {
      listDigitalAppBaseInfoUn().then((res: any) => {
        if (res.code === '200') {
          const list = res.data || [];
          setSysList(list);
        }
      });
    }
  };

  // const getUserCollect = () => {
  //   saveDigitalAppRelByMe().then((res: any) => {
  //     console.log(res);
  //     if (res.code === '200') {
  //       const list = res.data || [];
  //       if (list.length > 0) {
  //         setSysList(list.slice(0, 4));
  //       } else {
  //         getList();
  //       }
  //     }
  //   });
  // };

  useEffect(() => {
    // if ((window as any)?.userInfo?.displayName) {
    //   getUserCollect();
    // } else {
    //   getList();
    // }
    getList();
  }, []);

  const handleClick = (item: any) => {
    const { url } = item;
    if (url) {
      const symbol = url?.indexOf('?') > -1 ? '&' : '?';
      const token = localStorage.getItem('jishan_token');
      window.open(`${url}${token ? `${symbol}token=${token}` : ''}`, '_blank');
    } else {
      message.warning('暂无链接');
    }
  };

  return (
    <div className="home-systemList">
      <Carousel>
        {new Array(Math.ceil(sysList?.length / 4)).fill(0)?.map((_, i) => (
          <div>
            <div className="system-list">
              {sysList?.slice(i * 4, (i + 1) * 4).map((item: any, index: number) => (
                <div key={index} className="system-list-item" onClick={() => handleClick(item)}>
                  <div className={`item-box item-${index + 1}`}>
                    <div className="item-title ellipsis" title={item.appName}>
                      {item.appName}
                    </div>
                    <div className="item-desc ellipsis" title={item.appDesc}>
                      {item.appDesc}
                    </div>
                    <div className="item-img">
                      <DoubleRightOutlined />
                    </div>
                  </div>
                  <img src={yinying} alt="" className="yinying-img" />
                </div>
              ))}
            </div>
          </div>
        ))}
      </Carousel>
    </div>
  );
};

export default SystemList;
