.home-systemList {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  background: url('../../../assets/images/home/<USER>') no-repeat;
  background-size: 100% 100%;

  .ant-carousel {
    flex: 1;
    max-width: 100%;

    .slick-dots {
      li {
        width: 20px;
        margin: 0 4px;

        button {
          height: 6px;
          border-radius: 0;
          opacity: 0.75;

          &:hover {
            opacity: 1;
          }

          &::before {
            display: none;
          }
        }

        &.slick-active {
          width: 40px;

          button {
            background: #3164f6;
          }
        }
      }
    }
  }

  .system-list {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;

    .system-list-item {
      position: relative;
      display: inline-block;
      margin: 0 8px;

      .item-box {
        position: relative;
        z-index: 5;
        width: 240px;
        height: 120px;
        padding: 24px 16px 16px 16px;
        border-radius: 4px;
        cursor: pointer;

        &.item-1 {
          background: #0f68cd;
        }
        &.item-2 {
          background: #0fcd9d;
        }
        &.item-3 {
          background: #48c1e6;
        }
        &.item-4 {
          background: #ffaf00;
        }
        &.item-5 {
          background: #138ac0;
        }

        .item-title {
          margin-bottom: 8px;
          color: #fffff4;
          font-weight: 500;
          font-size: 24px;
          font-family: Alibaba-PuHuiTi-M;
          line-height: 33px;
        }

        .item-desc {
          margin-bottom: 5px;
          color: #fffff4;
          font-weight: 400;
          font-size: 16px;
          font-family: Alibaba-PuHuiTi-R;
          line-height: 22px;
        }

        .item-img {
          text-align: right;

          .anticon {
            color: rgba(216, 216, 216, 0.9);
            font-size: 12px;
          }
        }
      }

      .yinying-img {
        position: absolute;
        bottom: -30px;
        z-index: 1;
      }
    }
  }

  .ellipsis {
    /* 隐藏超出容器的文本 */
    overflow: hidden;
    /* 确保文本不会换行 */
    white-space: nowrap;
    /* 当文本溢出时，显示省略号 */
    text-overflow: ellipsis;
  }
}
