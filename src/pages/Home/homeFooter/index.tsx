import { getEventCodeDict } from '@/services';
import { listPageUrlInfo, listUnitManage2 } from '@/services/home/<USER>';
import { CaretUpOutlined } from '@ant-design/icons';
import { Dropdown, Space } from 'antd';
import React, { useEffect, useState } from 'react';
import logoLetter from '../../../assets/images/home/<USER>';
import tu1 from '../../../assets/images/home/<USER>';
import tu2 from '../../../assets/images/home/<USER>';
import './index.less';

const Home: React.FC<any> = (props) => {
  const [urlGroupList, setUrlGroupList] = useState<any>([]);
  const [footDatas, setFootDatas] = useState<any>({});
  const [footInfo, setFootInfo] = useState<any>([]);

  const getDictData = async () => {
    const res = await getEventCodeDict({ catCode: 'url_group' });
    if (res.code === '200') {
      setUrlGroupList(res.data || []);
    }
  };

  const getFooterMenus = async () => {
    const res = await listPageUrlInfo();
    if (res.code === '200') {
      setFootDatas(res.data || {});
    }
  };

  const getFootInfo = async () => {
    const res = await listUnitManage2({ searchKey: '河北水务有限公司' });
    if (res.code === '200') {
      const list = res.data.data || [];
      if (list.length > 0) {
        const info = list[0]?.remark || '';
        const pList = info.split('%%') || [];
        const infoList: any = [];
        pList.forEach((element: string) => {
          const spanList = element.split('+');
          infoList.push(spanList);
        });
        setFootInfo(infoList);
      }
    }
  };

  useEffect(() => {
    getDictData();
    getFootInfo();
  }, []);

  useEffect(() => {
    getFooterMenus();
  }, [urlGroupList]);

  const footItems: any = (code: string) => {
    const list = footDatas[code] || [];
    return list.map((item: any) => {
      return {
        label: (
          <a href={item.urlAddr} target="_blank" rel="noopener noreferrer">
            {item.urlName}
          </a>
        ),
        key: item.urlId,
      };
    });
  };

  return (
    <div className="home-footer">
      <div className="footer-top">
        <div className="top-cont">
          {urlGroupList.map((item: any) => {
            return (
              <Dropdown
                key={item.catId}
                menu={{ items: footItems(item.code) }}
                trigger={['click']}
                placement="top"
              >
                <a onClick={(e) => e.preventDefault()}>
                  <Space>
                    {item.codeName}
                    <CaretUpOutlined />
                  </Space>
                </a>
              </Dropdown>
            );
          })}
        </div>
      </div>
      <div className="footer-main">
        <div className="left">
          <img className="logo-letter" src={logoLetter} alt="logo" />
          {footInfo.map((item: any, index: number) => {
            return (
              <p key={index}>
                {item.map((sp: any, ind: number) => {
                  return <span key={ind}>{sp}</span>;
                })}
              </p>
            );
          })}
        </div>
        <div className="right">
          <img src={tu1} alt="" />
          <img src={tu2} alt="" />
        </div>
      </div>
    </div>
  );
};

export default Home;
