.home-footer {
  height: 364px;
  background: url('../../../assets/images/home/<USER>') no-repeat bottom center;
  background-size: 100% 100%;

  .footer-top {
    position: relative;
    display: flex;
    align-items: center;
    height: 68px;
    background: url('../../../assets/images/home/<USER>') no-repeat top center;

    &::before {
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      z-index: 1;
      display: block;
      width: 100%;
      height: 38px;
      background: #edf7ff;
      content: ' ';
    }

    .top-cont {
      position: relative;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 1100px;
      margin: 0 auto;

      .ant-space-item {
        color: #666666;
        font-weight: 400;
        font-size: 16px;
        font-family: Alibaba-PuHuiTi-R;
        cursor: default;

        .anticon {
          color: #a5c3dc;
        }
      }
    }
  }

  .footer-main {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 1260px;
    margin: 0 auto;
    padding: 40px 40px 0 0;

    .left {
      display: flex;
      flex-direction: column;

      .logo-letter {
        width: 200px;
        height: 40px;
        margin-bottom: 40px;
      }

      p {
        margin-bottom: 15px;
        color: #ffffff;
        font-weight: 400;
        font-size: 14px;
        font-family: Alibaba-PuHuiTi-R;

        span {
          margin-right: 60px;
        }
      }
    }

    .right {
      img {
        margin-left: 10px;
      }
    }
  }
}
