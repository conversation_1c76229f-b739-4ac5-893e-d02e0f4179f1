import { listPissTextInfo } from '@/services/home/<USER>';
import { history } from '@umijs/max';
import { Carousel } from 'antd';
import React, { useEffect, useState } from 'react';
import morentupian from '../../../assets/images/morentupian.png';
import './index.less';

const Home: React.FC<any> = (props) => {
  const [list, setList] = useState<any[]>([]);
  const moduleId = '10';

  const getList = () => {
    listPissTextInfo({ pageNo: 1, pageSize: 1, moduleId, isPublished: 1 }).then((res: any) => {
      const list = res.data.data || [];
      list.forEach((element: any) => {
        const tgpUrlList: any[] = [];
        if (element.tgpUrls) {
          const tgpUrls = element.tgpUrls.split(',');
          tgpUrls.forEach((tgp: any) => {
            tgpUrlList.push(`${PREFIX_FILE}${PREFIX_SERVER}${tgp}`);
          });
        } else {
          tgpUrlList.push(morentupian);
        }

        element.tgpUrlList = tgpUrlList;
      });
      setList(list);
    });
  };

  useEffect(() => {
    getList();
  }, []);

  return (
    <div className="home-banner">
      <Carousel>
        {list.map((item) => {
          return item.tgpUrlList.map((tgp: any, index: number) => {
            return (
              <div
                key={`${item.textId}-${index}`}
                className="banner-item"
                onClick={() => history.push(`/columnDetail?mid=${moduleId}&tid=${item.textId}`)}
              >
                <img src={tgp} alt="" />
              </div>
            );
          });
        })}
      </Carousel>
    </div>
  );
};

export default Home;
