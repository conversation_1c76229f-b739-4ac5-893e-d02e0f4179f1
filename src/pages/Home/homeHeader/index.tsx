import { Link, history } from '@umijs/max';
import { message } from 'antd';
import React from 'react';
import shoucang from '../../../assets/images/home/<USER>';
import shouye from '../../../assets/images/home/<USER>';
import tuichu from '../../../assets/images/home/<USER>';
import zhanghu from '../../../assets/images/home/<USER>';
import logo from '../../../assets/images/home/<USER>';
import logoLetter from '../../../assets/images/home/<USER>';
import logoLetterLan from '../../../assets/images/home/<USER>';
import './index.less';

const HomeHeader: React.FC<any> = (props) => {
  const { from = 'home' } = props;

  const loginOut = async () => {
    localStorage.removeItem('jishan_token');
    // window.location.href = `/${BASE}/login`;
    history.push('/login', { from: location.pathname });
  };

  const goHome = () => {
    window.location.href = `/${BASE}/home`;
  };

  const addFavorite = () => {
    const url = window.location;
    const { title } = document;
    const ua = navigator.userAgent.toLowerCase();
    if (ua.indexOf('360se') > -1) {
      message.warning('由于360浏览器功能限制，请按 Ctrl+D 手动收藏！');
    } else if (ua.indexOf('msie 8') > -1) {
      (window as any).external.AddToFavoritesBar(url, title); // IE8
    } else if (document.all) {
      try {
        (window as any).external.addFavorite(url, title);
      } catch (e) {
        message.warning('您的浏览器不支持,请按 Ctrl+D 手动收藏!');
      }
    } else if ((window as any).sidebar) {
      (window as any).sidebar.addPanel(title, url, '');
    } else {
      message.warning('您的浏览器不支持,请按 Ctrl+D 手动收藏!');
    }
  };

  const setHome = (obj: any, url: any) => {
    console.log('setHome==', obj);
    try {
      obj.style.behavior = 'url(#default#homepage)';
      obj.setHomePage(url);
    } catch (e) {
      if ((window as any).netscape) {
        try {
          (window as any).netscape.security.PrivilegeManager.enablePrivilege('UniversalXPConnect');
        } catch (e) {
          message.warning(
            "抱歉，此操作被浏览器拒绝！\n\n请在浏览器地址栏输入“about:config”并回车然后将[signed.applets.codebase_principal_support]设置为'true'",
          );
        }
      } else {
        message.warning(
          `抱歉，您所使用的浏览器无法完成此操作。\n\n您需要手动将【${url}】设置为首页。`,
        );
      }
    }
  };

  return (
    <div className="home-header-inner">
      <div className="home-logo" onClick={() => goHome()}>
        <img src={logo} alt="logo" />
        <img
          src={from === 'column' ? logoLetterLan : logoLetter}
          alt="logo"
          style={{ marginLeft: 20 }}
        />
      </div>
      <div className="home-menu">
        <div
          className={`home-menu-item ${from === 'home' ? 'active' : ''}`}
          onClick={() => history.push('/home-index')}
        >
          首页
        </div>
        {(window as any)?.userInfo?.displayName && (
          <Link to="/workbench" target="_blank" className="home-menu-item">
            个人工作台
          </Link>
        )}
        {(window as any)?.userInfo?.displayName && (
          <Link to="/msgPublished" target="_blank" className="home-menu-item">
            系统维护
          </Link>
        )}
      </div>
      <div className="home-right">
        <div className="user-box">
          <div className="icon-box">
            <img src={zhanghu} alt="" />
          </div>
          {(window as any)?.userInfo?.displayName && (
            <span className="useName">{(window as any)?.userInfo?.displayName}</span>
          )}
          {!(window as any)?.userInfo?.displayName && (
            <span className="useName" style={{ cursor: 'pointer' }} onClick={() => loginOut()}>
              登录
            </span>
          )}
          <img className="tuichu" src={tuichu} alt="" onClick={() => loginOut()} />
        </div>

        <div className="others">
          <img src={shouye} alt="" onClick={(event) => setHome(event, window.location)} />
          <img src={shoucang} alt="" onClick={() => addFavorite()} />
        </div>
      </div>
    </div>
  );
};

export default HomeHeader;
