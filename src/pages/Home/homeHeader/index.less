.home-header-inner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: -6px;

  .home-logo {
    display: flex;
    align-items: center;
  }

  .home-menu {
    display: flex;
    align-items: center;

    .home-menu-item {
      width: 120px;
      height: 32px;
      margin-right: 30px;
      color: #407add;
      font-weight: 500;
      font-size: 18px;
      font-family: Alibaba-PuHuiTi-M;
      line-height: 32px;
      text-align: center;
      text-decoration: none;
      border: 1px solid rgba(64, 122, 221, 1);
      border-radius: 8px;
      cursor: pointer;

      &.active {
        color: #fff;
        background: #407add;
      }
    }
  }

  .home-right {
    display: flex;

    .user-box {
      display: flex;
      align-items: center;

      .icon-box {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        background: #fff;
        border-radius: 50%;
      }

      .useName {
        margin-left: 10px;
        color: #407add;
        font-weight: 500;
        font-size: 20px;
        font-family: Ali<PERSON>ba-PuHuiTi-M;
      }

      .tuichu {
        margin-left: 10px;
        cursor: pointer;
      }
    }

    .others {
      margin-left: 50px;

      img {
        margin-left: 15px;
        cursor: pointer;
      }
    }
  }
}
