.home-portal {
  background: url('../../../assets/images/home/<USER>') no-repeat bottom center;
  background-size: 100% auto;

  .home-portal-wrapper {
    min-height: 1500px;
    padding-bottom: 120px;
    background: url('../../../assets/images/home/<USER>') no-repeat top center;
    background-size: 100% auto;

    .home-portal-container {
      width: 1340px;
      margin: 0 auto;

      .react-grid-item {
        overflow-y: hidden;
      }
    }
  }
}
