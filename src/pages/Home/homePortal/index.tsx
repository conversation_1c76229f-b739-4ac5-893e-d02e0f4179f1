/* eslint-disable */
import PortalLayoutDisplay from '@/pages/SystemMaintenance/PortalLayoutDisplay';
import React from 'react';
import '../../SystemMaintenance/PortalLayout/reactGridLayout.less';
import './index.less';

const Home: React.FC<any> = (props) => {
  const moduleTypeCompomentMap: any = {
    module_1: 'pictureAndArticles',
    module_2: 'articlesList',
    module_3: 'graphicCards',
    module_5: 'pictureLinkLeft',
    module_6: 'pictureLinkRight',
    module_4: 'pictureList',
  };

  return (
    <div className="home-portal">
      <div className="home-portal-wrapper">
        <div className="home-portal-container">
          <PortalLayoutDisplay />
        </div>
      </div>
    </div>
  );
};

export default Home;
