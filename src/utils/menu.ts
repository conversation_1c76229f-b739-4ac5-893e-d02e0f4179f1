/* eslint-disable global-require */
/* eslint-disable import/no-dynamic-require */

export const dynamicLoadComponent = (component: any) => {
  if (!component) {
    return require('../pages/404').default;
  }
  try {
    const ass = require(`../pages/${component}/index`);
    return ass.default;
  } catch (error) {
    return require('../pages/404').default;
  }
};

export const dealFlatMenu = (data: any[]): any[] => {
  let flatArr: any[] = [];
  data?.forEach((item) => {
    if (item.path) {
      flatArr.push({
        ...item,
        path: item.path,
      });
    }
    if (item.children && item.children.length > 0) {
      flatArr = [...flatArr, ...dealFlatMenu(item.children)];
    }
  });
  return flatArr;
};
