/**
 * 处理下载文件
 * @param filePath 路径
 * @returns
 */
export function downloadFile(filePath: any) {
  let url = filePath;
  if (!filePath) {
    return '';
  }
  if (!filePath.startsWith('http')) {
    url = new URL(`${location.origin}${filePath}`);
  }
  const urlParams = new URLSearchParams(url.searchParams);
  // const fileName = urlParams.get('fileName');
  const objectName = urlParams.get('objectName');
  try {
    return `${PREFIX_FILE}${PREFIX_SERVER}${filePath}&fullfilename=${objectName}`;
  } catch (error) {
    console.error('Invalid  URL  format', error);
    return '';
  }
}

// fileType: P: 图片，V: 视频，F：文件
export function formatFiles(list: any[], fileType?: string) {
  const newList = list.map((item) => {
    if (item.response) {
      return { ...item.response?.data };
    }
    const url = item.atchPath;
    const urlParams = new URLSearchParams(url);
    const objectName = urlParams.get('objectName');
    return {
      atchName: item.atchName,
      bucket: 'hbnsbd',
      downloadUrl: item.atchPath,
      savePath: objectName,
      atchSize: item.atchSize,
      atchType: item.atchType,
    };
  });
  return newList;
}

/**
 * 处理文件预览
 * @param value 文件列表
 * @returns
 */
export function initFileList(value: any[]) {
  const fileUrlList = value?.map((item: any) => {
    const url = item.atchPath;
    const urlParams = new URLSearchParams(url);
    const objectName = urlParams.get('objectName');
    return {
      ...item,
      uid: objectName,
      name: item.atchName,
      status: 'done',
      url: downloadFile(url),
      filePath: url,
    };
  });
  return fileUrlList;
}

export function initFile(path: string, name: string) {
  return {
    uid: name,
    name,
    status: 'done',
    url: downloadFile(path),
    filePath: path,
  };
}

/**
 *  通过blob下载
 * @param res blob数据
 * @param rename 重命名
 */
export function downloadByBlob(res: any, rename?: string) {
  const reader = new FileReader();
  reader.readAsArrayBuffer(res);
  reader.onload = (e) => {
    const results = e?.target?.result || '';
    const blob = new Blob([results], { type: res.type });
    const url = URL.createObjectURL(blob);
    //  将文件下载下来
    const link = document.createElement('a');
    link.href = url;
    if (rename) {
      link.setAttribute('download', rename);
    }
    link.style.display = 'none';
    document.getElementsByTagName('link');
    link.click();
  };
}

/**
 * 获取文件类型
 * @param fileName 文件名 优先级高于 filePath
 * @param filePath 文件路径 与filePath二者传其一
 * @returns string
 */
export function getFileType(fileName?: string, filePath?: string) {
  let name = fileName;
  if (!name && filePath) {
    const urlParams = new URLSearchParams(filePath);
    const newName = urlParams.get('fileName');
    if (newName) {
      name = newName;
    }
  }
  if (name) {
    const extension = name.split('.').pop();
    if (extension) {
      return extension.toLowerCase();
    }
  }
  return 'unknown';
}
