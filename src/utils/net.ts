// @ts-ignore
import { request as UmiRequest } from 'umi';

type ApiResponse<T> = {
  code: string;
  action: string;
  msg: string;
  data: T;
};

export const withErrorHandling = <T>(apiCall: () => Promise<ApiResponse<T>>) => {
  return async (): Promise<T | null> => {
    try {
      const response = await apiCall();
      if (response.code !== '200') {
        console.error('API Error:', response.msg);
        return null;
      }
      return response.data;
    } catch (error) {
      console.error('Unexpected Error:', error);
      return null;
    }
  };
};

export class request {
  static get(url: string, params?: any) {
    return UmiRequest(url, {
      method: 'GET',
      params,
    });
  }

  static post(url: string, data?: any) {
    return UmiRequest(url, {
      method: 'POST',
      data,
    });
  }
}
