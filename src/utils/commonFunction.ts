/* eslint-disable global-require */
/* eslint-disable import/no-dynamic-require */
// import moment from 'moment';
import { message } from 'antd';
import dayjs from 'dayjs';

export function getPicUrl(path?: string, type?: string) {
  if (!path) return '';
  let imgUrl = '';
  try {
    imgUrl = require(`@/assets/images/${path}.${type || 'png'}`);
    return imgUrl;
  } catch (e) {
    console.log(e);
    return '';
  }
}

export const getOptionsFromDict = (dicts: any[]) => {
  if (dicts.length > 0) {
    return dicts.map((item) => ({
      label: item.dictValue,
      value: item.dictCode,
    }));
  }
  return [];
};

// 生成 code 到 label 的映射
export const createCodeLabelMap = (options: { value: string; label: string }[]) => {
  return options.reduce((acc, option) => {
    acc[option.value] = option.label;
    return acc;
  }, {} as Record<string, string>);
};

export const fetchOptions = async (
  fetchFunction: () => Promise<any>,
  setOptions: (options: any[]) => void,
  labelKey: string,
  valueKey: string,
  callback?: (data: any[]) => void,
) => {
  try {
    const res = await fetchFunction();
    if (res.code === '200') {
      const {
        data: { data },
      } = res;
      if (Array.isArray(data)) {
        const options = data.map((item) => ({
          label: item[labelKey],
          value: item[valueKey],
        }));
        setOptions(options);
        if (callback) callback(data);
      }
    } else {
      message.error(res.msg);
    }
  } catch (error) {
    message.error('获取数据失败');
  }
};

export const setFormDataFromFileUrl = (fileUrl: string) => {
  if (!fileUrl) {
    return { fileUrl: [] };
  }
  // 提取 fileName
  const urlParams = new URLSearchParams(fileUrl.split('?')[1]);
  const fileName = urlParams.get('fileName') || '';
  return {
    name: fileName,
    downloadUrl: fileUrl,
  };
};

export const getRecordInitValue = (record: any, keyMap: string[]) => {
  // 提取 record 中的字段
  return keyMap.reduce((acc, key) => {
    if (record.hasOwnProperty(key)) {
      acc[key] = record[key] || '';
    }
    return acc;
  }, {} as Record<string, any>);
};

/**
 * 将数组转换成tree数据结构
 * @param data 待处理数组
 * @param childId 自定义childid
 * @param parentId 自定义父级id
 * @returns
 */
export function listToTree(data: any[], childId: string, parentId: string) {
  // * 先生成parent建立父子关系
  const obj: any = {};
  data?.forEach((item) => {
    obj[item?.[childId]] = item;
  });

  const parentList: any[] = [];
  data?.forEach((item) => {
    const parent = obj[item?.[parentId]];
    if (parent) {
      // * 当前项有父节点
      parent.children = parent?.children || [];
      parent.children.push(item);
    } else {
      // * 当前项没有父节点 -> 顶层
      parentList.push(item);
    }
  });
  return parentList;
}

/**
 * 简单求和
 * @param list
 * @returns
 */
export function getTotal(list: any, field?: string) {
  return list?.reduce((cur: any, next: any) => {
    let value = Number(cur);
    value += field ? Number(next[field] || 0) : Number(next.value || 0);
    return value;
  }, 0);
}

/**
 * 计算某个时间和当前时间的差异，并根据这个差异显示“多少天前”、“多少月前”或“多少年前”
 * @param createTime '2023-01-01'
 * @returns
 */
export function diffTime(createTime: string) {
  const now = dayjs(); // 获取当前时间
  const created = dayjs(createTime); // 将传入的创建时间转换为 Moment 对象
  const daysDiff = now.diff(created, 'days'); // 计算与当前时间的天数差
  const monthsDiff = now.diff(created, 'months'); // 计算与当前时间的月数差
  const yearsDiff = now.diff(created, 'years'); // 计算与当前时间的年数差

  if (daysDiff >= 30 && daysDiff < 365) {
    return `${monthsDiff} 月前`;
  }
  if (daysDiff >= 365) {
    return `${yearsDiff} 年前`;
  }
  return `${daysDiff} 天前`;
}

// 获取嵌套值（支持 item.subItem.field 格式）
export function getNestedValue(obj: any, path: string) {
  return path.split('.').reduce((o: { [x: string]: any }, p: string | number) => o?.[p], obj);
}

// 映射样式
export function mapItemStyle(
  item: { [x: string]: string | number },
  styleConfig: { [s: string]: unknown } | ArrayLike<unknown>,
) {
  return Object.entries(styleConfig).reduce((acc: any, [key, val]: [any, any]) => {
    if (typeof val === 'object' && val.$map) {
      acc[key] = val.colors[item[val.$map]] || val.colors._default;
    }
    return acc;
  }, {});
}

export function mapData(source: any, path: string) {
  // 处理空路径或空数据源
  if (!path || !source) return undefined;

  const segments = path
    .split(/(?:\]\.|\.?$$].?)/g)
    .flatMap((s: string) => s.split('.'))
    .filter(Boolean);

  let current = [source]; // 始终保持数组形式处理

  for (const segment of segments) {
    // 处理数组展开语法 []
    if (segment === '') {
      current = current.flatMap((item) => (Array.isArray(item) ? item : [item]));
    } else if (segment.includes('[')) {
      // 处理带索引的数组 sales[0]
      const [key, index] = segment.split(/\[|$$/g).filter(Boolean);
      current = current.flatMap((obj) => {
        const arr = obj?.[key];
        return index ? [arr?.[Number(index)]] : arr || [];
      });
    } else {
      // 处理普通对象属性
      current = current.flatMap((obj) => {
        const value = obj?.[segment];
        return value !== undefined ? value : [];
      });
    }
  }

  return current;
}

// 处理柱状图、折线图数据
export function generateOptions(
  apiData: Record<string, any>,
  fieldMap: { [s: string]: unknown } | ArrayLike<unknown>,
) {
  const processValue: any = (value: unknown) => {
    if (typeof value === 'string') {
      if (value.includes('.') || value.includes('[') || value.includes(']')) {
        return mapData(apiData, value);
      }
      return value;
    }
    if (Array.isArray(value)) {
      return value.map(processValue);
    }
    if (typeof value === 'object' && value !== null) {
      return Object.fromEntries(
        Object.entries(value).map(([k, v]: [any, any]) => [k, processValue(v)]),
      );
    }
    return value;
  };

  return Object.fromEntries(
    Object.entries(fieldMap).map(([key, value]) => [key, processValue(value)]),
  );
}

// 处理饼图数据
export function mapPieData(apiData: Record<string, any>, mapConfig: { series: any[] }) {
  const series: any[] = [];

  mapConfig.series.forEach(
    (seriesConfig: { data: { source: any; name: any; value: any; itemStyle: any } }) => {
      const seriesData: { name: any; value: any }[] = [];

      // 获取数据源
      const sourceData = getNestedValue(apiData, seriesConfig.data.source);
      // 遍历数据项
      sourceData.forEach((item: any) => {
        const entry: any = {
          name: getNestedValue(item, seriesConfig.data.name),
          value: getNestedValue(item, seriesConfig.data.value),
        };

        // 处理附加样式
        // if (seriesConfig.data.itemStyle) {
        //   entry.itemStyle = mapItemStyle(item, seriesConfig.data.itemStyle);
        // }

        seriesData.push(entry);
      });

      series.push({
        type: 'pie',
        ...seriesConfig,
        data: seriesData,
      });
    },
  );

  return { ...mapConfig, series };
}

/** 根据路径获取对象中的值 */
export function getValueByPath(obj: any, path: string): any {
  const parts = path.split(/[\[\]\.]/).filter((part) => part.length > 0);
  let current = obj;
  for (const part of parts) {
    if (current && typeof current === 'object' && part in current) {
      current = current[part];
    } else {
      return undefined; // 如果路径不存在，返回 undefined
    }
  }

  return current;
}

/** 根据路径设置对象中的值 */
export function setValueByPath(obj: any, path: string, value: any): void {
  const parts = path.split(/[\[\]\.]/).filter((part) => part.length > 0);
  let current = obj;

  for (let i = 0; i < parts.length - 1; i += 1) {
    const part = parts[i];
    if (current && typeof current === 'object' && part in current) {
      current = current[part];
    } else {
      return; // 如果路径不存在，直接返回
    }
  }

  const lastPart = parts[parts.length - 1];
  if (current && typeof current === 'object') {
    current[lastPart] = value;
  }
}

export async function exportFile(formValues: any, url: string, title?: string) {
  const queryString = new URLSearchParams(formValues).toString();
  const fullUrl = `${url}?${queryString}`;
  try {
    const response = await fetch(fullUrl, {
      method: 'GET',
    });
    if (response.ok) {
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${title || 'export_data'}.xlsx`;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
      message.success('导出成功');
    } else {
      throw new Error('导出失败');
    }
  } catch (error) {
    message.error('导出失败');
  }
}

export function getCurrentFormattedTime() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
  const day = String(now.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

export const commonPlaceHolder = (label: string) => `请输入${label}`;

export const requiredRule = (message: string) => ({
  required: true,
  message,
});
