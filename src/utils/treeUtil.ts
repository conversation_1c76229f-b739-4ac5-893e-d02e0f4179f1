/** 获取叶子节点, 并将全部叶子节点挂在根节点下的leafNodes属性下 */
export function getLeafNodes(data: any[], parentId: string, idKey: string = 'id') {
  const obj: any = {};
  const rootNodes: any[] = [];
  const leafNodes: any[] = [];

  // 构建树结构
  data?.forEach((item) => {
    obj[item[idKey]] = { ...item, children: [] };
    if (!item[parentId]) {
      rootNodes.push(obj[item[idKey]]);
    }
  });

  // 填充子节点
  data?.forEach((item) => {
    if (item[parentId]) {
      const parent = obj[item[parentId]];
      if (parent) {
        parent.children.push(obj[item[idKey]]);
      }
    }
  });

  // 识别叶子节点
  const findLeafNodes = (node: any) => {
    if (node.children.length === 0) {
      leafNodes.push(node);
    } else {
      node.children.forEach((child: any) => findLeafNodes(child));
    }
  };

  rootNodes.forEach((rootNode) => findLeafNodes(rootNode));

  // 将叶子节点挂在根节点下
  rootNodes.forEach((rootNode) => {
    rootNode.leafNodes = leafNodes;
  });

  return rootNodes;
}

/** 将树全部节点转换为列表 */
export function treeToList(data: any[], parentId: string) {
  const list: any[] = [];
  data?.forEach((item) => {
    list.push(item);
    if (item[parentId]) {
      list.push(...treeToList(item.children, parentId));
    }
  });
  return list;
}
