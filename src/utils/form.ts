export const codeRules = { pattern: /^[a-zA-Z0-9_]+$/, message: '编码只能包含数字、字母和下划线' };

export const nameLengthRules = { max: 50, message: '名称不能超过50个字符' };

export const codeLengthRules = { max: 20, message: '编码不能超过20个字符' };

export const textAreaLengthRules = { max: 300, message: '不能超过300个字符' };

export const phoneRules = { pattern: /^\d+$/, message: '联系电话只能包含数字' };

export const phoneLengthRules = { min: 1, max: 15, message: '电话号码长度不超过15位' };

export const emailRules = { type: 'email', message: '请输入有效的邮箱地址!' };
