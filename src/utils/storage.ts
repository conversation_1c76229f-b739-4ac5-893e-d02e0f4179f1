interface StorageItemProps {
  name: string;
  value: any;
  expires?: number; // 单位:h，默认8h过期，传-1永不过期
}

export class CustomStorage {
  constructor() {}

  // 设置缓存
  setItem(params: StorageItemProps) {
    const obj = {
      ...params,
      startTime: 0,
    };
    if (params.expires !== -1) {
      obj.startTime = new Date().getTime();
    }
    localStorage.setItem(params.name, JSON.stringify(obj));
  }

  // 获取缓存
  getItem(name: string) {
    const item: StorageItemProps & { startTime?: number } = JSON.parse(
      localStorage.getItem(name) || '{}',
    );
    if (item.startTime && item.expires !== -1) {
      const time = new Date().getTime() - item.startTime;
      if (time > (item.expires || 8) * 60 * 60 * 1000) {
        this.removeItem(name);
        return false;
      }
    }
    return item.value;
  }

  // 移除缓存
  removeItem(key: string) {
    localStorage.removeItem(key);
  }

  // 移除全部缓存
  clear() {
    localStorage.clear();
  }
}
