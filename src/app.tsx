/** eslint-disable */
import { AvatarDropdown, AvatarName, Question, SelectLang } from '@/components';
import { MenuOutlined, UserOutlined } from '@ant-design/icons';
import { type Settings as LayoutSettings } from '@ant-design/pro-components';
import type { RunTimeLayoutConfig } from '@umijs/max';
import { history } from '@umijs/max';
import { App, ConfigProvider, message } from 'antd';
import React from 'react';
import defaultSettings from '../config/defaultSettings';
import BasicLayout from './components/BasicLayout';
import MenuHeaderRender from './components/MenuHeaderRender';
import { errorConfig } from './requestErrorConfig';
import { getUserMenuPermRole } from './services/login';
import './mockServer';

const loginPath = `/${BASE}/login`;
const homePath = `/${BASE}/home-index`;
const columnPath = `/${BASE}/column`;
const columnDetailPath = `/${BASE}/columnDetail`;
const whiteList = [loginPath, homePath, columnPath, columnDetailPath, `/${BASE}`];
let extraRoutes: any;
let userInfo: any;
let flatMenu: any;

/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */
export async function getInitialState(): Promise<{
  settings?: Partial<LayoutSettings>;
  userInfo?: API.CurrentUser;
  loading?: boolean;
  flatMenu?: any;
}> {
  // 如果不是登录页面，执行
  const { location } = history;
  // if (!whiteList.includes(location.pathname)) {
  console.log('非白名单');
  const query = new URLSearchParams(location.search);
  const token = query.get('token');
  if (token) {
    localStorage.setItem('jishan_token', token);
  }
  if (location.pathname !== loginPath && location.pathname !== `/${BASE}`) {
    const hbnsbdToken = localStorage.getItem('jishan_token');
    if (!hbnsbdToken) {
      message.error('登录已失效');
      localStorage.removeItem('jishan_token');
      setTimeout(() => {
        history.push('/login', { from: location.pathname });
      }, 1000);
      return {};
    }
    return {
      userInfo,
      flatMenu,
      settings: defaultSettings as Partial<LayoutSettings>,
    };
  }
  return {
    // userInfo,
    // flatMenu,
    settings: defaultSettings as Partial<LayoutSettings>,
  };
}

// ProLayout 支持的api https://procomponents.ant.design/components/layout
export const layout: RunTimeLayoutConfig = ({ initialState, setInitialState }) => {
  React.useEffect(() => {
    setInitialState({ ...initialState, userInfo, flatMenu });
  }, [userInfo, flatMenu]);
  return {
    actionsRender: () => [<Question key="doc" />, <SelectLang key="SelectLang" />],
    avatarProps: {
      src: <UserOutlined />,
      title: <AvatarName />,
      render: (_, avatarChildren) => {
        return <AvatarDropdown>{avatarChildren}</AvatarDropdown>;
      },
    },
    pure: window.location.pathname === loginPath,
    menu: {
      // 动态渲染菜单
      selectable: true,

      request: async () => {
        const menus = extraRoutes;
        const handleMenus = (menus: any[]): any[] =>
          menus.map((item) => ({
            ...item,
            name: item.menuName,
            path: item.menuType === 'link' ? `/${item.code}` : item.path || `/${item.code}`,
            // exact: true,
            icon: item.menuIcon ? (
              `${FILE_URL}${portalPrefix}/api/file/load/${item.menuIcon}`
            ) : (
              <MenuOutlined />
            ),
            children: item.children ? handleMenus(item.children) : [],
          }));
        const messs = handleMenus(menus);
        const dealFlatMenu = (data: any[], parentMenuKey?: string): any[] => {
          let flatArr: any[] = [];
          data?.forEach((item) => {
            const menuKey = parentMenuKey ? `${parentMenuKey}.${item.code}` : `menu.${item.code}`;
            flatArr.push({
              ...item,
              menuKey,
              path: item?.path || `/${item.code}`,
              // code: item?.path,
            });
            if (item.children && item.children.length > 0) {
              flatArr = [...flatArr, ...dealFlatMenu(item.children, menuKey)];
            }
          });
          return flatArr;
        };
        window.flatMenu = dealFlatMenu(messs);
        flatMenu = messs;
        return messs;
      },
    },
    onPageChange: () => {
      const { location } = history;
      // 如果没有登录，重定向到 login
      if (!initialState?.userInfo && location.pathname !== loginPath) {
        history.push(loginPath);
      }
    },

    menuHeaderRender: undefined,
    headerTitleRender: () => window.location.pathname !== loginPath && <MenuHeaderRender />,
    childrenRender: (children) => {
      return (
        <ConfigProvider
          theme={{
            token: {
              colorBorder: '#d8d8d8',
              colorPrimary: '#3164F6',
              borderRadius: 2,
            },
          }}
        >
          <App>
            <BasicLayout flatMenu={window.flatMenu}>{children}</BasicLayout>
          </App>
        </ConfigProvider>
      );
    },
    ...initialState?.settings,
  };
};

/**
 * @name request 配置，可以配置错误处理
 * 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。
 * @doc https://umijs.org/docs/max/request#配置
 */
export const request = {
  ...errorConfig,
};

export async function render(oldRender: any) {
  const { location } = history;
  const query = new URLSearchParams(location.search);
  const token = query.get('token');
  if (token) {
    localStorage.setItem('jishan_token', token);
  }
  if (localStorage.getItem('jishan_token') || token) {
    const res = await getUserMenuPermRole();
    if (res.isError) {
      localStorage.removeItem('access_token');
      !whiteList.includes(location.pathname) && history.push('/login', { from: location.pathname });
    }
    if (res?.data) {
      (window as any).userInfo = {
        ...res?.data?.user,
      };
      (window as any).userRoles = res?.data?.roles || [];
      (window as any).systems = res?.data?.systems || [];
      extraRoutes = res?.data?.menus || [];
      userInfo = res?.data?.user;
    }
    oldRender();
  } else {
    history.push('/login', { from: location.pathname });
    oldRender();
  }
}
